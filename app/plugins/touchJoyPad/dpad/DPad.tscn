[gd_scene load_steps=3 format=2]

[ext_resource path="res://plugins/touchJoyPad/dpad/buttonPressed.png" type="Texture" id=1]
[ext_resource path="res://plugins/touchJoyPad/dpad/button.png" type="Texture" id=2]

[node name="d-pad" type="Node2D"]

[node name="left" type="TouchScreenButton" parent="."]
position = Vector2( -80, -31 )
normal = ExtResource( 2 )
pressed = ExtResource( 1 )
action = "button_left"

[node name="up" type="TouchScreenButton" parent="."]
position = Vector2( 31, -80 )
rotation = 1.5708
normal = ExtResource( 2 )
pressed = ExtResource( 1 )
action = "ui_up"

[node name="down" type="TouchScreenButton" parent="."]
position = Vector2( -30, 80 )
rotation = 4.71239
normal = ExtResource( 2 )
pressed = ExtResource( 1 )
action = "ui_down"

[node name="right" type="TouchScreenButton" parent="."]
position = Vector2( 80, 31 )
rotation = 3.14159
normal = ExtResource( 2 )
pressed = ExtResource( 1 )
action = "button_right"
