[gd_scene load_steps=10 format=2]

[ext_resource path="res://addons/kenney_particle_pack/16/star_07_16.png" type="Texture" id=1]
[ext_resource path="res://scripts/Menu.gd" type="Script" id=2]
[ext_resource path="res://scenes/Themes/tryu_theme.tres" type="Theme" id=3]

[sub_resource type="Gradient" id=2]
offsets = PoolRealArray( 0, 0.362222, 0.648889, 0.995556 )
colors = PoolColorArray( 1, 0.984314, 0, 0.431373, 1, 1, 1, 1, 0.918945, 1, 0, 0.617544, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=3]
gradient = SubResource( 2 )

[sub_resource type="Curve" id=4]
_data = [ Vector2( 0, 0 ), 0.0, 0.0, 0, 0, Vector2( 1, 0.590909 ), -4.22182, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=5]
curve = SubResource( 4 )

[sub_resource type="ParticlesMaterial" id=6]
lifetime_randomness = 0.2
emission_shape = 2
emission_box_extents = Vector3( 130, 30, 1 )
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 180.0
gravity = Vector3( 0, 10, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
angle = 720.0
angle_random = 1.0
scale = 5.0
scale_random = 1.0
scale_curve = SubResource( 5 )
color_ramp = SubResource( 3 )

[sub_resource type="Theme" id=1]

[node name="Menu" type="Control"]
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource( 2 )

[node name="MainCont" type="VBoxContainer" parent="."]
margin_left = 16.0
margin_top = 32.0
margin_right = 247.0
margin_bottom = 560.0
alignment = 2

[node name="Buy" type="Button" parent="MainCont"]
margin_right = 268.0
margin_bottom = 50.0
focus_neighbour_bottom = NodePath("../Start")
theme = ExtResource( 3 )
text = "BUY NOW!"

[node name="Particles2D" type="Particles2D" parent="MainCont/Buy"]
modulate = Color( 1, 1, 1, 0.619608 )
position = Vector2( 130, 17 )
amount = 50
speed_scale = 1.2
randomness = 1.0
process_material = SubResource( 6 )
texture = ExtResource( 1 )

[node name="Start" type="Button" parent="MainCont"]
margin_top = 54.0
margin_right = 268.0
margin_bottom = 104.0
focus_neighbour_top = NodePath("../Buy")
focus_neighbour_bottom = NodePath("../GameMode")
theme = ExtResource( 3 )
text = "Start"

[node name="StartCont" type="VBoxContainer" parent="MainCont/Start"]
visible = false
margin_left = 278.0
margin_top = -53.0
margin_right = 534.0
margin_bottom = 159.0
alignment = 2

[node name="Easy" type="Button" parent="MainCont/Start/StartCont"]
margin_right = 256.0
margin_bottom = 50.0
focus_neighbour_left = NodePath("../..")
theme = ExtResource( 3 )
text = "Easy"

[node name="Normal" type="Button" parent="MainCont/Start/StartCont"]
margin_top = 54.0
margin_right = 256.0
margin_bottom = 104.0
focus_neighbour_left = NodePath("../..")
theme = ExtResource( 3 )
text = "Normal"

[node name="Hard" type="Button" parent="MainCont/Start/StartCont"]
margin_top = 108.0
margin_right = 256.0
margin_bottom = 158.0
focus_neighbour_left = NodePath("../..")
theme = ExtResource( 3 )
text = "Hard"

[node name="Extreme" type="Button" parent="MainCont/Start/StartCont"]
margin_top = 162.0
margin_right = 256.0
margin_bottom = 212.0
focus_neighbour_left = NodePath("../..")
theme = ExtResource( 3 )
text = "Extreme"

[node name="GameMode" type="Button" parent="MainCont"]
margin_top = 108.0
margin_right = 268.0
margin_bottom = 158.0
focus_neighbour_top = NodePath("../Start")
focus_neighbour_bottom = NodePath("../SelectShip")
theme = ExtResource( 3 )
text = "Campaign"

[node name="SelectShip" type="Button" parent="MainCont"]
margin_top = 162.0
margin_right = 268.0
margin_bottom = 212.0
focus_neighbour_top = NodePath("../GameMode")
focus_neighbour_bottom = NodePath("../Shop")
theme = ExtResource( 3 )
text = "Select Ship"

[node name="Shop" type="Button" parent="MainCont"]
margin_top = 216.0
margin_right = 268.0
margin_bottom = 266.0
focus_neighbour_top = NodePath("../SelectShip")
focus_neighbour_bottom = NodePath("../Options")
theme = ExtResource( 3 )
text = "Upgrade Ship"

[node name="Options" type="Button" parent="MainCont"]
margin_top = 270.0
margin_right = 268.0
margin_bottom = 320.0
focus_neighbour_top = NodePath("../Shop")
focus_neighbour_bottom = NodePath("../Achievments")
theme = ExtResource( 3 )
text = "Options"

[node name="OptionsCont" type="VBoxContainer" parent="MainCont/Options"]
visible = false
margin_left = 280.0
margin_top = -56.0
margin_right = 536.0
margin_bottom = 156.0
theme = SubResource( 1 )
alignment = 2

[node name="Music" type="Button" parent="MainCont/Options/OptionsCont"]
margin_right = 256.0
margin_bottom = 50.0
theme = ExtResource( 3 )
text = "Music ON"

[node name="Sound" type="Button" parent="MainCont/Options/OptionsCont"]
margin_top = 54.0
margin_right = 256.0
margin_bottom = 104.0
theme = ExtResource( 3 )
text = "Sound ON"

[node name="Fullscreen" type="Button" parent="MainCont/Options/OptionsCont"]
margin_top = 108.0
margin_right = 256.0
margin_bottom = 158.0
theme = ExtResource( 3 )
text = "Fullscreen"

[node name="ControlType" type="Button" parent="MainCont/Options/OptionsCont"]
margin_top = 162.0
margin_right = 256.0
margin_bottom = 212.0
theme = ExtResource( 3 )
text = "Keys / Joy"

[node name="Achievments" type="Button" parent="MainCont"]
margin_top = 324.0
margin_right = 268.0
margin_bottom = 374.0
focus_neighbour_top = NodePath("../Options")
focus_neighbour_bottom = NodePath("../Info")
theme = ExtResource( 3 )
text = "Achievements"

[node name="Info" type="Button" parent="MainCont"]
margin_top = 378.0
margin_right = 268.0
margin_bottom = 428.0
focus_neighbour_top = NodePath("../Achievments")
focus_neighbour_bottom = NodePath("../Leaderboard")
theme = ExtResource( 3 )
text = "Help"

[node name="Leaderboard" type="Button" parent="MainCont"]
margin_top = 432.0
margin_right = 268.0
margin_bottom = 482.0
focus_neighbour_top = NodePath("../Info")
focus_neighbour_bottom = NodePath("../Exit")
theme = ExtResource( 3 )
text = "Leaderboard"

[node name="Exit" type="Button" parent="MainCont"]
margin_top = 486.0
margin_right = 268.0
margin_bottom = 536.0
focus_neighbour_top = NodePath("../Leaderboard")
focus_neighbour_bottom = NodePath("../Start")
theme = ExtResource( 3 )
text = "Exit"

[node name="ExitCont" type="VBoxContainer" parent="MainCont/Exit"]
visible = false
margin_left = 280.0
margin_top = -53.0
margin_right = 434.0
margin_bottom = 51.0
alignment = 2

[node name="Ok" type="Button" parent="MainCont/Exit/ExitCont"]
margin_right = 154.0
margin_bottom = 50.0
theme = ExtResource( 3 )
text = "Ok"

[node name="Cancel" type="Button" parent="MainCont/Exit/ExitCont"]
margin_top = 54.0
margin_right = 154.0
margin_bottom = 104.0
theme = ExtResource( 3 )
text = "Cancel"
