[gd_scene load_steps=17 format=2]

[ext_resource path="res://shaders/VHS.gdshader" type="Shader" id=1]
[ext_resource path="res://scripts/TitleScreen.gd" type="Script" id=2]
[ext_resource path="res://scenes/Menu.tscn" type="PackedScene" id=3]
[ext_resource path="res://assets/sounds/tryusoratitle.mp3" type="AudioStream" id=4]
[ext_resource path="res://scenes/PlayerDisplay.tscn" type="PackedScene" id=5]
[ext_resource path="res://assets/bg_images/menu.jpg" type="Texture" id=6]
[ext_resource path="res://assets/Tryu_Logo_1_transparent_small.png" type="Texture" id=7]
[ext_resource path="res://scenes/Themes/TitleScreen.tres" type="DynamicFont" id=8]
[ext_resource path="res://assets/fonts/Commodore Pixelized v1.2.ttf" type="DynamicFontData" id=9]

[sub_resource type="Shader" id=16]
code = "shader_type canvas_item;

uniform float brightness : hint_range(0.001, 2.0) = 1.0;
uniform float anchor : hint_range(-1.0,0.0) = -0.5;
//The point where all the lines come from.
uniform float speed_scale = 1.0;
uniform float fov : hint_range(0.001, 1.0) = 0.2;
uniform float line_count = 1.0;
uniform vec4 background_color : hint_color = vec4(0.0, 0.1, 0.2, 1.0);
uniform vec4 grid_color : hint_color = vec4(1.0, 0.5, 1.0, 1.0);

float grid(vec2 uv, float batt) {
    vec2 size = vec2(uv.y, uv.y * uv.y * 0.2) * 0.01* (batt + 0.05);
    uv += vec2(0.0, TIME * speed_scale);
    uv = abs(fract(uv) - 0.5);
 	vec2 lines = smoothstep(size, vec2(0.0), uv);
 	lines += smoothstep(size * 5.0, vec2(0.0), uv) * 0.4 * batt;
    return lines.x + lines.y;
}
void fragment() {
	vec2 uv = UV;
	vec4 col = background_color;
    uv.y = 3.0 / (abs(uv.y + fov) + 0.05);
	uv.x += anchor;
    uv.x *= uv.y * line_count;
    float gridVal = grid(uv, brightness);
    col = mix(background_color, grid_color, gridVal);
	COLOR = col;
}"

[sub_resource type="ShaderMaterial" id=12]
shader = SubResource( 16 )
shader_param/brightness = 0.867
shader_param/anchor = -0.5
shader_param/speed_scale = 0.7
shader_param/fov = 0.2
shader_param/line_count = 3.0
shader_param/background_color = Color( 0, 0, 0, 0.780392 )
shader_param/grid_color = Color( 0.486275, 0.176471, 0.517647, 1 )

[sub_resource type="ProxyTexture" id=9]

[sub_resource type="ShaderMaterial" id=17]
shader = ExtResource( 1 )
shader_param/overlay = false
shader_param/scanlines_opacity = 0.4
shader_param/scanlines_width = 0.25
shader_param/grille_opacity = 0.3
shader_param/resolution = Vector2( 640, 480 )
shader_param/pixelate = true
shader_param/roll = true
shader_param/roll_speed = 1.0
shader_param/roll_size = 15.0
shader_param/roll_variation = 1.8
shader_param/distort_intensity = 0.05
shader_param/noise_opacity = 0.4
shader_param/noise_speed = 5.0
shader_param/static_noise_intensity = 0.06
shader_param/aberration = 0.03
shader_param/brightness = 2.0
shader_param/discolor = true
shader_param/warp_amount = 0.0
shader_param/clip_warp = true
shader_param/vignette_intensity = 0.4
shader_param/vignette_opacity = 0.0

[sub_resource type="DynamicFontData" id=20]
font_path = "res://assets/fonts/Commodore Pixelized v1.2.ttf"

[sub_resource type="DynamicFont" id=19]
size = 44
font_data = SubResource( 20 )

[sub_resource type="DynamicFont" id=18]
size = 15
outline_size = 2
outline_color = Color( 0, 0, 0, 1 )
use_mipmaps = true
use_filter = true
font_data = ExtResource( 9 )

[node name="TitleScreen" type="Node2D"]
script = ExtResource( 2 )
__meta__ = {
"_edit_vertical_guides_": [ 507.0 ]
}

[node name="BGImage" type="Sprite" parent="."]
modulate = Color( 1, 1, 1, 0.67451 )
position = Vector2( 511.5, 293.5 )
scale = Vector2( 1.00879, 1.0191 )
texture = ExtResource( 6 )

[node name="NeonLines" type="Sprite" parent="."]
material = SubResource( 12 )
position = Vector2( 507, 510 )
scale = Vector2( 1062, 154 )
texture = SubResource( 9 )

[node name="VHS Shader" type="ColorRect" parent="."]
modulate = Color( 1, 1, 1, 0.192157 )
material = SubResource( 17 )
margin_right = 1028.0
margin_bottom = 600.0
rect_scale = Vector2( 1, 0.988892 )
rect_pivot_offset = Vector2( 531, 213.405 )

[node name="TryuLogo1TransparentSmall" type="Sprite" parent="."]
position = Vector2( 505, 74 )
scale = Vector2( 0.592105, 0.592105 )
texture = ExtResource( 7 )

[node name="Title" type="Label" parent="."]
visible = false
margin_left = 705.0
margin_top = 15.0
margin_right = 1011.0
margin_bottom = 66.0
custom_fonts/font = ExtResource( 8 )
text = "Tryü Sora"

[node name="Badge" type="Label" parent="."]
margin_left = 861.0
margin_top = 82.0
margin_right = 1609.0
margin_bottom = 133.0
rect_scale = Vector2( 0.2, 0.5 )
custom_fonts/font = ExtResource( 8 )
text = "badge"
align = 2

[node name="TopMessage" type="Label" parent="."]
visible = false
modulate = Color( 1, 1, 1, 0.137255 )
margin_left = 16.0
margin_top = 15.0
margin_right = 1334.0
margin_bottom = 66.0
rect_scale = Vector2( 0.4, 1 )
custom_fonts/font = ExtResource( 8 )
text = "For testing only. All rights reserved."
align = 2

[node name="Version" type="Label" parent="."]
margin_left = 712.0
margin_top = 33.0
margin_right = 1460.0
margin_bottom = 84.0
rect_scale = Vector2( 0.4, 1 )
custom_fonts/font = ExtResource( 8 )
text = "v0.00"
align = 2

[node name="Money" type="Label" parent="."]
margin_left = 299.0
margin_top = 529.0
margin_right = 1715.0
margin_bottom = 580.0
rect_scale = Vector2( 0.5, 1 )
custom_fonts/font = ExtResource( 8 )
text = "Money: $0"
align = 2

[node name="HighScoreFlow" type="Label" parent="."]
margin_left = 299.0
margin_top = 478.0
margin_right = 1715.0
margin_bottom = 529.0
rect_scale = Vector2( 0.5, 1 )
custom_fonts/font = ExtResource( 8 )
text = "Hi Score Flow: 0"
align = 2

[node name="HighScoreCampaign" type="Label" parent="."]
margin_left = 299.0
margin_top = 425.0
margin_right = 1715.0
margin_bottom = 476.0
rect_scale = Vector2( 0.5, 1 )
custom_fonts/font = ExtResource( 8 )
text = "Hi Score Campaign: 0"
align = 2

[node name="Menu" parent="." instance=ExtResource( 3 )]
rect_pivot_offset = Vector2( 0, 575 )

[node name="AudioStreamPlayer" type="AudioStreamPlayer" parent="."]

[node name="AudioStreamPlayer_title" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 4 )
volume_db = -10.0

[node name="PlayerDisplay" parent="." instance=ExtResource( 5 )]
position = Vector2( 507, 308 )
scale = Vector2( 2, 2 )

[node name="Demo" type="Label" parent="."]
modulate = Color( 1, 0, 0, 0.827451 )
margin_left = 778.0
margin_top = 466.0
margin_right = 914.0
margin_bottom = 510.0
rect_rotation = 15.0
rect_scale = Vector2( 2.5, 2.5 )
rect_pivot_offset = Vector2( 67.0968, 22.5921 )
custom_fonts/font = SubResource( 19 )
text = "DEMO"
align = 1
valign = 1

[node name="Overlay" type="Node2D" parent="."]

[node name="TerminalLabel" type="Label" parent="Overlay"]
modulate = Color( 0.423529, 1, 0.329412, 1 )
margin_left = 611.0
margin_top = 129.0
margin_right = 1008.0
margin_bottom = 411.0
grow_horizontal = 0
grow_vertical = 0
custom_fonts/font = SubResource( 18 )
text = "TERMINAL TEXT"
align = 2
valign = 2
