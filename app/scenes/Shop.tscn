[gd_scene load_steps=12 format=2]

[ext_resource path="res://assets/fonts/arial_narrow_7.ttf" type="DynamicFontData" id=1]
[ext_resource path="res://assets/fonts/c64.tres" type="DynamicFont" id=2]
[ext_resource path="res://scripts/Shop.gd" type="Script" id=3]
[ext_resource path="res://scenes/Themes/tryu_theme.tres" type="Theme" id=4]
[ext_resource path="res://scenes/Themes/buy_buttons.tres" type="Theme" id=5]

[sub_resource type="DynamicFontData" id=13]
font_path = "res://assets/fonts/Commodore Pixelized v1.2.ttf"

[sub_resource type="DynamicFont" id=12]
size = 44
font_data = SubResource( 13 )

[sub_resource type="DynamicFont" id=8]
size = 33
font_data = ExtResource( 1 )

[sub_resource type="Theme" id=9]
default_font = SubResource( 8 )

[sub_resource type="DynamicFont" id=10]
size = 27
font_data = ExtResource( 1 )

[sub_resource type="Theme" id=11]
default_font = SubResource( 10 )

[node name="Control" type="Control"]
anchor_right = 1.0
anchor_bottom = 1.0
rect_pivot_offset = Vector2( 80, 14 )
script = ExtResource( 3 )
__meta__ = {
"_edit_horizontal_guides_": [ 555.0 ],
"_edit_vertical_guides_": [ 999.0, 27.0 ]
}

[node name="ColorRect" type="ColorRect" parent="."]
margin_right = 1024.0
margin_bottom = 576.0
color = Color( 0, 0, 0, 1 )

[node name="Disclaimer" type="Label" parent="."]
modulate = Color( 1, 0, 0, 1 )
margin_left = 27.0
margin_top = 505.0
margin_right = 163.0
margin_bottom = 556.0
rect_scale = Vector2( 0.5, 0.5 )
custom_fonts/font = SubResource( 12 )
text = "Purchased items are valid 
for  a  single  game only"

[node name="Title" type="Label" parent="."]
margin_left = 21.0
margin_top = 15.0
margin_right = 157.0
margin_bottom = 66.0
custom_fonts/font = SubResource( 12 )
text = "Shop"

[node name="Money" type="Label" parent="."]
margin_left = 29.0
margin_top = 505.0
margin_right = 1413.0
margin_bottom = 556.0
rect_scale = Vector2( 0.7, 1 )
custom_fonts/font = SubResource( 12 )
text = "$1000"
align = 2

[node name="HBoxContainer" type="HBoxContainer" parent="."]
margin_left = 29.0
margin_top = 107.0
margin_right = 998.0
margin_bottom = 505.0

[node name="Labels" type="VBoxContainer" parent="HBoxContainer"]
margin_right = 239.0
margin_bottom = 398.0
size_flags_horizontal = 3

[node name="Speed" type="Label" parent="HBoxContainer/Labels"]
margin_right = 239.0
margin_bottom = 36.0
theme = SubResource( 9 )
text = "Speed"

[node name="Bullet" type="Label" parent="HBoxContainer/Labels"]
margin_top = 40.0
margin_right = 239.0
margin_bottom = 76.0
theme = SubResource( 9 )
text = "Bullet"

[node name="Weapon" type="Label" parent="HBoxContainer/Labels"]
margin_top = 80.0
margin_right = 239.0
margin_bottom = 116.0
theme = SubResource( 9 )
text = "Weapon"

[node name="Shield" type="Label" parent="HBoxContainer/Labels"]
margin_top = 120.0
margin_right = 239.0
margin_bottom = 156.0
theme = SubResource( 9 )
text = "Shield"

[node name="Rapid Fire" type="Label" parent="HBoxContainer/Labels"]
visible = false
margin_top = 160.0
margin_right = 239.0
margin_bottom = 196.0
theme = SubResource( 9 )
text = "Auto Fire"

[node name="Extra Life" type="Label" parent="HBoxContainer/Labels"]
margin_top = 160.0
margin_right = 239.0
margin_bottom = 196.0
theme = SubResource( 9 )
text = "Lives"

[node name="Wing Simple" type="Label" parent="HBoxContainer/Labels"]
margin_top = 200.0
margin_right = 239.0
margin_bottom = 236.0
theme = SubResource( 9 )
text = "Simple Wing"

[node name="Wing Super" type="Label" parent="HBoxContainer/Labels"]
margin_top = 240.0
margin_right = 239.0
margin_bottom = 276.0
theme = SubResource( 9 )
text = "Super Wing"

[node name="Wing Homing" type="Label" parent="HBoxContainer/Labels"]
margin_top = 280.0
margin_right = 239.0
margin_bottom = 316.0
theme = SubResource( 9 )
text = "Homing Wing"

[node name="Prices" type="VBoxContainer" parent="HBoxContainer"]
margin_left = 243.0
margin_right = 482.0
margin_bottom = 398.0
size_flags_horizontal = 3

[node name="Speed" type="Label" parent="HBoxContainer/Prices"]
margin_right = 239.0
margin_bottom = 36.0
theme = SubResource( 9 )
text = "$500"

[node name="Bullet" type="Label" parent="HBoxContainer/Prices"]
margin_top = 40.0
margin_right = 239.0
margin_bottom = 76.0
theme = SubResource( 9 )
text = "$500"

[node name="Weapon" type="Label" parent="HBoxContainer/Prices"]
margin_top = 80.0
margin_right = 239.0
margin_bottom = 116.0
theme = SubResource( 9 )
text = "$1500"

[node name="Shield" type="Label" parent="HBoxContainer/Prices"]
margin_top = 120.0
margin_right = 239.0
margin_bottom = 156.0
theme = SubResource( 9 )
text = "$5500"

[node name="RapidFire" type="Label" parent="HBoxContainer/Prices"]
visible = false
margin_top = 160.0
margin_right = 239.0
margin_bottom = 196.0
theme = SubResource( 9 )
text = "$2000"

[node name="Lives" type="Label" parent="HBoxContainer/Prices"]
margin_top = 160.0
margin_right = 239.0
margin_bottom = 196.0
theme = SubResource( 9 )
text = "$10000"

[node name="Wing1" type="Label" parent="HBoxContainer/Prices"]
margin_top = 200.0
margin_right = 239.0
margin_bottom = 236.0
theme = SubResource( 9 )
text = "$7000"

[node name="Wing2" type="Label" parent="HBoxContainer/Prices"]
margin_top = 240.0
margin_right = 239.0
margin_bottom = 276.0
theme = SubResource( 9 )
text = "$9000"

[node name="Wing3" type="Label" parent="HBoxContainer/Prices"]
margin_top = 280.0
margin_right = 239.0
margin_bottom = 316.0
theme = SubResource( 9 )
text = "$10000"

[node name="Values" type="VBoxContainer" parent="HBoxContainer"]
margin_left = 486.0
margin_right = 725.0
margin_bottom = 398.0
size_flags_horizontal = 3

[node name="Speed" type="Label" parent="HBoxContainer/Values"]
margin_right = 239.0
margin_bottom = 36.0
theme = SubResource( 9 )
text = "200"

[node name="Bullet" type="Label" parent="HBoxContainer/Values"]
margin_top = 40.0
margin_right = 239.0
margin_bottom = 76.0
theme = SubResource( 9 )
text = "3"

[node name="Weapon" type="Label" parent="HBoxContainer/Values"]
margin_top = 80.0
margin_right = 239.0
margin_bottom = 116.0
theme = SubResource( 9 )
text = "1"

[node name="Shield" type="Label" parent="HBoxContainer/Values"]
margin_top = 120.0
margin_right = 239.0
margin_bottom = 156.0
theme = SubResource( 9 )
text = "0"

[node name="RapidFire" type="Label" parent="HBoxContainer/Values"]
visible = false
margin_top = 160.0
margin_right = 239.0
margin_bottom = 196.0
theme = SubResource( 9 )
text = "NO"

[node name="Lives" type="Label" parent="HBoxContainer/Values"]
margin_top = 160.0
margin_right = 239.0
margin_bottom = 196.0
theme = SubResource( 9 )
text = "2"

[node name="Wing1" type="Label" parent="HBoxContainer/Values"]
margin_top = 200.0
margin_right = 239.0
margin_bottom = 236.0
theme = SubResource( 9 )
text = "0"

[node name="Wing2" type="Label" parent="HBoxContainer/Values"]
margin_top = 240.0
margin_right = 239.0
margin_bottom = 276.0
theme = SubResource( 9 )
text = "0"

[node name="Wing3" type="Label" parent="HBoxContainer/Values"]
margin_top = 280.0
margin_right = 239.0
margin_bottom = 316.0
theme = SubResource( 9 )
text = "0"

[node name="Buttons" type="VBoxContainer" parent="HBoxContainer"]
margin_left = 729.0
margin_right = 969.0
margin_bottom = 398.0
size_flags_horizontal = 3

[node name="Speed" type="Button" parent="HBoxContainer/Buttons"]
margin_right = 240.0
margin_bottom = 37.0
theme = ExtResource( 5 )
text = "<--- BUY"
align = 0

[node name="Bullet" type="Button" parent="HBoxContainer/Buttons"]
margin_top = 41.0
margin_right = 240.0
margin_bottom = 78.0
theme = ExtResource( 5 )
text = "<--- BUY"
align = 0

[node name="Weapon" type="Button" parent="HBoxContainer/Buttons"]
margin_top = 82.0
margin_right = 240.0
margin_bottom = 119.0
theme = ExtResource( 5 )
text = "<--- BUY"
align = 0

[node name="Shield" type="Button" parent="HBoxContainer/Buttons"]
margin_top = 123.0
margin_right = 240.0
margin_bottom = 160.0
theme = ExtResource( 5 )
text = "<--- BUY"
align = 0

[node name="RapidFire" type="Button" parent="HBoxContainer/Buttons"]
visible = false
margin_top = 160.0
margin_right = 240.0
margin_bottom = 196.0
theme = SubResource( 11 )
text = "Buy"

[node name="Lives" type="Button" parent="HBoxContainer/Buttons"]
margin_top = 164.0
margin_right = 240.0
margin_bottom = 201.0
theme = ExtResource( 5 )
text = "<--- BUY"
align = 0

[node name="Wing1" type="Button" parent="HBoxContainer/Buttons"]
margin_top = 205.0
margin_right = 240.0
margin_bottom = 242.0
theme = ExtResource( 5 )
text = "<--- BUY"
align = 0

[node name="Wing2" type="Button" parent="HBoxContainer/Buttons"]
margin_top = 246.0
margin_right = 240.0
margin_bottom = 283.0
theme = ExtResource( 5 )
text = "<--- BUY"
align = 0

[node name="Wing3" type="Button" parent="HBoxContainer/Buttons"]
margin_top = 287.0
margin_right = 240.0
margin_bottom = 324.0
theme = ExtResource( 5 )
text = "<--- BUY"
align = 0

[node name="CloseButton" type="Button" parent="."]
margin_left = 821.0
margin_top = 14.0
margin_right = 998.0
margin_bottom = 65.0
theme = ExtResource( 4 )
custom_fonts/font = ExtResource( 2 )
text = "Close"
