[gd_scene load_steps=9 format=2]

[ext_resource path="res://scenes/Themes/in_game_bonus_label_fon.tres" type="DynamicFont" id=1]
[ext_resource path="res://addons/kenney_particle_pack/star_08.png" type="Texture" id=2]
[ext_resource path="res://scripts/BonusMultiplierLabel.gd" type="Script" id=3]

[sub_resource type="Gradient" id=2]
offsets = PoolRealArray( 0, 0.179487, 0.65812 )
colors = PoolColorArray( 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=3]
gradient = SubResource( 2 )

[sub_resource type="ParticlesMaterial" id=4]
flag_disable_z = true
direction = Vector3( 0, -1, 0 )
spread = 90.0
gravity = Vector3( 0, 98, 0 )
initial_velocity = 80.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 0.01
scale_random = 0.05
color_ramp = SubResource( 3 )
hue_variation = 0.62
hue_variation_random = 0.36

[sub_resource type="Animation" id=5]
resource_name = "LabelAnim"
tracks/0/type = "value"
tracks/0/path = NodePath("Bonus:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, -100 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Bonus:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.4, 1 ),
"transitions": PoolRealArray( 0.5, 1, 0.5 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[sub_resource type="Animation" id=6]
resource_name = "LabelAnimOminous"
tracks/0/type = "value"
tracks/0/path = NodePath("Bonus:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, -100 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Bonus:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.4, 1 ),
"transitions": PoolRealArray( 0.5, 1, 0.5 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Bonus:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[node name="Multiplier" type="Node2D"]
script = ExtResource( 3 )

[node name="Bonus" type="Node2D" parent="."]
modulate = Color( 1, 1, 1, 0 )

[node name="Label" type="Label" parent="Bonus"]
margin_left = -108.0
margin_top = -17.0
margin_right = 109.0
margin_bottom = 28.0
grow_horizontal = 2
grow_vertical = 2
custom_fonts/font = ExtResource( 1 )
text = "200"
align = 1
valign = 1

[node name="Particles2D" type="Particles2D" parent="Bonus"]
emitting = false
amount = 20
lifetime = 3.0
speed_scale = 3.0
explosiveness = 0.8
local_coords = false
process_material = SubResource( 4 )
texture = ExtResource( 2 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
anims/LabelAnim = SubResource( 5 )
anims/LabelAnimOminous = SubResource( 6 )
