[gd_scene load_steps=6 format=2]

[ext_resource path="res://assets/Enemy - Blue blade - Frame 2.png" type="Texture" id=1]
[ext_resource path="res://assets/Enemy - Blue blade - Frame 1.png" type="Texture" id=2]
[ext_resource path="res://scripts/Enemy_5.gd" type="Script" id=3]

[sub_resource type="SpriteFrames" id=1]
animations = [ {
"frames": [ ExtResource( 2 ), ExtResource( 1 ) ],
"loop": true,
"name": "default",
"speed": 1.0
} ]

[sub_resource type="RectangleShape2D" id=2]
extents = Vector2( 11.3333, 12.6667 )

[node name="Enemy_5" type="Area2D"]
script = ExtResource( 3 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
scale = Vector2( 2.75, 2.75 )
frames = SubResource( 1 )
frame = 1
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
position = Vector2( 0, -1 )
scale = Vector2( 1.5, 1.5 )
shape = SubResource( 2 )
