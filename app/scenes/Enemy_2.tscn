[gd_scene load_steps=7 format=2]

[ext_resource path="res://assets/enemy2.png" type="Texture" id=1]
[ext_resource path="res://scripts/Enemy_2.gd" type="Script" id=2]

[sub_resource type="AtlasTexture" id=1]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 22, 22 )

[sub_resource type="AtlasTexture" id=2]
atlas = ExtResource( 1 )
region = Rect2( 22, 0, 22, 22 )

[sub_resource type="SpriteFrames" id=3]
animations = [ {
"frames": [ SubResource( 1 ), SubResource( 2 ) ],
"loop": true,
"name": "default",
"speed": 2.0
} ]

[sub_resource type="RectangleShape2D" id=4]
extents = Vector2( 18, 22 )

[node name="Enemy_2" type="Area2D"]
script = ExtResource( 2 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
scale = Vector2( 3.75, 3.75 )
frames = SubResource( 3 )
frame = 1
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
position = Vector2( -1, -4 )
shape = SubResource( 4 )
