[gd_scene load_steps=13 format=2]

[ext_resource path="res://assets/mines-green.png" type="Texture" id=1]
[ext_resource path="res://assets/mines-red.png" type="Texture" id=2]
[ext_resource path="res://scripts/SpaceMine.gd" type="Script" id=3]
[ext_resource path="res://assets/sounds/beep.wav" type="AudioStream" id=4]
[ext_resource path="res://addons/kenney_particle_pack/spark_04.png" type="Texture" id=5]

[sub_resource type="Shader" id=1]

[sub_resource type="ShaderMaterial" id=2]
shader = SubResource( 1 )

[sub_resource type="SpriteFrames" id=3]
animations = [ {
"frames": [ ExtResource( 1 ), ExtResource( 2 ) ],
"loop": true,
"name": "default",
"speed": 10.0
} ]

[sub_resource type="CapsuleShape2D" id=4]
radius = 17.0001
height = 23.9998

[sub_resource type="Gradient" id=58]
offsets = PoolRealArray( 0.002849, 0.381766, 0.666667 )
colors = PoolColorArray( 1, 0, 0, 1, 1, 0.78125, 0.78125, 1, 0.96875, 1, 0, 0 )

[sub_resource type="GradientTexture" id=57]
gradient = SubResource( 58 )

[sub_resource type="ParticlesMaterial" id=59]
emission_shape = 1
emission_sphere_radius = 10.65
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 0.1
scale_random = 0.1
color_ramp = SubResource( 57 )

[node name="SpaceMine" type="Area2D"]
script = ExtResource( 3 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
material = SubResource( 2 )
frames = SubResource( 3 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
rotation = 1.57079
shape = SubResource( 4 )

[node name="AudioStreamPlayer2D" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 4 )
volume_db = -20.0
pitch_scale = 0.75

[node name="Particles2D" type="Particles2D" parent="."]
modulate = Color( 1, 1, 1, 0.423529 )
amount = 15
lifetime = 0.5
process_material = SubResource( 59 )
texture = ExtResource( 5 )
