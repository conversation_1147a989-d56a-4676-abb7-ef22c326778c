[gd_scene load_steps=8 format=2]

[ext_resource path="res://scenes/PlayerDisplay.tscn" type="PackedScene" id=1]
[ext_resource path="res://scenes/Themes/tryu_theme.tres" type="Theme" id=2]
[ext_resource path="res://scripts/ShipSelector.gd" type="Script" id=3]

[sub_resource type="DynamicFontData" id=18]
font_path = "res://assets/fonts/Commodore Pixelized v1.2.ttf"

[sub_resource type="DynamicFont" id=20]
size = 44
font_data = SubResource( 18 )

[sub_resource type="DynamicFontData" id=19]
font_path = "res://assets/fonts/Commodore Pixelized v1.2.ttf"

[sub_resource type="DynamicFont" id=21]
size = 44
font_data = SubResource( 19 )

[node name="ShipSelector" type="Node2D"]
script = ExtResource( 3 )

[node name="ColorRect" type="ColorRect" parent="."]
margin_right = 1024.0
margin_bottom = 576.0
color = Color( 0, 0, 0, 1 )

[node name="Ship" parent="." instance=ExtResource( 1 )]
position = Vector2( 868, 266 )
scale = Vector2( 3, 3 )

[node name="Title" type="Label" parent="."]
margin_left = 19.0
margin_top = 21.0
margin_right = 461.0
margin_bottom = 72.0
custom_fonts/font = SubResource( 20 )
text = "Ship Selector"

[node name="Name" type="Label" parent="."]
modulate = Color( 0.717647, 0.239216, 0.647059, 1 )
margin_left = 23.0
margin_top = 92.0
margin_right = 1439.0
margin_bottom = 143.0
rect_scale = Vector2( 0.5, 1 )
custom_fonts/font = SubResource( 21 )
text = "Name"

[node name="Description" type="Label" parent="."]
margin_left = 24.0
margin_top = 162.0
margin_right = 1440.0
margin_bottom = 399.0
rect_scale = Vector2( 0.5, 1 )
custom_fonts/font = SubResource( 21 )
text = "Description"
autowrap = true

[node name="Note" type="Label" parent="."]
modulate = Color( 1, 0, 0, 1 )
margin_left = 27.0
margin_top = 418.0
margin_right = 1443.0
margin_bottom = 469.0
rect_scale = Vector2( 0.5, 1 )
custom_fonts/font = SubResource( 21 )
text = "Finish the game to get more ships!"

[node name="Previous" type="Button" parent="."]
margin_left = 25.0
margin_top = 487.0
margin_right = 106.0
margin_bottom = 543.0
focus_neighbour_left = NodePath("../Select")
focus_neighbour_top = NodePath("../Close")
focus_neighbour_right = NodePath("../Next")
focus_neighbour_bottom = NodePath("../Select")
theme = ExtResource( 2 )
text = "<"

[node name="Next" type="Button" parent="."]
margin_left = 111.0
margin_top = 488.0
margin_right = 192.0
margin_bottom = 544.0
focus_neighbour_left = NodePath("../Previous")
focus_neighbour_top = NodePath("../Close")
focus_neighbour_right = NodePath("../Select")
focus_neighbour_bottom = NodePath("../Select")
theme = ExtResource( 2 )
text = ">"

[node name="Select" type="Button" parent="."]
margin_left = 829.0
margin_top = 494.0
margin_right = 995.0
margin_bottom = 550.0
focus_neighbour_left = NodePath("../Next")
focus_neighbour_top = NodePath("../Close")
focus_neighbour_right = NodePath("../Previous")
focus_neighbour_bottom = NodePath("../Close")
theme = ExtResource( 2 )
text = "Select"

[node name="Close" type="Button" parent="."]
margin_left = 826.0
margin_top = 23.0
margin_right = 992.0
margin_bottom = 79.0
focus_neighbour_left = NodePath("../Next")
focus_neighbour_top = NodePath("../Select")
focus_neighbour_right = NodePath("../Previous")
focus_neighbour_bottom = NodePath("../Select")
theme = ExtResource( 2 )
text = "Close"

[node name="Unavailable" type="Node2D" parent="."]
position = Vector2( 850, 256 )
rotation = 0.720821

[node name="2" type="Label" parent="Unavailable"]
modulate = Color( 0, 0, 0, 1 )
margin_left = -150.0
margin_top = -36.0
margin_right = 224.0
margin_bottom = 15.0
rect_scale = Vector2( 1, 1.5 )
custom_fonts/font = SubResource( 21 )
text = "LOCKED"
align = 1

[node name="1" type="Label" parent="Unavailable"]
modulate = Color( 1, 0, 0, 1 )
margin_left = -154.0
margin_top = -40.0
margin_right = 220.0
margin_bottom = 11.0
rect_scale = Vector2( 1, 1.5 )
custom_fonts/font = SubResource( 21 )
text = "LOCKED"
align = 1
