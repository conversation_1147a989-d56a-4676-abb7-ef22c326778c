[gd_scene load_steps=7 format=2]

[ext_resource path="res://scripts/SplashScreen.gd" type="Script" id=1]
[ext_resource path="res://assets/sounds/521826__lartti__fluorescent-lamp-blinks-constantly.wav" type="AudioStream" id=2]

[sub_resource type="DynamicFontData" id=13]
font_path = "res://assets/fonts/Commodore Pixelized v1.2.ttf"

[sub_resource type="DynamicFont" id=14]
size = 44
font_data = SubResource( 13 )

[sub_resource type="Environment" id=15]
background_mode = 4
glow_enabled = true
glow_intensity = 0.92
glow_strength = 0.99
glow_bloom = 0.14
glow_blend_mode = 1
adjustment_enabled = true
adjustment_brightness = 0.58

[sub_resource type="Animation" id=16]
resource_name = "splash_text"
length = 2.0
tracks/0/type = "value"
tracks/0/path = NodePath("Title:percent_visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.0, 1.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Fly:percent_visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 2 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ 0.0, 1.0 ]
}

[node name="SplashScreen" type="Node2D"]
position = Vector2( 0, 1 )
script = ExtResource( 1 )

[node name="Title" type="Label" parent="."]
modulate = Color( 0.313726, 0.823529, 0.188235, 1 )
margin_left = 282.0
margin_top = 205.0
margin_right = 724.0
margin_bottom = 256.0
rect_pivot_offset = Vector2( 291, 39 )
custom_fonts/font = SubResource( 14 )
text = "DeadFly.Games"
percent_visible = 0.0

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource( 15 )

[node name="Fly" type="Label" parent="."]
modulate = Color( 0.313726, 0.823529, 0.188235, 1 )
margin_left = 420.0
margin_top = 274.0
margin_right = 701.0
margin_bottom = 412.0
rect_rotation = 12.3
rect_scale = Vector2( 0.7, 0.7 )
custom_fonts/font = SubResource( 14 )
text = "   \"\"
\\\\(XX)//
   ~~"
percent_visible = 0.0

[node name="AudioStreamPlayer" type="AudioStreamPlayer" parent="."]
stream = ExtResource( 2 )
autoplay = true

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "splash_text"
reset_on_save = false
anims/splash_text = SubResource( 16 )
