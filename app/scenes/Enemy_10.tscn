[gd_scene load_steps=7 format=2]

[ext_resource path="res://assets/minion-2.png" type="Texture" id=1]
[ext_resource path="res://assets/minion-1.png" type="Texture" id=2]
[ext_resource path="res://scripts/Enemy_10.gd" type="Script" id=3]
[ext_resource path="res://assets/minion-3.png" type="Texture" id=4]

[sub_resource type="SpriteFrames" id=3]
animations = [ {
"frames": [ ExtResource( 2 ), ExtResource( 1 ), ExtResource( 4 ) ],
"loop": true,
"name": "default",
"speed": 6.0
} ]

[sub_resource type="RectangleShape2D" id=4]
extents = Vector2( 16.5, 17 )

[node name="Enemy_10" type="Area2D"]
script = ExtResource( 3 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
position = Vector2( -4.61936e-07, -4 )
scale = Vector2( 1.4, 1.4 )
frames = SubResource( 3 )
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2( 0, 5 )
shape = SubResource( 4 )
