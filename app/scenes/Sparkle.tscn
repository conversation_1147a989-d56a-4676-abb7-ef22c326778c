[gd_scene load_steps=6 format=2]

[ext_resource path="res://scripts/Sparkle.gd" type="Script" id=1]
[ext_resource path="res://addons/kenney_particle_pack/16/star_07_16.png" type="Texture" id=2]

[sub_resource type="Gradient" id=1]
offsets = PoolRealArray( 0, 0.144708, 1 )
colors = PoolColorArray( 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=2]
gradient = SubResource( 1 )

[sub_resource type="ParticlesMaterial" id=3]
flag_disable_z = true
direction = Vector3( 0, 1, 0 )
spread = 180.0
gravity = Vector3( 0, 100, 0 )
initial_velocity = 20.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 0.3
scale_random = 0.2
color_ramp = SubResource( 2 )

[node name="Sparkle" type="Node2D"]
script = ExtResource( 1 )

[node name="Particles2D" type="Particles2D" parent="."]
amount = 15
explosiveness = 0.7
process_material = SubResource( 3 )
texture = ExtResource( 2 )
