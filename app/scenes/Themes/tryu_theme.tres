[gd_resource type="Theme" load_steps=13 format=2]

[ext_resource path="res://scenes/Themes/normal_button_styleboxflat.tres" type="StyleBox" id=1]
[ext_resource path="res://assets/fonts/c64.tres" type="DynamicFont" id=2]
[ext_resource path="res://scenes/Themes/disabled_button_styleboxflat.tres" type="StyleBox" id=3]
[ext_resource path="res://scenes/Themes/focused_button_styleboxflat.tres" type="StyleBox" id=4]
[ext_resource path="res://scenes/Themes/pressed_button_styleboxflat.tres" type="StyleBox" id=5]
[ext_resource path="res://scenes/Themes/hover_button.tres" type="StyleBox" id=6]

[sub_resource type="StyleBoxEmpty" id=1]

[sub_resource type="StyleBoxEmpty" id=2]

[sub_resource type="StyleBoxEmpty" id=3]

[sub_resource type="StyleBoxEmpty" id=4]

[sub_resource type="StyleBoxEmpty" id=5]

[sub_resource type="StyleBoxEmpty" id=6]

[resource]
Button/colors/font_color = Color( 1, 1, 1, 1 )
Button/colors/font_color_disabled = Color( 1, 1, 1, 0.647059 )
Button/colors/font_color_focus = Color( 1, 1, 1, 1 )
Button/colors/font_color_hover = Color( 1, 1, 1, 1 )
Button/colors/font_color_pressed = Color( 1, 1, 1, 1 )
Button/constants/hseparation = 5
Button/fonts/font = ExtResource( 2 )
Button/styles/disabled = ExtResource( 3 )
Button/styles/focus = ExtResource( 4 )
Button/styles/hover = ExtResource( 6 )
Button/styles/normal = ExtResource( 1 )
Button/styles/pressed = ExtResource( 5 )
ItemList/colors/font_color = Color( 1, 1, 1, 1 )
ItemList/colors/font_color_selected = Color( 1, 1, 1, 1 )
ItemList/colors/guide_color = Color( 0.529412, 0.12549, 0.470588, 0.345098 )
ItemList/constants/hseparation = 0
ItemList/constants/icon_margin = 0
ItemList/constants/line_separation = 0
ItemList/constants/vseparation = 0
ItemList/fonts/font = null
ItemList/styles/bg = SubResource( 1 )
ItemList/styles/bg_focus = SubResource( 2 )
ItemList/styles/cursor = SubResource( 3 )
ItemList/styles/cursor_unfocused = SubResource( 4 )
ItemList/styles/selected = SubResource( 5 )
ItemList/styles/selected_focus = SubResource( 6 )
