[gd_resource type="Theme" load_steps=18 format=2]

[ext_resource path="res://scenes/Themes/tab_font.tres" type="DynamicFont" id=1]

[sub_resource type="Image" id=27]
data = {
"data": PoolByteArray( 45, 44, 47, 8, 72, 70, 74, 254, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 72, 70, 74, 254, 45, 44, 47, 9, 72, 70, 74, 255, 64, 62, 66, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 64, 62, 66, 255, 72, 70, 74, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 195, 195, 195, 255, 195, 195, 195, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 194, 194, 194, 255, 194, 194, 194, 255, 194, 194, 194, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 193, 193, 193, 255, 194, 194, 194, 255, 194, 194, 194, 255, 194, 194, 194, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 193, 193, 193, 255, 193, 193, 193, 255, 193, 193, 193, 255, 193, 193, 193, 255, 193, 193, 193, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 192, 192, 192, 255, 192, 192, 192, 255, 192, 192, 192, 255, 192, 192, 192, 255, 192, 192, 192, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 192, 192, 192, 255, 192, 192, 192, 255, 192, 192, 192, 255, 192, 192, 192, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 191, 191, 191, 255, 191, 191, 191, 255, 191, 191, 191, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 190, 190, 190, 255, 190, 190, 190, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 72, 70, 74, 255, 64, 62, 66, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 64, 62, 66, 255, 72, 70, 74, 255, 45, 44, 47, 13, 72, 70, 74, 254, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 72, 70, 74, 254, 45, 44, 47, 25 ),
"format": "RGBA8",
"height": 16,
"mipmaps": false,
"width": 16
}

[sub_resource type="ImageTexture" id=6]
flags = 4
flags = 4
image = SubResource( 27 )
size = Vector2( 16, 16 )

[sub_resource type="Image" id=28]
data = {
"data": PoolByteArray( 61, 59, 63, 7, 96, 93, 98, 254, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 96, 93, 98, 254, 61, 59, 63, 12, 96, 93, 98, 255, 86, 83, 88, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 86, 83, 88, 255, 96, 93, 98, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 201, 201, 201, 255, 201, 201, 201, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 200, 200, 200, 255, 200, 200, 200, 255, 200, 200, 200, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 198, 198, 198, 255, 199, 199, 199, 255, 198, 198, 198, 255, 199, 199, 199, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 198, 198, 198, 255, 198, 198, 198, 255, 198, 198, 198, 255, 197, 197, 197, 255, 198, 198, 198, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 197, 197, 197, 255, 197, 197, 197, 255, 197, 197, 197, 255, 197, 197, 197, 255, 197, 197, 197, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 196, 196, 196, 255, 196, 196, 196, 255, 196, 196, 196, 255, 196, 196, 196, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 195, 195, 195, 255, 195, 195, 195, 255, 196, 196, 196, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 195, 195, 195, 255, 195, 195, 195, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 96, 93, 98, 255, 86, 83, 88, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 86, 83, 88, 255, 96, 93, 98, 255, 61, 59, 63, 9, 96, 93, 98, 254, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 96, 93, 98, 254, 61, 59, 63, 28 ),
"format": "RGBA8",
"height": 16,
"mipmaps": false,
"width": 16
}

[sub_resource type="ImageTexture" id=8]
flags = 4
flags = 4
image = SubResource( 28 )
size = Vector2( 16, 16 )

[sub_resource type="Image" id=29]
data = {
"data": PoolByteArray( 45, 44, 47, 8, 72, 70, 74, 254, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 72, 70, 74, 254, 45, 44, 47, 9, 72, 70, 74, 255, 64, 62, 66, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 64, 62, 66, 255, 72, 70, 74, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 195, 195, 195, 255, 195, 195, 195, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 195, 195, 195, 255, 194, 194, 194, 255, 194, 194, 194, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 193, 193, 193, 255, 194, 194, 194, 255, 194, 194, 194, 255, 194, 194, 194, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 193, 193, 193, 255, 193, 193, 193, 255, 193, 193, 193, 255, 193, 193, 193, 255, 193, 193, 193, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 192, 192, 192, 255, 192, 192, 192, 255, 192, 192, 192, 255, 192, 192, 192, 255, 192, 192, 192, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 192, 192, 192, 255, 192, 192, 192, 255, 192, 192, 192, 255, 192, 192, 192, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 191, 191, 191, 255, 191, 191, 191, 255, 191, 191, 191, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 190, 190, 190, 255, 190, 190, 190, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 76, 74, 78, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 76, 74, 78, 255, 72, 70, 74, 255, 64, 62, 66, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 56, 54, 58, 255, 64, 62, 66, 255, 72, 70, 74, 255, 45, 44, 47, 13, 72, 70, 74, 254, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 76, 74, 78, 255, 72, 70, 74, 254, 45, 44, 47, 25 ),
"format": "RGBA8",
"height": 16,
"mipmaps": false,
"width": 16
}

[sub_resource type="ImageTexture" id=10]
flags = 4
flags = 4
image = SubResource( 29 )
size = Vector2( 16, 16 )

[sub_resource type="Image" id=30]
data = {
"data": PoolByteArray( 61, 59, 63, 7, 96, 93, 98, 254, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 96, 93, 98, 254, 61, 59, 63, 12, 96, 93, 98, 255, 86, 83, 88, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 86, 83, 88, 255, 96, 93, 98, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 201, 201, 201, 255, 201, 201, 201, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 200, 200, 200, 255, 200, 200, 200, 255, 200, 200, 200, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 198, 198, 198, 255, 199, 199, 199, 255, 198, 198, 198, 255, 199, 199, 199, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 198, 198, 198, 255, 198, 198, 198, 255, 197, 197, 197, 255, 198, 198, 198, 255, 198, 198, 198, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 197, 197, 197, 255, 197, 197, 197, 255, 197, 197, 197, 255, 197, 197, 197, 255, 197, 197, 197, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 196, 196, 196, 255, 196, 196, 196, 255, 196, 196, 196, 255, 196, 196, 196, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 195, 195, 195, 255, 195, 195, 195, 255, 195, 195, 195, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 194, 194, 194, 255, 194, 194, 194, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 101, 98, 103, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 101, 98, 103, 255, 96, 93, 98, 255, 86, 83, 88, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 75, 73, 78, 255, 86, 83, 88, 255, 96, 93, 98, 255, 61, 59, 63, 9, 96, 93, 98, 254, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 101, 98, 103, 255, 96, 93, 98, 254, 61, 59, 63, 28 ),
"format": "RGBA8",
"height": 16,
"mipmaps": false,
"width": 16
}

[sub_resource type="ImageTexture" id=12]
flags = 4
flags = 4
image = SubResource( 30 )
size = Vector2( 16, 16 )

[sub_resource type="Image" id=31]
data = {
"data": PoolByteArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 225, 94, 224, 244, 224, 255, 224, 255, 224, 255, 224, 255, 224, 246, 225, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 223, 40, 223, 210, 224, 255, 224, 255, 224, 215, 227, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 232, 11, 225, 159, 225, 159, 232, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ),
"format": "LumAlpha8",
"height": 16,
"mipmaps": false,
"width": 16
}

[sub_resource type="ImageTexture" id=14]
flags = 4
flags = 4
image = SubResource( 31 )
size = Vector2( 16, 16 )

[sub_resource type="Image" id=32]
data = {
"data": PoolByteArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 224, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 225, 94, 224, 244, 224, 255, 224, 255, 224, 255, 224, 255, 224, 246, 225, 102, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 223, 40, 223, 210, 224, 255, 224, 255, 224, 215, 227, 45, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 232, 11, 225, 159, 225, 159, 232, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ),
"format": "LumAlpha8",
"height": 16,
"mipmaps": false,
"width": 16
}

[sub_resource type="ImageTexture" id=16]
flags = 4
flags = 4
image = SubResource( 32 )
size = Vector2( 16, 16 )

[sub_resource type="StyleBoxEmpty" id=23]

[sub_resource type="StyleBoxEmpty" id=24]
content_margin_left = 10.0
content_margin_right = 10.0
content_margin_top = 10.0
content_margin_bottom = 10.0

[sub_resource type="StyleBoxEmpty" id=25]
content_margin_left = 10.0
content_margin_right = 10.0
content_margin_top = 10.0
content_margin_bottom = 10.0

[sub_resource type="StyleBoxEmpty" id=26]
content_margin_left = 10.0
content_margin_right = 10.0
content_margin_top = 10.0
content_margin_bottom = 10.0

[resource]
TabContainer/colors/font_color_bg = Color( 0.294118, 0.12549, 0.937255, 1 )
TabContainer/colors/font_color_disabled = Color( 0.294118, 0.12549, 0.937255, 0.776471 )
TabContainer/colors/font_color_fg = Color( 0.529412, 0.12549, 0.470588, 1 )
TabContainer/constants/hseparation = 4
TabContainer/constants/label_valign_bg = 2
TabContainer/constants/label_valign_fg = 0
TabContainer/constants/side_margin = 8
TabContainer/constants/top_margin = 24
TabContainer/fonts/font = ExtResource( 1 )
TabContainer/icons/decrement = SubResource( 6 )
TabContainer/icons/decrement_highlight = SubResource( 8 )
TabContainer/icons/increment = SubResource( 10 )
TabContainer/icons/increment_highlight = SubResource( 12 )
TabContainer/icons/menu = SubResource( 14 )
TabContainer/icons/menu_highlight = SubResource( 16 )
TabContainer/styles/panel = SubResource( 23 )
TabContainer/styles/tab_bg = SubResource( 24 )
TabContainer/styles/tab_disabled = SubResource( 25 )
TabContainer/styles/tab_fg = SubResource( 26 )
