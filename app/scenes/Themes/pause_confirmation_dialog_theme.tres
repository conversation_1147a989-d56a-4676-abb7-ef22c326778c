[gd_resource type="Theme" format=2]

[resource]
Button/colors/font_color = Color( 0.88, 0.88, 0.88, 1 )
Button/colors/font_color_disabled = Color( 0.9, 0.9, 0.9, 0.2 )
Button/colors/font_color_focus = Color( 0.94, 0.94, 0.94, 1 )
Button/colors/font_color_hover = Color( 0.94, 0.94, 0.94, 1 )
Button/colors/font_color_hover_pressed = Color( 1, 1, 1, 1 )
Button/colors/font_color_pressed = Color( 1, 1, 1, 1 )
Button/colors/icon_color_disabled = Color( 1, 1, 1, 0.4 )
Button/colors/icon_color_focus = Color( 1, 1, 1, 1 )
Button/colors/icon_color_hover = Color( 1, 1, 1, 1 )
Button/colors/icon_color_hover_pressed = Color( 1, 1, 1, 1 )
Button/colors/icon_color_normal = Color( 1, 1, 1, 1 )
Button/colors/icon_color_pressed = Color( 1, 1, 1, 1 )
Button/constants/hseparation = 2
Button/fonts/font = null
Button/styles/disabled = null
Button/styles/focus = null
Button/styles/hover = null
Button/styles/normal = null
Button/styles/pressed = null
