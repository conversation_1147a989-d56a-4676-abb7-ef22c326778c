[gd_resource type="Theme" load_steps=11 format=2]

[ext_resource path="res://scenes/Themes/pressed_button_styleboxflat.tres" type="StyleBox" id=1]
[ext_resource path="res://scenes/Themes/hover_button.tres" type="StyleBox" id=2]
[ext_resource path="res://scenes/Themes/normal_button_styleboxflat.tres" type="StyleBox" id=3]
[ext_resource path="res://scenes/Themes/focused_button_styleboxflat.tres" type="StyleBox" id=4]
[ext_resource path="res://assets/fonts/Commodore Pixelized v1.2.ttf" type="DynamicFontData" id=5]
[ext_resource path="res://scenes/Themes/disabled_button_styleboxflat.tres" type="StyleBox" id=6]

[sub_resource type="DynamicFont" id=2]
size = 24
outline_size = 3
outline_color = Color( 0, 0, 0, 1 )
use_filter = true
font_data = ExtResource( 5 )

[sub_resource type="DynamicFontData" id=1]
font_path = "res://assets/fonts/arial_narrow_7.ttf"

[sub_resource type="DynamicFont" id=3]
size = 40
outline_size = 4
outline_color = Color( 0, 0, 0, 1 )
font_data = SubResource( 1 )

[sub_resource type="StyleBoxFlat" id=4]
bg_color = Color( 0, 0, 0, 1 )

[resource]
Button/colors/font_color = Color( 1, 1, 1, 1 )
Button/colors/font_color_disabled = Color( 1, 1, 1, 0.647059 )
Button/colors/font_color_focus = Color( 1, 1, 1, 1 )
Button/colors/font_color_hover = Color( 1, 1, 1, 1 )
Button/colors/font_color_pressed = Color( 1, 1, 1, 1 )
Button/constants/hseparation = 5
Button/fonts/font = SubResource( 2 )
Button/styles/disabled = ExtResource( 6 )
Button/styles/focus = ExtResource( 4 )
Button/styles/hover = ExtResource( 2 )
Button/styles/normal = ExtResource( 3 )
Button/styles/pressed = ExtResource( 1 )
WindowDialog/colors/title_color = Color( 1, 1, 1, 1 )
WindowDialog/constants/close_h_ofs = 18
WindowDialog/constants/close_v_ofs = 18
WindowDialog/constants/scaleborder_size = 4
WindowDialog/constants/title_height = 20
WindowDialog/fonts/title_font = SubResource( 3 )
WindowDialog/icons/close = null
WindowDialog/icons/close_highlight = null
WindowDialog/styles/panel = SubResource( 4 )
