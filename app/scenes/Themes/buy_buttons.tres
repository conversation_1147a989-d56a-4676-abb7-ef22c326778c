[gd_resource type="Theme" load_steps=8 format=2]

[sub_resource type="DynamicFontData" id=1]
font_path = "res://assets/fonts/arial_narrow_7.ttf"

[sub_resource type="DynamicFont" id=6]
size = 34
outline_size = 1
use_filter = true
font_data = SubResource( 1 )

[sub_resource type="StyleBoxEmpty" id=7]

[sub_resource type="StyleBoxEmpty" id=2]

[sub_resource type="StyleBoxEmpty" id=3]

[sub_resource type="StyleBoxEmpty" id=4]

[sub_resource type="StyleBoxEmpty" id=5]

[resource]
Button/colors/font_color = Color( 0.223529, 0.105882, 0.654902, 1 )
Button/colors/font_color_disabled = Color( 0, 0, 0, 1 )
Button/colors/font_color_focus = Color( 1, 0.407843, 0.913725, 1 )
Button/colors/font_color_hover = Color( 1, 0.407843, 0.913725, 1 )
Button/colors/font_color_hover_pressed = Color( 0.529412, 0.12549, 0.470588, 0.827451 )
Button/colors/font_color_pressed = Color( 0.529412, 0.12549, 0.470588, 0.827451 )
Button/colors/icon_color_disabled = Color( 1, 1, 1, 0.4 )
Button/colors/icon_color_focus = Color( 1, 1, 1, 1 )
Button/colors/icon_color_hover = Color( 1, 1, 1, 1 )
Button/colors/icon_color_hover_pressed = Color( 1, 1, 1, 1 )
Button/colors/icon_color_normal = Color( 1, 1, 1, 1 )
Button/colors/icon_color_pressed = Color( 1, 1, 1, 1 )
Button/constants/hseparation = 2
Button/fonts/font = SubResource( 6 )
Button/styles/disabled = SubResource( 7 )
Button/styles/focus = SubResource( 2 )
Button/styles/hover = SubResource( 3 )
Button/styles/normal = SubResource( 4 )
Button/styles/pressed = SubResource( 5 )
