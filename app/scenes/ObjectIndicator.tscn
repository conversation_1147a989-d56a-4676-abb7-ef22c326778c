[gd_scene load_steps=7 format=2]

[ext_resource path="res://assets/detector_green.png" type="Texture" id=1]
[ext_resource path="res://scripts/ObjectIndicator.gd" type="Script" id=2]
[ext_resource path="res://assets/detector_yellow.png" type="Texture" id=3]
[ext_resource path="res://assets/detector_red.png" type="Texture" id=4]

[sub_resource type="SpriteFrames" id=1]
animations = [ {
"frames": [ ExtResource( 1 ), ExtResource( 3 ), ExtResource( 4 ) ],
"loop": true,
"name": "default",
"speed": 10.0
} ]

[sub_resource type="Animation" id=2]
resource_name = "blink"
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("AnimatedSprite:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 1,
"values": [ true, false, true ]
}

[node name="ObjectIndicator" type="Node2D"]
scale = Vector2( 0.8, 0.8 )
script = ExtResource( 2 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
visible = false
modulate = Color( 1, 1, 1, 0.784314 )
frames = SubResource( 1 )

[node name="Label" type="Label" parent="."]
modulate = Color( 0, 1, 0.133333, 0.784314 )
margin_left = -59.0
margin_top = 13.0
margin_right = 59.0
margin_bottom = 27.0
grow_horizontal = 2
text = "object"
align = 1
valign = 1
uppercase = true

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "blink"
playback_speed = 2.0
anims/blink = SubResource( 2 )
