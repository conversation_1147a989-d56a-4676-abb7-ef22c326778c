[gd_resource type="ShaderMaterial" load_steps=2 format=2]

[sub_resource type="Shader" id=4]
code = "shader_type canvas_item;

uniform int direction = 0;

void fragment() {
	vec4 t = texture(TEXTURE, UV);
	if(direction!=0)
	{
		COLOR = t;
		COLOR.rgb = vec3((direction==-1 ? 1.0-UV.x: UV.x))*t.rgb;
		COLOR.rgb = (COLOR.rgb + t.rgb*1.5) / 2.5;
	}
	else
	{
		COLOR = t;
	}
}"

[resource]
shader = SubResource( 4 )
shader_param/direction = -1
