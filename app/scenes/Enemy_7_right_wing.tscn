[gd_scene load_steps=13 format=2]

[ext_resource path="res://scripts/Enemy_7_wing.gd" type="Script" id=3]
[ext_resource path="res://addons/kenney_particle_pack/spark_04.png" type="Texture" id=4]
[ext_resource path="res://assets/boss-right-wing-damaged.png" type="Texture" id=5]
[ext_resource path="res://assets/boss-right-wing.png" type="Texture" id=6]

[sub_resource type="RectangleShape2D" id=2]
extents = Vector2( 21, 8.5 )

[sub_resource type="SpriteFrames" id=3]
animations = [ {
"frames": [ ExtResource( 6 ), ExtResource( 5 ) ],
"loop": true,
"name": "default",
"speed": 5.0
} ]

[sub_resource type="Gradient" id=58]
offsets = PoolRealArray( 0.002849, 0.381766, 0.666667 )
colors = PoolColorArray( 0.440329, 0.827778, 0.997559, 1, 0, 0.976562, 1, 1, 0, 1, 0.227451, 0 )

[sub_resource type="GradientTexture" id=60]
gradient = SubResource( 58 )

[sub_resource type="ParticlesMaterial" id=59]
emission_shape = 1
emission_sphere_radius = 10.65
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 0.05
scale_random = 0.05
color_ramp = SubResource( 60 )

[sub_resource type="Gradient" id=62]
offsets = PoolRealArray( 0.002849, 0.381766, 0.666667 )
colors = PoolColorArray( 0.997559, 0.440329, 0.962732, 1, 1, 0, 0, 1, 0, 1, 0.227451, 0 )

[sub_resource type="GradientTexture" id=64]
gradient = SubResource( 62 )

[sub_resource type="ParticlesMaterial" id=63]
emission_shape = 2
emission_box_extents = Vector3( 200, 1, 1 )
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 0.1
scale_random = 0.1
color_ramp = SubResource( 64 )

[node name="Wing" type="Area2D"]
script = ExtResource( 3 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2( 39, -0.5 )
shape = SubResource( 2 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
position = Vector2( 41, 0 )
frames = SubResource( 3 )

[node name="Particles2D" type="Particles2D" parent="."]
modulate = Color( 1, 1, 1, 0.313726 )
position = Vector2( 28, 1 )
emitting = false
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 59 )
texture = ExtResource( 4 )

[node name="Scratch" type="Particles2D" parent="."]
modulate = Color( 1, 1, 1, 0.635294 )
position = Vector2( 39, 1 )
rotation = -0.0191986
scale = Vector2( 0.1, 0.048 )
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 63 )
texture = ExtResource( 4 )
