tool
extends Area2D

enum BrickTypeEnum {
	STRONG,
	WEAK,
	STATIC,
	GLA<PERSON>
}

export(BrickTypeEnum) var BrickType = BrickTypeEnum.STATIC setget setBrickType, getBrickType

var hitCnt = 0

func getMaxHitPoints():
	var cfg = {
		BrickTypeEnum.STRONG: 10 + Global.GameScene.getProperDifficulty() * 10,
		BrickTypeEnum.WEAK: 10,
		BrickTypeEnum.STATIC: 1000000,
		BrickTypeEnum.GLASS: 1000000
	}

	return cfg[BrickType]

func addHitArea():
	var _c = connect("area_entered",self,"_on_hit")


func _ready():
	Global.setTimeout(self,1,self, "addHitArea")
	$AnimatedSprite.frame = getFrameByAnimation()

func getFrameByAnimation():
	match BrickType:
		BrickTypeEnum.STRONG:
			return 0
		BrickTypeEnum.WEAK:
			return 3
		BrickTypeEnum.STATIC:
			return 4
		BrickTypeEnum.GLASS:
			return 5
		_:
			return 0

func getBrickType():
	return BrickType

func setBrickType(brickType):
	BrickType = brickType
	$AnimatedSprite.frame = getFrameByAnimation()

func destroy():
	# todo: add animation, explosion etc
	if BrickType == BrickTypeEnum.STRONG:
		$Explosion.emitting = true
		$Explosion.modulate.a = 2

	self.modulate.a = 0.2
	# setBrickType(BrickTypeEnum.GLASS)
	queue_free()

	Global.GameScene.call_deferred("handleLoot",self.global_position, randi()%3!=2)



func causeDamage(points):
	hitCnt += points

	if(hitCnt >= getMaxHitPoints()):
		destroy()
		return false

	if(BrickType == BrickTypeEnum.STRONG):
		var percent = float(((float(getMaxHitPoints()-hitCnt))/float(getMaxHitPoints()))*100.0);
		percent = int(percent / 10.0)
		if(percent<7):
			$AnimatedSprite.frame = 1

		if(percent<5):
			$AnimatedSprite.frame = 2



func _on_hit(area):

	if(!Global.GameScene.isCurrentBossReady):
		return false

	if(BrickType==BrickTypeEnum.GLASS):
		return false

	if(area.has_method("getDamagePoint")):
		Global.callIfExists(area,"destroy")
		# area.destroy()
		if(area.has_method("getDamagePoint")):
			causeDamage(area.getDamagePoint())
