[gd_scene load_steps=10 format=2]

[ext_resource path="res://scenes/Bosses/Boss1/Brick.tscn" type="PackedScene" id=1]
[ext_resource path="res://assets/enemy2.png" type="Texture" id=2]
[ext_resource path="res://scenes/DiscoBall.tscn" type="PackedScene" id=3]
[ext_resource path="res://scenes/Bosses/Boss1/Boss1.gd" type="Script" id=4]

[sub_resource type="AtlasTexture" id=1]
atlas = ExtResource( 2 )
region = Rect2( 0, 0, 22, 22 )

[sub_resource type="AtlasTexture" id=2]
atlas = ExtResource( 2 )
region = Rect2( 22, 0, 22, 22 )

[sub_resource type="SpriteFrames" id=3]
animations = [ {
"frames": [ SubResource( 1 ), SubResource( 2 ) ],
"loop": true,
"name": "default",
"speed": 2.0
} ]

[sub_resource type="Animation" id=5]
resource_name = "Boss1Dance"
length = 2.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("AnimatedSprite:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.6, 1.4, 2 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -10.0, 10.0, 0.0 ]
}

[sub_resource type="CapsuleShape2D" id=4]
radius = 30.0

[node name="Boss1" type="Area2D"]
position = Vector2( 10, -30 )
script = ExtResource( 4 )

[node name="Bricks" type="Node2D" parent="."]

[node name="BG" type="Node2D" parent="Bricks"]

[node name="ColorRect" type="ColorRect" parent="Bricks/BG"]
self_modulate = Color( 1, 0, 0, 0.0784314 )
margin_left = -135.0
margin_top = 25.0
margin_right = 115.0
margin_bottom = 105.0

[node name="ColorRect6" type="ColorRect" parent="Bricks/BG"]
self_modulate = Color( 1, 0, 0, 0.0784314 )
margin_left = -185.0
margin_top = 105.0
margin_right = 165.0
margin_bottom = 175.0

[node name="ColorRect2" type="ColorRect" parent="Bricks/BG"]
self_modulate = Color( 1, 0, 0, 0.0980392 )
margin_left = -115.0
margin_top = 5.0
margin_right = 95.0
margin_bottom = 25.0

[node name="ColorRect3" type="ColorRect" parent="Bricks/BG"]
self_modulate = Color( 1, 0, 0, 0.117647 )
margin_left = -105.0
margin_top = -5.0
margin_right = 85.0
margin_bottom = 5.0

[node name="ColorRect4" type="ColorRect" parent="Bricks/BG"]
self_modulate = Color( 1, 0, 0, 0.156863 )
margin_left = -95.0
margin_top = -15.0
margin_right = 75.0
margin_bottom = -5.0

[node name="ColorRect5" type="ColorRect" parent="Bricks/BG"]
self_modulate = Color( 1, 0, 0, 0.196078 )
margin_left = -75.0
margin_top = -25.0
margin_right = 55.0
margin_bottom = -15.0

[node name="ScrollingBricks" type="Node2D" parent="Bricks"]

[node name="Brick18" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -120, 110 )
BrickType = 1

[node name="Brick19" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -110, 110 )
BrickType = 1

[node name="Brick21" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -90, 110 )
BrickType = 1

[node name="Brick24" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -100, 110 )
BrickType = 1

[node name="Brick38" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -120, 120 )
BrickType = 1

[node name="Brick39" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -110, 120 )
BrickType = 1

[node name="Brick40" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -100, 120 )
BrickType = 1

[node name="Brick41" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -90, 120 )
BrickType = 1

[node name="Brick53" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -120, 130 )
BrickType = 1

[node name="Brick54" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -110, 130 )
BrickType = 1

[node name="Brick55" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -100, 130 )
BrickType = 1

[node name="Brick56" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -90, 130 )
BrickType = 1

[node name="Brick74" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 100, 110 )
BrickType = 1

[node name="Brick79" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 70, 110 )
BrickType = 1

[node name="Brick80" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 80, 110 )
BrickType = 1

[node name="Brick82" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 90, 110 )
BrickType = 1

[node name="Brick83" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 70, 120 )
BrickType = 1

[node name="Brick84" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 80, 120 )
BrickType = 1

[node name="Brick85" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 90, 120 )
BrickType = 1

[node name="Brick86" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 100, 120 )
BrickType = 1

[node name="Brick92" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 70, 130 )
BrickType = 1

[node name="Brick93" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 80, 130 )
BrickType = 1

[node name="Brick94" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 90, 130 )
BrickType = 1

[node name="Brick95" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 100, 130 )
BrickType = 1

[node name="Brick110" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 50, 110 )
BrickType = 1

[node name="Brick111" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 60, 110 )
BrickType = 1

[node name="Brick112" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -80, 110 )
BrickType = 1

[node name="Brick113" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -70, 110 )
BrickType = 1

[node name="Brick114" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 40, 110 )
BrickType = 1

[node name="Brick115" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -80, 120 )
BrickType = 1

[node name="Brick116" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -70, 120 )
BrickType = 1

[node name="Brick117" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 40, 120 )
BrickType = 1

[node name="Brick118" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 50, 120 )
BrickType = 1

[node name="Brick119" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 60, 120 )
BrickType = 1

[node name="Brick120" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -80, 130 )
BrickType = 1

[node name="Brick121" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -70, 130 )
BrickType = 1

[node name="Brick122" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 40, 130 )
BrickType = 1

[node name="Brick123" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 50, 130 )
BrickType = 1

[node name="Brick124" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 60, 130 )
BrickType = 1

[node name="Brick20" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -20, 110 )
BrickType = 1

[node name="Brick162" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -10, 110 )
BrickType = 1

[node name="Brick163" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 10, 110 )
BrickType = 1

[node name="Brick205" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 0, 110 )
BrickType = 1

[node name="Brick206" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -20, 120 )
BrickType = 1

[node name="Brick207" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -10, 120 )
BrickType = 1

[node name="Brick208" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 0, 120 )
BrickType = 1

[node name="Brick209" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 10, 120 )
BrickType = 1

[node name="Brick210" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -20, 130 )
BrickType = 1

[node name="Brick211" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -10, 130 )
BrickType = 1

[node name="Brick212" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 0, 130 )
BrickType = 1

[node name="Brick213" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 10, 130 )
BrickType = 1

[node name="Brick218" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 20, 110 )
BrickType = 1

[node name="Brick219" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 30, 110 )
BrickType = 1

[node name="Brick220" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 20, 120 )
BrickType = 1

[node name="Brick221" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 30, 120 )
BrickType = 1

[node name="Brick222" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 20, 130 )
BrickType = 1

[node name="Brick223" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( 30, 130 )
BrickType = 1

[node name="Brick238" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -60, 110 )
BrickType = 1

[node name="Brick239" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -50, 110 )
BrickType = 1

[node name="Brick240" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -30, 110 )
BrickType = 1

[node name="Brick241" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -40, 110 )
BrickType = 1

[node name="Brick242" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -60, 120 )
BrickType = 1

[node name="Brick243" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -50, 120 )
BrickType = 1

[node name="Brick244" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -40, 120 )
BrickType = 1

[node name="Brick245" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -30, 120 )
BrickType = 1

[node name="Brick246" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -60, 130 )
BrickType = 1

[node name="Brick247" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -50, 130 )
BrickType = 1

[node name="Brick248" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -40, 130 )
BrickType = 1

[node name="Brick249" parent="Bricks/ScrollingBricks" instance=ExtResource( 1 )]
position = Vector2( -30, 130 )
BrickType = 1

[node name="DescruptibleBricks" type="Node2D" parent="Bricks"]

[node name="Brick68" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -120, 160 )
BrickType = 0

[node name="Brick69" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -110, 160 )
BrickType = 0

[node name="Brick70" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -100, 160 )
BrickType = 0

[node name="Brick101" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 70, 160 )
BrickType = 0

[node name="Brick102" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 80, 160 )
BrickType = 0

[node name="Brick103" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 90, 160 )
BrickType = 0

[node name="Brick104" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 100, 160 )
BrickType = 0

[node name="Brick126" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -70, 160 )
BrickType = 0

[node name="Brick128" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 50, 160 )
BrickType = 0

[node name="Brick156" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -120, 170 )
BrickType = 0

[node name="Brick157" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -110, 170 )
BrickType = 0

[node name="Brick158" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -100, 170 )
BrickType = 0

[node name="Brick175" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 70, 170 )
BrickType = 0

[node name="Brick176" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 80, 170 )
BrickType = 0

[node name="Brick177" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 90, 170 )
BrickType = 0

[node name="Brick178" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 100, 170 )
BrickType = 0

[node name="Brick189" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -85, 165 )
scale = Vector2( 2, 2 )
BrickType = 0

[node name="Brick190" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -70, 170 )
BrickType = 0

[node name="Brick192" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 50, 170 )
BrickType = 0

[node name="Brick193" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 60, 170 )
BrickType = 0

[node name="Brick215" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -10, 160 )
BrickType = 0

[node name="Brick216" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 0, 160 )
BrickType = 0

[node name="Brick217" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 10, 160 )
BrickType = 0

[node name="Brick224" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 20, 160 )
BrickType = 0

[node name="Brick227" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -10, 170 )
BrickType = 0

[node name="Brick228" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 0, 170 )
BrickType = 0

[node name="Brick229" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 10, 170 )
BrickType = 0

[node name="Brick252" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -40, 160 )
BrickType = 0

[node name="Brick93" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -100, 150 )
BrickType = 0

[node name="Brick94" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -90, 150 )
BrickType = 0

[node name="Brick110" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 70, 150 )
BrickType = 0

[node name="Brick113" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 100, 150 )
BrickType = 0

[node name="Brick209" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -80, 150 )
BrickType = 0

[node name="Brick210" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -70, 150 )
BrickType = 0

[node name="Brick211" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 40, 150 )
BrickType = 0

[node name="Brick212" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 50, 150 )
BrickType = 0

[node name="Brick213" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 60, 150 )
scale = Vector2( 1, 3 )
BrickType = 0

[node name="Brick218" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -20, 150 )
BrickType = 0

[node name="Brick221" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 10, 150 )
BrickType = 0

[node name="Brick238" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 20, 150 )
BrickType = 0

[node name="Brick239" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 30, 150 )
BrickType = 0

[node name="Brick263" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -55, 155 )
scale = Vector2( 2, 2 )
BrickType = 0

[node name="Brick266" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -5, 145 )
scale = Vector2( 2, 2 )
BrickType = 0

[node name="Brick267" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -25, 165 )
scale = Vector2( 2, 2 )
BrickType = 0

[node name="Brick268" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 35, 165 )
scale = Vector2( 2, 2 )
BrickType = 0

[node name="Brick269" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 85, 145 )
scale = Vector2( 2, 2 )
BrickType = 0

[node name="Brick264" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -40, 150 )
BrickType = 0

[node name="Brick265" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -30, 150 )
BrickType = 0

[node name="Brick254" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -60, 170 )
BrickType = 0

[node name="Brick255" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -50, 170 )
BrickType = 0

[node name="Brick256" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -40, 170 )
BrickType = 0

[node name="Brick234" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 20, 170 )
BrickType = 0

[node name="Brick160" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -115, 145 )
scale = Vector2( 2, 2 )
BrickType = 0

[node name="Brick162" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -100, 140 )
BrickType = 0

[node name="Brick179" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 70, 140 )
BrickType = 0

[node name="Brick182" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 100, 140 )
BrickType = 0

[node name="Brick194" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -80, 140 )
scale = Vector2( 3, 1 )
BrickType = 0

[node name="Brick196" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 40, 140 )
BrickType = 0

[node name="Brick197" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 50, 140 )
BrickType = 0

[node name="Brick230" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -20, 140 )
BrickType = 0

[node name="Brick233" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 10, 140 )
BrickType = 0

[node name="Brick258" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -60, 140 )
BrickType = 0

[node name="Brick259" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -50, 140 )
BrickType = 0

[node name="Brick260" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -40, 140 )
BrickType = 0

[node name="Brick261" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -30, 140 )
BrickType = 0

[node name="Brick236" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 20, 140 )
BrickType = 0

[node name="Brick237" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 30, 140 )
BrickType = 0

[node name="Brick15" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -110, 100 )
BrickType = 0

[node name="Brick97" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -120, 100 )
BrickType = 0

[node name="Brick16" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -100, 100 )
BrickType = 0

[node name="Brick35" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -90, 100 )
BrickType = 0

[node name="Brick36" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -80, 100 )
BrickType = 0

[node name="Brick37" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -70, 100 )
BrickType = 0

[node name="Brick38" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -60, 100 )
BrickType = 0

[node name="Brick39" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -50, 100 )
BrickType = 0

[node name="Brick40" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -40, 100 )
BrickType = 0

[node name="Brick41" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -30, 100 )
BrickType = 0

[node name="Brick50" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -20, 100 )
BrickType = 0

[node name="Brick51" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( -10, 100 )
BrickType = 0

[node name="Brick76" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 0, 100 )
BrickType = 0

[node name="Brick77" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 10, 100 )
BrickType = 0

[node name="Brick88" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 20, 100 )
BrickType = 0

[node name="Brick89" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 30, 100 )
BrickType = 0

[node name="Brick98" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 40, 100 )
BrickType = 0

[node name="Brick120" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 50, 100 )
BrickType = 0

[node name="Brick121" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 60, 100 )
BrickType = 0

[node name="Brick124" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 70, 100 )
BrickType = 0

[node name="Brick222" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 80, 100 )
BrickType = 0

[node name="Brick241" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 90, 100 )
BrickType = 0

[node name="Brick242" parent="Bricks/DescruptibleBricks" instance=ExtResource( 1 )]
position = Vector2( 100, 100 )
BrickType = 0

[node name="Brick" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( -10, -30 )
scale = Vector2( 13, 1 )
BrickType = 3

[node name="Brick2" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( 60, -20 )
BrickType = 3

[node name="Brick3" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( 70, -20 )
BrickType = 3

[node name="Brick4" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( -80, -20 )
BrickType = 3

[node name="Brick5" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( -90, -20 )
BrickType = 3

[node name="Brick6" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( -100, -10 )
BrickType = 3

[node name="Brick7" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( -110, 0 )
BrickType = 3

[node name="Brick9" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( 80, -10 )
BrickType = 3

[node name="Brick10" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( 90, 0 )
BrickType = 3

[node name="Brick13" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( 125, 100 )
scale = Vector2( 4, 1 )

[node name="Brick14" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( -145, 100 )
scale = Vector2( 4, 1 )

[node name="Brick244" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( 120, 90 )

[node name="Brick22" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( -170, 110 )

[node name="Brick25" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( -180, 110 )

[node name="Brick26" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( 160, 110 )

[node name="Brick43" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( 170, 140 )
scale = Vector2( 1, 5 )

[node name="Brick44" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( -190, 140 )
scale = Vector2( 1, 5 )

[node name="Brick78" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( 150, 110 )

[node name="Brick147" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( -160, 170 )
scale = Vector2( 6, 1 )

[node name="Brick148" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( 140, 170 )
scale = Vector2( 6, 1 )

[node name="Brick131" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( -120, 20 )
BrickType = 3

[node name="Brick132" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( -120, 10 )
BrickType = 3

[node name="Brick134" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( 100, 20 )
BrickType = 3

[node name="Brick135" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( 100, 10 )
BrickType = 3

[node name="Brick8" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( -130, 54 )
scale = Vector2( 1, 7 )
BrickType = 3

[node name="Brick11" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( 110, 54 )
scale = Vector2( 1, 7 )
BrickType = 3

[node name="Brick243" parent="Bricks" instance=ExtResource( 1 )]
position = Vector2( -140, 90 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
position = Vector2( 52, 58 )
scale = Vector2( 4.54545, 4.54545 )
frames = SubResource( 3 )
playing = true

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "Boss1Dance"
anims/Boss1Dance = SubResource( 5 )

[node name="AlienCollisionShape" type="CollisionShape2D" parent="."]
position = Vector2( 50, 54 )
shape = SubResource( 4 )

[node name="DiscoBall" parent="." instance=ExtResource( 3 )]
position = Vector2( -50, 30 )
