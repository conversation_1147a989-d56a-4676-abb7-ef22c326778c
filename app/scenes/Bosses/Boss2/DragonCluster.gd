extends "res://scenes/Bosses/Boss2/DragonPartBase.gd"

var Bullet = preload("res://scenes/EnemyBullet_1.tscn")

var doAllowBullet = false

func allowBullet():
	doAllowBullet = true

func getSprite():
	return $"Sprite"

func initialize():
	Global.setTimeout(self,randf()*5,self,"allowBullet")

func fireBullet(_doForce = false):

	if(!canFireBullet(global_position)):
		return

	if(isDead):
		return

	var bullet = Bullet.instance()
	bullet.position = self.global_position+Vector2(0, 16);
	bullet.z_index  = z_index-1;
	Global.GameScene.add_child(bullet);

	pass

func _process(_delta):

	if(!doAllowBullet):
		return

	if(!Global.doThrottle("DragonCluster_"+self.name, 1000+randi()%2000)):
		fireBullet()
