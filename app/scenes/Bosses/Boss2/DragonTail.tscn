[gd_scene load_steps=22 format=2]

[ext_resource path="res://assets/Boss - Dragon - Tail.png" type="Texture" id=1]
[ext_resource path="res://scenes/Bosses/Boss2/DragonTail.gd" type="Script" id=2]
[ext_resource path="res://shaders/hit.tres" type="Shader" id=3]
[ext_resource path="res://addons/kenney_particle_pack/magic_05.png" type="Texture" id=4]
[ext_resource path="res://addons/kenney_particle_pack/spark_02.png" type="Texture" id=6]
[ext_resource path="res://addons/kenney_particle_pack/spark_03.png" type="Texture" id=7]
[ext_resource path="res://addons/kenney_particle_pack/spark_04.png" type="Texture" id=8]

[sub_resource type="RectangleShape2D" id=3]
extents = Vector2( 22.5, 7.5 )

[sub_resource type="RectangleShape2D" id=4]
extents = Vector2( 42.585, 6.70295 )

[sub_resource type="CircleShape2D" id=5]
radius = 17.74

[sub_resource type="Gradient" id=11]
offsets = PoolRealArray( 0.002849, 0.381766, 0.666667 )
colors = PoolColorArray( 0.440329, 0.827778, 0.997559, 1, 0, 0.976562, 1, 1, 0, 1, 0.227451, 0 )

[sub_resource type="GradientTexture" id=12]
gradient = SubResource( 11 )

[sub_resource type="ParticlesMaterial" id=17]
emission_shape = 1
emission_sphere_radius = 10.65
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 0.01
scale_random = 0.06
color_ramp = SubResource( 12 )

[sub_resource type="ParticlesMaterial" id=14]
emission_shape = 1
emission_sphere_radius = 10.65
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 0.01
scale_random = 0.02
color_ramp = SubResource( 12 )

[sub_resource type="ShaderMaterial" id=6]
shader = ExtResource( 3 )
shader_param/active = false
shader_param/gray = false

[sub_resource type="Animation" id=1]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("../../TailCollision1:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 23, 25 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("../../TailCollision1:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("../../TailCollision2:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 69, 25 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("../../TailCollision2:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("../../TailCollision2:scale")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("../../TailCollision3:position")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 130, 44 ) ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("../../TailCollision3:rotation_degrees")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 22.2997 ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("../../TailCollision3:scale")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ) ]
}
tracks/8/type = "value"
tracks/8/path = NodePath("../../TailEndArea/TailEndCollision:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 160, 56 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("../../TailEndArea/TailEndCollision:rotation_degrees")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 22.2997 ]
}

[sub_resource type="Animation" id=2]
resource_name = "TailWiggle"
length = 8.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("Bone2D:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 0.5, 1, 1, 2 ),
"update": 0,
"values": [ 0.0, -20.0, 0.0, 30.0 ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Bone2D/Bone2D2:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0.1, 2.2, 4.2, 6.2 ),
"transitions": PoolRealArray( 0.5, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -20.0, 0.0, 20.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("Bone2D/Bone2D2/Bone2D3:rotation_degrees")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0.3, 2.5, 4.5, 6.5 ),
"transitions": PoolRealArray( 0.5, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -20.0, 0.0, 20.0 ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("Bone2D/Bone2D2/Bone2D3/Bone2D4:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0.6, 2.9, 4.9, 7 ),
"transitions": PoolRealArray( 0.5, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -20.0, 0.0, 20.0 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("Bone2D/Bone2D2/Bone2D3/Bone2D4/Bone2D5:rotation_degrees")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 1.1, 3.3, 5.4, 7.4 ),
"transitions": PoolRealArray( 0.5, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -20.0, 0.0, 20.0 ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("Bone2D/Bone2D2/Bone2D3/Bone2D4/Bone2D5/Bone2D6:rotation_degrees")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 1.7, 3.8, 5.9, 7.9 ),
"transitions": PoolRealArray( 0.5, 1, -2, 1 ),
"update": 0,
"values": [ 0.0, -10.0, 0.0, 10.0 ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("../../TailCollision1:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0, 2, 4, 6, 8 ),
"transitions": PoolRealArray( 0.5, 1, 1, 2, 1 ),
"update": 0,
"values": [ Vector2( 23, 25 ), Vector2( 23, 17 ), Vector2( 23, 24 ), Vector2( 18, 34 ), Vector2( 23, 25 ) ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("../../TailCollision1:rotation_degrees")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0, 2, 4, 6 ),
"transitions": PoolRealArray( 0.5, 1, 1, 2 ),
"update": 0,
"values": [ 0.0, -18.2044, 1.44172, 28.173 ]
}
tracks/8/type = "value"
tracks/8/path = NodePath("../../TailCollision2:position")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/imported = false
tracks/8/enabled = true
tracks/8/keys = {
"times": PoolRealArray( 0, 0.7, 1, 1.8, 2, 3.7, 4.9, 6, 6.2, 6.5, 7.2, 8 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 69, 25 ), Vector2( 66, 8 ), Vector2( 63, 2 ), Vector2( 62, -6 ), Vector2( 61, -6 ), Vector2( 68, 18 ), Vector2( 64, 43 ), Vector2( 48, 61 ), Vector2( 48, 61 ), Vector2( 51, 59 ), Vector2( 58, 48 ), Vector2( 69, 25 ) ]
}
tracks/9/type = "value"
tracks/9/path = NodePath("../../TailCollision2:rotation_degrees")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/imported = false
tracks/9/enabled = true
tracks/9/keys = {
"times": PoolRealArray( 0, 1, 2, 3.7, 4.9, 6, 6.5, 7.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, -30.7045, -41.2461, -11.1611, 16.519, 54.9191, 46.6555, 26.54 ]
}
tracks/10/type = "value"
tracks/10/path = NodePath("../../TailCollision2:scale")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/imported = false
tracks/10/enabled = true
tracks/10/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ) ]
}
tracks/11/type = "value"
tracks/11/path = NodePath("../../TailCollision3:position")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/imported = false
tracks/11/enabled = true
tracks/11/keys = {
"times": PoolRealArray( 0, 0.6, 1.5, 2.2, 3.3, 4.1, 4.9, 5.8, 6, 6.5, 7.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 130, 44 ), Vector2( 128, -8.00001 ), Vector2( 95, -54 ), Vector2( 85, -59 ), Vector2( 110, -27 ), Vector2( 128, 14 ), Vector2( 123, 66 ), Vector2( 80, 109 ), Vector2( 71, 119 ), Vector2( 65, 124 ), Vector2( 92, 102 ) ]
}
tracks/12/type = "value"
tracks/12/path = NodePath("../../TailCollision3:rotation_degrees")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/imported = false
tracks/12/enabled = true
tracks/12/keys = {
"times": PoolRealArray( 0, 0.6, 1.5, 2.2, 3.3, 4.1, 4.9, 5.8, 6, 6.5, 7.2 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ 22.2997, -15.974, -65.9833, -82.1669, -50.35, -14.5946, 23.3488, 68.5749, 80.5949, 89.1213, 78.6592 ]
}
tracks/13/type = "value"
tracks/13/path = NodePath("../../TailCollision3:scale")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/imported = false
tracks/13/enabled = true
tracks/13/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ) ]
}
tracks/14/type = "value"
tracks/14/path = NodePath("../../TailEndArea/TailEndCollision:position")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/imported = false
tracks/14/enabled = true
tracks/14/keys = {
"times": PoolRealArray( 0, 0.3, 0.8, 1.4, 2.2, 3.2, 4, 4.8, 5.4, 6.1, 6.3, 6.8, 7.3, 7.7 ),
"transitions": PoolRealArray( 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 160, 56 ), Vector2( 166, 17 ), Vector2( 152, -45 ), Vector2( 116, -88 ), Vector2( 86, -97 ), Vector2( 126, -62 ), Vector2( 158, -9 ), Vector2( 157, 70 ), Vector2( 126, 123 ), Vector2( 70, 157 ), Vector2( 65, 158 ), Vector2( 77, 149 ), Vector2( 107, 129 ), Vector2( 140, 95 ) ]
}
tracks/15/type = "value"
tracks/15/path = NodePath("../../TailEndArea/TailEndCollision:rotation_degrees")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/imported = false
tracks/15/enabled = true
tracks/15/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 22.2997 ]
}
tracks/16/type = "method"
tracks/16/path = NodePath("../..")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/imported = false
tracks/16/enabled = true
tracks/16/keys = {
"times": PoolRealArray( 5.1 ),
"transitions": PoolRealArray( 1 ),
"values": [ {
"args": [  ],
"method": "fireBullet"
} ]
}
tracks/17/type = "value"
tracks/17/path = NodePath("../../TailEndArea/TailEndCollision/TailIndicator:modulate")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/imported = false
tracks/17/enabled = true
tracks/17/keys = {
"times": PoolRealArray( 2.1, 4.1, 5.1, 5.4 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 0 ), Color( 1, 1, 1, 1 ), Color( 1, 1, 1, 0 ) ]
}

[sub_resource type="CanvasItemMaterial" id=10]
blend_mode = 2

[sub_resource type="ParticlesMaterial" id=13]
emission_shape = 1
emission_sphere_radius = 10.65
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 0.02
scale_random = 0.06
color_ramp = SubResource( 12 )

[sub_resource type="ParticlesMaterial" id=15]
emission_shape = 1
emission_sphere_radius = 10.65
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 0.05
scale_random = 0.08
color_ramp = SubResource( 12 )

[sub_resource type="ParticlesMaterial" id=16]
emission_shape = 1
emission_sphere_radius = 10.65
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 0.1
scale_random = 0.1
color_ramp = SubResource( 12 )

[node name="DragonTail" type="Area2D"]
script = ExtResource( 2 )

[node name="TailCollision1" type="CollisionShape2D" parent="."]
position = Vector2( 23, 25 )
shape = SubResource( 3 )

[node name="TailCollision2" type="CollisionShape2D" parent="."]
position = Vector2( 69, 25 )
shape = SubResource( 3 )

[node name="TailCollision3" type="CollisionShape2D" parent="."]
position = Vector2( 130, 44 )
rotation = 0.389203
shape = SubResource( 4 )

[node name="TailEndArea" type="Area2D" parent="."]

[node name="TailEndCollision" type="CollisionShape2D" parent="TailEndArea"]
position = Vector2( 160, 56 )
rotation = 0.389203
shape = SubResource( 5 )

[node name="TailIndicator" type="Sprite" parent="TailEndArea/TailEndCollision"]
modulate = Color( 1, 1, 1, 0 )
scale = Vector2( 0.2, 0.05 )
texture = ExtResource( 4 )

[node name="Particles2D" type="Particles2D" parent="TailEndArea/TailEndCollision"]
self_modulate = Color( 1, 1, 1, 0.411765 )
position = Vector2( 1.68414, 1.47098 )
rotation = -0.389203
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 17 )
texture = ExtResource( 8 )

[node name="Particles2D2" type="Particles2D" parent="TailEndArea/TailEndCollision/Particles2D"]
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 14 )
texture = ExtResource( 7 )

[node name="Tail" type="Polygon2D" parent="."]
material = SubResource( 6 )
position = Vector2( 0, -1 )
texture = ExtResource( 1 )
skeleton = NodePath("TailSkeleton")
polygon = PoolVector2Array( 4.98177, 6.07533, 1.57959, 10.2066, 1.21507, 23.5723, 4.98177, 30.7412, 13.8518, 30.4982, 30.1337, 30.6197, 44.4714, 30.7412, 58.4447, 30.7412, 70.7169, 30.4982, 83.5966, 30.7412, 96.2333, 30.6197, 111.057, 30.4982, 124.787, 30.4982, 135.723, 30.6197, 141.677, 30.7412, 175.82, 30.7412, 168.894, 25.8809, 164.034, 23.6938, 159.781, 26.9745, 153.341, 26.9745, 152.005, 26.1239, 145.079, 26.367, 135.358, 25.7594, 135.48, 22.2357, 133.657, 22.1142, 132.321, 24.6659, 125, 23.4252, 125, 19.7835, 120.866, 23.8189, 110.039, 21.8504, 111.614, 19.0945, 111.713, 16.2402, 107.776, 17.4213, 106.791, 22.0472, 96.752, 20.1772, 97.5394, 14.3701, 95.374, 14.3701, 92.2244, 19.685, 83.4646, 18.3071, 85.8268, 13.3858, 85.7283, 11.2205, 83.2677, 11.4173, 79.626, 16.6339, 69.8819, 15.2559, 72.7362, 8.16929, 69.8819, 8.26772, 66.3386, 13.9764, 58.563, 12.6969, 59.252, 8.46457, 59.7441, 6.39764, 57.2835, 6.69291, 53.3465, 12.5, 44.0945, 11.3189, 46.5551, 5.80709, 46.3583, 4.52756, 43.7992, 4.52756, 40.5512, 10.6299, 30.5118, 9.05512, 32.2835, 4.03543, 32.5787, 2.06693, 30.6102, 2.46063, 25.2953, 8.85827, 16.5354, 7.38189, 18.5039, 2.55906, 18.6024, 0.688978, 16.2402, 0.885828, 12.1063, 6.69291 )
uv = PoolVector2Array( 4.98177, 6.07533, 1.57959, 10.2066, 1.21507, 23.5723, 4.98177, 30.7412, 13.8518, 30.4982, 30.1337, 30.6197, 44.4714, 30.7412, 58.4447, 30.7412, 70.7169, 30.4982, 83.5966, 30.7412, 96.2333, 30.6197, 111.057, 30.4982, 124.787, 30.4982, 135.723, 30.6197, 141.677, 30.7412, 175.82, 30.7412, 168.894, 25.8809, 164.034, 23.6938, 159.781, 26.9745, 153.341, 26.9745, 152.005, 26.1239, 145.079, 26.367, 135.358, 25.7594, 135.48, 22.2357, 133.657, 22.1142, 132.321, 24.6659, 125, 23.4252, 125, 19.7835, 120.866, 23.8189, 110.039, 21.8504, 111.614, 19.0945, 111.713, 16.2402, 107.776, 17.4213, 106.791, 22.0472, 96.752, 20.1772, 97.5394, 14.3701, 95.374, 14.3701, 92.2244, 19.685, 83.4646, 18.3071, 85.8268, 13.3858, 85.7283, 11.2205, 83.2677, 11.4173, 79.626, 16.6339, 69.8819, 15.2559, 72.7362, 8.16929, 69.8819, 8.26772, 66.3386, 13.9764, 58.563, 12.6969, 59.252, 8.46457, 59.7441, 6.39764, 57.2835, 6.69291, 53.3465, 12.5, 44.0945, 11.3189, 46.5551, 5.80709, 46.3583, 4.52756, 43.7992, 4.52756, 40.5512, 10.6299, 30.5118, 9.05512, 32.2835, 4.03543, 32.5787, 2.06693, 30.6102, 2.46063, 25.2953, 8.85827, 16.5354, 7.38189, 18.5039, 2.55906, 18.6024, 0.688978, 16.2402, 0.885828, 12.1063, 6.69291 )
polygons = [ PoolIntArray( 65, 66, 0, 1, 2, 3, 4, 5, 6, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64 ), PoolIntArray( 6, 52, 51, 50, 49, 48, 47, 46, 45, 44, 43, 8, 7 ), PoolIntArray( 8, 43, 42, 41, 40, 39, 38, 37, 36, 35, 34, 10, 9 ), PoolIntArray( 10, 11, 12, 26, 27, 28, 29, 30, 31, 32, 33, 34 ), PoolIntArray( 26, 12, 13, 14, 21, 22, 23, 24, 25 ), PoolIntArray( 14, 15, 16, 17, 18, 19, 20, 21 ) ]
bones = [ "Bone2D", PoolRealArray( 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 ), "Bone2D/Bone2D2", PoolRealArray( 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "Bone2D/Bone2D2/Bone2D3", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "Bone2D/Bone2D2/Bone2D3/Bone2D4", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "Bone2D/Bone2D2/Bone2D3/Bone2D4/Bone2D5", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ), "Bone2D/Bone2D2/Bone2D3/Bone2D4/Bone2D5/Bone2D6", PoolRealArray( 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 ) ]

[node name="TailAnimation" type="AnimationPlayer" parent="Tail"]
root_node = NodePath("../TailSkeleton")
autoplay = "TailWiggle"
playback_speed = 2.0
anims/RESET = SubResource( 1 )
anims/TailWiggle = SubResource( 2 )

[node name="TailSkeleton" type="Skeleton2D" parent="Tail"]
position = Vector2( -220, -13 )

[node name="Bone2D" type="Bone2D" parent="Tail/TailSkeleton"]
position = Vector2( 221, 31 )
rest = Transform2D( 1, 0, 0, 1, 221, 31 )
__meta__ = {
"_edit_ik_": true
}

[node name="Bone2D2" type="Bone2D" parent="Tail/TailSkeleton/Bone2D"]
position = Vector2( 42, 4 )
rotation = 0.0183719
rest = Transform2D( 1, 0, 0, 1, 42, 4 )
__meta__ = {
"_edit_ik_": true
}

[node name="Bone2D3" type="Bone2D" parent="Tail/TailSkeleton/Bone2D/Bone2D2"]
position = Vector2( 27, 2 )
rotation = 0.0581776
rest = Transform2D( 1, 0, 0, 1, 27, 2 )
__meta__ = {
"_edit_ik_": true
}

[node name="Bone2D4" type="Bone2D" parent="Tail/TailSkeleton/Bone2D/Bone2D2/Bone2D3"]
position = Vector2( 26, 2 )
rotation = 0.1309
rest = Transform2D( 1, 0, 0, 1, 26, 2 )
__meta__ = {
"_edit_ik_": true
}

[node name="Bone2D5" type="Bone2D" parent="Tail/TailSkeleton/Bone2D/Bone2D2/Bone2D3/Bone2D4"]
position = Vector2( 29, 2 )
rotation = 0.225866
rest = Transform2D( 1, 0, 0, 1, 29, 2 )
__meta__ = {
"_edit_ik_": true
}

[node name="Bone2D6" type="Bone2D" parent="Tail/TailSkeleton/Bone2D/Bone2D2/Bone2D3/Bone2D4/Bone2D5"]
position = Vector2( 18, 1 )
rotation = 0.164837
rest = Transform2D( 1, 0, 0, 1, 18, 1 )
default_length = 32.0
__meta__ = {
"_edit_ik_": true
}

[node name="Scratch" type="Sprite" parent="Tail/TailSkeleton/Bone2D/Bone2D2/Bone2D3"]
material = SubResource( 10 )
position = Vector2( 16.0755, 2.54773 )
rotation = 0.159301
scale = Vector2( 0.0703052, 0.0116425 )
z_index = 4096
texture = ExtResource( 6 )

[node name="Scratch2" type="Sprite" parent="Tail/TailSkeleton/Bone2D/Bone2D2/Bone2D3/Scratch"]
material = SubResource( 10 )
position = Vector2( 13.0669, -210.082 )
scale = Vector2( -0.449223, -1.02796 )
texture = ExtResource( 6 )

[node name="Particles2D" type="Particles2D" parent="Tail/TailSkeleton/Bone2D/Bone2D2/Bone2D3/Scratch"]
self_modulate = Color( 1, 1, 1, 0.196078 )
position = Vector2( -18.4865, -173.285 )
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 13 )
texture = ExtResource( 8 )

[node name="Particles2D2" type="Particles2D" parent="Tail/TailSkeleton/Bone2D/Bone2D2/Bone2D3/Scratch/Particles2D"]
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 14 )
texture = ExtResource( 7 )

[node name="Scratch" type="Sprite" parent="Tail/TailSkeleton/Bone2D/Bone2D2"]
material = SubResource( 10 )
position = Vector2( 16.0755, 2.54773 )
rotation = 0.159301
scale = Vector2( 0.119658, 0.0212458 )
z_index = 4096
texture = ExtResource( 6 )

[node name="Scratch2" type="Sprite" parent="Tail/TailSkeleton/Bone2D/Bone2D2/Scratch"]
material = SubResource( 10 )
position = Vector2( 13.0669, -210.082 )
scale = Vector2( -0.449223, -1.02796 )
texture = ExtResource( 6 )

[node name="Particles2D" type="Particles2D" parent="Tail/TailSkeleton/Bone2D/Bone2D2/Scratch"]
modulate = Color( 1, 1, 1, 0.196078 )
position = Vector2( 14.9929, -55.4578 )
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 15 )
texture = ExtResource( 8 )

[node name="Particles2D2" type="Particles2D" parent="Tail/TailSkeleton/Bone2D/Bone2D2/Scratch/Particles2D"]
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 14 )
texture = ExtResource( 7 )

[node name="Scratch" type="Sprite" parent="Tail/TailSkeleton/Bone2D"]
material = SubResource( 10 )
position = Vector2( 21, 4 )
rotation = -0.121125
scale = Vector2( -0.117005, 0.0257203 )
z_index = 4096
texture = ExtResource( 6 )

[node name="Scratch2" type="Sprite" parent="Tail/TailSkeleton/Bone2D/Scratch"]
material = SubResource( 10 )
position = Vector2( 13.0669, -210.082 )
scale = Vector2( -0.449223, -1.02796 )
texture = ExtResource( 6 )

[node name="Particles2D" type="Particles2D" parent="Tail/TailSkeleton/Bone2D/Scratch"]
self_modulate = Color( 1, 1, 1, 0.196078 )
position = Vector2( -63.099, -98.3342 )
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 16 )
texture = ExtResource( 8 )

[node name="Particles2D2" type="Particles2D" parent="Tail/TailSkeleton/Bone2D/Scratch/Particles2D"]
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 14 )
texture = ExtResource( 7 )
