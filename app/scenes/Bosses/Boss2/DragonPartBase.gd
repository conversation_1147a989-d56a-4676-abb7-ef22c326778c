extends Area2D

var Explosion = preload("res://scenes/Explosion_1.tscn")

var maxHealth = 100
var health = 0
var isDead = false

var pointPerHit = 10
var pointPerKill = 100

signal part_died(ogject)

func initialize():
	# overwrite to initialize the enemy
	pass

func destroy():
	boomEffect(null, true)

	for _i in range(0,5):
		Global.setTimeout(self,randf()*2,self,"boomEffect")

	var tween = Global.createTween(self)
	tween.interpolate_property(self,"scale",Vector2(1,1),Vector2(1.5,1.5),0.3, Tween.TRANS_EXPO, Tween.EASE_OUT);
	tween.interpolate_property(self,"modulate:a",1.0,0.0,0.3, Tween.TRANS_LINEAR, Tween.EASE_IN);
	tween.connect("tween_all_completed",self,"queue_free")
	tween.start()

func _fon():
	if(isDead):
		return
	#self.scale = Vector2(1.05,1.05)
	self.getSprite().material.set_shader_param("active", 1)

func _foff():
	if(isDead):
		return
	#self.scale = Vector2(1.0,1.0)
	self.getSprite().material.set_shader_param("active", 0)

func canFireBullet(position):
	return !Global.isOffScreen(position,100) && Global.GameScene.isPlayerReady() && !Global.isOffScreenBottom(position, -200)

func setScratch(_percentage):
	if is_instance_valid($"Scratch"):
		$"Scratch".modulate.a  =(100-_percentage)/100.0

func flash():
	Global.playSound(SoundManager.HitSound, global_position, -10)
	var _percentage = Global.getPercentage(health,maxHealth);
	self.setScratch(_percentage)
	_fon()
	Global.setTimeout(self,0.1,self,"_foff")

# overwrite to get the sprite
func getSprite():
	pass

func _ready():
	maxHealth += 100*Global.GameScene.getProperDifficulty()
	health = maxHealth
	var _c = connect("area_entered",self,"_on_hit")

	if is_instance_valid($"Scratch"):
		$"Scratch".modulate.a = 0

	if(is_instance_valid(self.getSprite())):
		self.getSprite().material = self.getSprite().material.duplicate()

	initialize()

var lastBullet = null

func causeDamage(points):

	if(!Global.doThrottle("DragonDamageThrottle"+str(get_instance_id()),Config.BossDamageThrottle)):
		self.health-=points
		checkHealth()

func _on_hit(target):

	# no hit if enemy is off screen
	if Global.isOffScreenBottom(position, 50):
		return false

	# make sure that one bullet can only hit once
	if lastBullet == target:
		return false

	lastBullet = target

	if(target.has_method("getDamagePoint") && !isDead):
		# cause damage
		causeDamage(target.getDamagePoint())
		# destroy bullet
		Global.callIfExists(target,"destroy")

func boomEffect(pos=null, spawnCrystal = false):
	var explosion = Explosion.instance()
	explosion.explosionColorMod = Color(1.0,0.0,0.0,1)
	var _t = pos if pos != null else global_position
	explosion.position = _t + Vector2(rand_range(-30,30),rand_range(-30,30))
	explosion.z_index  = z_index+1;
	explosion.scale = Vector2(randf()*2,randf()*2)
	Global.GameScene.add_child(explosion);

	if(spawnCrystal):
		for _i in range(5):
			Global.GameScene.spawnCrystal(global_position,Global.CrystalType.c5);
			Global.GameScene.spawnCrystal(global_position,Global.CrystalType.c10);
			Global.GameScene.spawnCrystal(global_position,Global.CrystalType.c20);

func die():

	self.getSprite().material.set_shader_param("active", 1)
	self.getSprite().material.set_shader_param("gray", 1)

	boomEffect()

	for _i in range(0,3):
		Global.setTimeout(self,randf()*1,self,"boomEffect")

	isDead = true;
	emit_signal("part_died", self)

func checkHealth():
	if(self.health <= 0):
		# give points to the user
		#Global.GameScene.score+=pointPerKill
		Global.GameScene.addScore(pointPerKill, position)
		Global.GameScene.call_deferred("handleLoot",self.global_position)
		die()
	else:
		#taking damage
		flash()

func isDragonPart():
	return true
