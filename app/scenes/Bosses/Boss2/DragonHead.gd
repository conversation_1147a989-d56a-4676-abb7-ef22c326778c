extends "res://scenes/Bosses/Boss2/DragonPartBase.gd"

func getSprite():
	return $"Sprite"

var Bullet = preload("res://scenes/EnemyBullet_3.tscn")

func fireBullet(doForce = false):

	if(!canFireBullet($"Mouth".global_position)):
		return

	if(isDead):
		return

	var bullet = Bullet.instance()
	bullet.position = $"Mouth".global_position;
	bullet.z_index  = z_index-1;
	Global.GameScene.add_child(bullet);

func initialize():
	Global.setTimeout(self,randf()*5,self,"allowBullet")

var doAllowBullet = false

func allowBullet():
	doAllowBullet = true

func _process(_delta):

	if(!doAllowBullet):
		return

	if(!Global.doThrottle("DragonHead_"+self.name, 3000+randi()%3000)):
		for i in range(1,4):
			Global.setTimeout(self,i*0.3,self,"fireBullet")

