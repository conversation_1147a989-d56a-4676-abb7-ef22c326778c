[gd_scene load_steps=16 format=2]

[ext_resource path="res://scenes/Bosses/Boss2/DragonSegmentLight.tscn" type="PackedScene" id=1]
[ext_resource path="res://assets/Boss - Dragon - Head.png" type="Texture" id=2]
[ext_resource path="res://scenes/Bosses/Boss2/DragonHead.gd" type="Script" id=3]
[ext_resource path="res://shaders/hit.tres" type="Shader" id=4]
[ext_resource path="res://addons/kenney_particle_pack/spark_02.png" type="Texture" id=5]
[ext_resource path="res://addons/kenney_particle_pack/spark_03.png" type="Texture" id=6]
[ext_resource path="res://addons/kenney_particle_pack/spark_04.png" type="Texture" id=7]

[sub_resource type="ShaderMaterial" id=2]
shader = ExtResource( 4 )
shader_param/active = false
shader_param/gray = false
shader_param/flash_color = Color( 1, 1, 1, 1 )

[sub_resource type="RectangleShape2D" id=1]
extents = Vector2( 32, 13 )

[sub_resource type="CircleShape2D" id=3]

[sub_resource type="CanvasItemMaterial" id=4]
blend_mode = 2

[sub_resource type="Gradient" id=54]
offsets = PoolRealArray( 0.002849, 0.381766, 0.666667 )
colors = PoolColorArray( 0.440329, 0.827778, 0.997559, 1, 0, 0.976562, 1, 1, 0, 1, 0.227451, 0 )

[sub_resource type="GradientTexture" id=57]
gradient = SubResource( 54 )

[sub_resource type="ParticlesMaterial" id=55]
emission_shape = 1
emission_sphere_radius = 10.65
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 0.1
scale_random = 0.1
color_ramp = SubResource( 57 )

[sub_resource type="ParticlesMaterial" id=56]
emission_shape = 1
emission_sphere_radius = 10.65
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 0.01
scale_random = 0.02
color_ramp = SubResource( 57 )

[node name="DragonHead" type="Area2D"]
script = ExtResource( 3 )

[node name="Sprite" type="Sprite" parent="."]
material = SubResource( 2 )
texture = ExtResource( 2 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2( -6, 7 )
shape = SubResource( 1 )

[node name="ClusterLight" parent="." instance=ExtResource( 1 )]
position = Vector2( 23, 0 )

[node name="Mouth" type="CollisionShape2D" parent="."]
position = Vector2( -44, 24 )
shape = SubResource( 3 )
disabled = true

[node name="Scratch" type="Sprite" parent="."]
material = SubResource( 4 )
position = Vector2( 3, 10 )
rotation = -0.231226
scale = Vector2( 0.177852, 0.0477639 )
texture = ExtResource( 5 )

[node name="Scratch2" type="Sprite" parent="Scratch"]
material = SubResource( 4 )
position = Vector2( 13.0669, -210.082 )
scale = Vector2( -0.449223, -1.02796 )
texture = ExtResource( 5 )

[node name="Particles2D" type="Particles2D" parent="Scratch"]
modulate = Color( 1, 1, 1, 0.313726 )
position = Vector2( -63.099, -98.3342 )
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 55 )
texture = ExtResource( 7 )

[node name="Particles2D2" type="Particles2D" parent="Scratch/Particles2D"]
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 56 )
texture = ExtResource( 6 )
