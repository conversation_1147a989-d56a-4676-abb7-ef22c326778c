extends "res://scenes/Bosses/Boss2/DragonPartBase.gd"

func initialize():
	var _c = $"TailEndArea".connect("area_entered",self,"_tail_hit")
	$"Tail/TailSkeleton/Bone2D/Scratch".modulate.a  = 0
	$"Tail/TailSkeleton/Bone2D/Bone2D2/Scratch".modulate.a  = 0
	$"Tail/TailSkeleton/Bone2D/Bone2D2/Bone2D3/Scratch".modulate.a  = 0

func _process(_delta):
	pass

func getSprite():
	return $"Tail"

var _maxCrystals = 10

func _tail_hit(target):
	if(isDead):
		return

	if(target.has_method("getDamagePoint")):

		if(_maxCrystals > 0):
			_maxCrystals -= 1
			if not Global.doThrottle("DragonTailTickle",100):
				Global.GameScene.spawnBonusLabel(target.position,["XD",":)",";)",":o"][randi()%4],randf()+0.5);
				Global.GameScene.spawnCrystal(target.position,Global.CrystalType.c50);
		elif (_maxCrystals==0):
			_maxCrystals -= 1
			Achievments.acquire("dragon_tickler")
			Global.GameScene.spawnBonusLabel(target.position,"Tickle!",0.5,true,true,2.0);
			Global.playTts(SoundManager.tts_heeeeeeeee_yaaaaaaaaa)
			$"TailEndArea".disconnect("area_entered",self,"_tail_hit")

		# destroy bullet
		Global.callIfExists(target,"destroy")

var Bullet = preload("res://scenes/EnemyBullet_2.tscn")

func setScratch(_percentage):
	var _np = (100-_percentage)/100.0
	$"Tail/TailSkeleton/Bone2D/Scratch".modulate.a  = _np
	$"Tail/TailSkeleton/Bone2D/Bone2D2/Scratch".modulate.a  = _np
	$"Tail/TailSkeleton/Bone2D/Bone2D2/Bone2D3/Scratch".modulate.a  = _np

func fireBullet(doForce = false):

	if(!canFireBullet($"TailEndArea/TailEndCollision".global_position)):
		return

	if(isDead):
		return

	var bullet = Bullet.instance()
	bullet.position = $"TailEndArea/TailEndCollision".global_position;
	bullet.z_index  = z_index-1;
	Global.GameScene.add_child(bullet);

	pass

