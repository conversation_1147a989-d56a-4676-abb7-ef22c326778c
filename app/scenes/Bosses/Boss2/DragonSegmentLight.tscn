[gd_scene load_steps=4 format=2]

[ext_resource path="res://assets/Boss - Dragon - Magnetic Light.png" type="Texture" id=1]

[sub_resource type="Animation" id=3]
resource_name = "ClusterLightAnimation"
length = 2.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("Boss-Dragon-MagneticLight:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.564706 ), Color( 1, 1, 1, 0.223529 ) ]
}

[sub_resource type="Animation" id=4]
resource_name = "ClusterLightAnimation"
length = 2.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("Boss-Dragon-MagneticLight:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 0.5, 2 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.564706 ), Color( 1, 1, 1, 0.223529 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Boss-Dragon-MagneticLight:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1 ),
"transitions": PoolRealArray( 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.1, 0.9 ) ]
}

[node name="ClusterLight" type="Node2D"]

[node name="Boss-Dragon-MagneticLight" type="Sprite" parent="."]
modulate = Color( 1, 1, 1, 0.564706 )
position = Vector2( 8.87472, 9 )
texture = ExtResource( 1 )

[node name="LightFadeAnimation" type="AnimationPlayer" parent="."]
autoplay = "ClusterLightAnimation"
anims/ClusterLightAnimation = SubResource( 3 )

[node name="LightFadeAnimation2" type="AnimationPlayer" parent="."]
autoplay = "ClusterLightAnimation"
anims/ClusterLightAnimation = SubResource( 4 )
