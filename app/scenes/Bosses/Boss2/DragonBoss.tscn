[gd_scene load_steps=7 format=2]

[ext_resource path="res://scenes/Bosses/Boss2/DragonCluster.tscn" type="PackedScene" id=1]
[ext_resource path="res://scenes/Bosses/Boss2/DragonHead.tscn" type="PackedScene" id=2]
[ext_resource path="res://scenes/Bosses/Boss2/DragonTail.tscn" type="PackedScene" id=3]
[ext_resource path="res://scripts/DragonBoss.gd" type="Script" id=4]

[sub_resource type="Animation" id=5]
resource_name = "DragonBodyAnimation"
length = 7.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("DragonCluster3:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 1.5, 3, 6 ),
"transitions": PoolRealArray( 0.5, 1, 2, 1 ),
"update": 0,
"values": [ Vector2( 172, 33 ), Vector2( 171, 27 ), Vector2( 171, 38 ), Vector2( 171, 28 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("DragonCluster3:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 1.5, 3, 6 ),
"transitions": PoolRealArray( 0.5, 1, 2, 1 ),
"update": 0,
"values": [ 0.0, 10.7418, -9.68399, 10.4704 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("DragonCluster2:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 1.5, 3, 6 ),
"transitions": PoolRealArray( 0.5, 1, 2, 1 ),
"update": 0,
"values": [ Vector2( 118, 33 ), Vector2( 116, 23 ), Vector2( 118, 39 ), Vector2( 116, 24 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("DragonCluster2:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0, 3, 6 ),
"transitions": PoolRealArray( 0.5, 2, 1 ),
"update": 0,
"values": [ 0.0, 5.36038, 1.73045 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("DragonCluster1:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0, 1.5, 3, 6 ),
"transitions": PoolRealArray( 0.5, 1, 2, 1 ),
"update": 0,
"values": [ Vector2( 64, 33 ), Vector2( 62, 27 ), Vector2( 62, 36 ), Vector2( 59, 31 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("DragonCluster1:rotation_degrees")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0, 1.5, 3, 6 ),
"transitions": PoolRealArray( 0.5, 1, 2, 1 ),
"update": 0,
"values": [ 0.0, -8.83716, 1.88628, -9.83897 ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("DragonHead:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0, 1.5, 3, 6 ),
"transitions": PoolRealArray( 0.5, 1, 2, 1 ),
"update": 0,
"values": [ Vector2( 6, 27 ), Vector2( 4, 28 ), Vector2( 1, 43 ), Vector2( 0.999999, 26 ) ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("DragonHead:rotation_degrees")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0, 1.5, 3, 6 ),
"transitions": PoolRealArray( 0.5, 1, 2, 1 ),
"update": 0,
"values": [ 0.0, -5.41243, -18.8792, 4.62241 ]
}

[sub_resource type="Animation" id=6]
length = 0.001
tracks/0/type = "value"
tracks/0/path = NodePath("DragonCluster3:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 172, 33 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("DragonCluster3:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("DragonCluster2:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 118, 33 ) ]
}
tracks/3/type = "value"
tracks/3/path = NodePath("DragonCluster2:rotation_degrees")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/4/type = "value"
tracks/4/path = NodePath("DragonCluster1:position")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 64, 33 ) ]
}
tracks/5/type = "value"
tracks/5/path = NodePath("DragonCluster1:rotation_degrees")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/imported = false
tracks/5/enabled = true
tracks/5/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}
tracks/6/type = "value"
tracks/6/path = NodePath("DragonHead:position")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/imported = false
tracks/6/enabled = true
tracks/6/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ Vector2( 6, 27 ) ]
}
tracks/7/type = "value"
tracks/7/path = NodePath("DragonHead:rotation_degrees")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/imported = false
tracks/7/enabled = true
tracks/7/keys = {
"times": PoolRealArray( 0 ),
"transitions": PoolRealArray( 1 ),
"update": 0,
"values": [ 0.0 ]
}

[node name="Dragon" type="Node2D"]
position = Vector2( -136, -50 )
script = ExtResource( 4 )

[node name="Body" type="Node2D" parent="."]
position = Vector2( 73, 16 )

[node name="BodyAnimation" type="AnimationPlayer" parent="Body"]
autoplay = "DragonBodyAnimation"
anims/DragonBodyAnimation = SubResource( 5 )
anims/RESET = SubResource( 6 )

[node name="DragonHead" parent="Body" instance=ExtResource( 2 )]
position = Vector2( 6, 27 )

[node name="DragonCluster1" parent="Body" instance=ExtResource( 1 )]
position = Vector2( 64, 33 )

[node name="DragonCluster2" parent="Body" instance=ExtResource( 1 )]
position = Vector2( 118, 33 )

[node name="DragonCluster3" parent="Body" instance=ExtResource( 1 )]
position = Vector2( 172, 33 )

[node name="DragonTail" parent="Body" instance=ExtResource( 3 )]
position = Vector2( 197, 18 )
