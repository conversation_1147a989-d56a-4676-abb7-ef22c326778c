 extends Area2D

var health = 300
var maxHealth = 300
var legCount = 8
var isDead = false
var Explosion = preload("res://scenes/Explosion_1.tscn")

signal spiderBossDead(bossInstance)

func incHealthDiff():
	var dict = {
		Global.GameDifficulty.EASY: 0,
		Global.GameDifficulty.NORMAL: 50,
		Global.GameDifficulty.HARD: 200,
		Global.GameDifficulty.EXTREME: 300
	}

	return self.health+dict[Global.GameScene.difficulty]

func getSprite():
	return $Body

func _fon():
	getSprite().material.set_shader_param("active", 1)

func _foff():
	getSprite().material.set_shader_param("active", 0)

func flash():
	Global.playSound(SoundManager.HitSound, global_position, -10)
	_fon()
	Global.setTimeout(self,0.1,self,"_foff")

func bodyExplosion(extreme = true, cnt = 0):
	var explosion = Explosion.instance()
	explosion.position = global_position-Vector2(80,150)+Vector2(randi()%160,randi()%300)
	explosion.z_index  = z_index+1;
	explosion.scale = Vector2(1+randf(),1+randf())

	if(extreme):
		explosion.volume = 5
		explosion.cameraShake = 0.2
		Global.GameScene.FlashWorldEnv(0.3, 10)
	else:
		Global.GameScene.FlashWorldEnv(0.2, max(1, 20/(cnt+2)))
		explosion.volume = 5-cnt

	Global.GameScene.add_child(explosion);

func spawnCrystal():
	get_parent().spawnCrystal()

func deadAnimOver():
	call_deferred("spawnCrystal")
	call_deferred("queue_free")

func startExplosions(isFinal = false):
	var timeSum = 0
	var timebase = Global.ifelse(isFinal,0.2,0.1)

	for _i in range(20):
		var _t = timebase+(randf()/10)
		Global.setTimeout(self, timeSum+_t, self,"bodyExplosion", [!isFinal, _i])
		timeSum+=_t

	if(isFinal):
		var tween = Global.createTween(self)
		tween.interpolate_property(self,"position:y",position.y,-200.0,timeSum, Tween.TRANS_QUAD, Tween.EASE_OUT)
		tween.connect("tween_all_completed",self,"deadAnimOver")
		tween.start()
	else:
		Global.setTimeout(self, timeSum, self,"startExplosions",[true])
	
	if(isFinal):
		var pwuRadius = 50
		Global.GameScene.spawnPowerup( global_position-Vector2(pwuRadius,300)+Vector2(randi()%(pwuRadius*2),randi()%100), Global.PowerupType.CRYSTAL_MAGNET)
		Global.GameScene.spawnPowerup( global_position-Vector2(pwuRadius,300)+Vector2(randi()%(pwuRadius*2),randi()%100), Global.PowerupType.TIMED_SUPER_RAPIDFIRE)
		Global.GameScene.spawnPowerup( global_position-Vector2(pwuRadius,300)+Vector2(randi()%(pwuRadius*2),randi()%100), Global.PowerupType.TIMED_SUPER_RAPIDFIRE)
		emit_signal("spiderBossDead", self)


var spiders = []

func hideSpiders():
	for spider in spiders:
		if(is_instance_valid(spider)):
			spider.call_deferred("queue_free");


func die():

	if(isDead):
		return false
	
	if(is_instance_valid(lastLaserAudio)):
		lastLaserAudio.stop();
		lastLaserAudio.call_deferred("queue_free");

	hideLaser()
	hideSpiders()

	Global.stopMusicInstantly(Global.GameScene.get_node("BackgroundMusicPlayer"))

	get_node("../AnimationPlayerWiggleX").stop()
	get_node("../AnimationPlayerWiggleY").stop()
	get_node("../AnimationPlayer").stop()

	isDead = true
	startExplosions()

func setScratch(_percentage):
	get_node("Body/Cracks").modulate.a = (100-_percentage)/100.0

func takeDamage(damage):

	if(Global.doThrottle("SpiderBossDamage"+str(get_instance_id()),Config.BossDamageThrottle)):
		return false

	health=health-damage
	health = max(0, health)

	setScratch(Global.getPercentage(health,maxHealth))

	if(health>0):
		flash()
		return true
	
	die()

func shieldFlashOn():
	$FrontShield.modulate = Color(1.2,1.2,1.2,1.2)
	$ShieldBalls.modulate = Color(1.2,1.2,1.2,1.2)
	Global.playSound(SoundManager.Shield,global_position, -10);
	Global.setTimeout(self,0.1,self,"shieldFlashOff")
	pass

func shieldFlashOff():
	$FrontShield.modulate = Color(1,1,1,1)
	$ShieldBalls.modulate = Color(1,1,1,0.45)
	pass

func _on_hit(area):
	if(area.has_method("getDamagePoint") && !isDead):
		Global.callIfExists(area,"destroy")
		if(isShieldDown):
			takeDamage(area.getDamagePoint())
		else:
			shieldFlashOn()

var isShieldDown = false

var legList = [] 

func decLegCnt(_legNode):
	legCount-=1
	State.setValue(State.SPIDER_LEG_COUNT, legCount)

func checkHideShield():
	if(legCount<=0 && !isShieldDown):
		isShieldDown = true
		$FrontShield.visible = false
		$ShieldBalls.visible = false
		releaseSpiders(6)
	
var SpiderBullet = preload("res://scenes/KamikazeSpider.tscn")

func releaseSpiders(numberOfSpiders=1, repeat = true):
	if(isDead):
		return false

	for _i in range(numberOfSpiders):
		var bullet = SpiderBullet.instance()
		spiders.push_back(bullet)
		bullet.position = self.global_position;
		bullet.z_index  = z_index-1;
		Global.GameScene.add_child(bullet);
		bullet.initStart(global_position, Vector2(100+randi()%int(Global.getWindowSize().x-100), randi()%200) , 0)
	
	if(repeat):
		Global.setTimeout(self,1+(randf()*2),self,"releaseSpiders",[1,true])

func _ready():

	maxHealth = incHealthDiff()
	health = maxHealth

	State.setValue(State.SPIDER_BOSS_INSTANCE, self)
	var _c1e = connect("area_entered",self,"_on_hit")

var laserInProgress = false

func showLaser():
	if(laserInProgress):
		$Beam.isActive(true)

func hideLaser():
	$Beam.isActive(false)
	$BeamParticles.visible = false
	laserInProgress = false

var lastLaserAudio = null

func fireLaser():

	if(laserInProgress || isDead):
		return false

	laserInProgress = true

	lastLaserAudio = Global.playSound(SoundManager.BigLaser, global_position,0)
	$BeamParticles.visible = true
	Global.setTimeout(self,2.7,self,"showLaser")
	Global.setTimeout(self,6.1,self,"hideLaser")



func init():

	State.setValue(State.SPIDER_LEG_COUNT, legCount)

	legList = [
		$LeftLegs/SpiderLeg1,
		$LeftLegs/SpiderLeg2,
		$LeftLegs/SpiderLeg3,
		$LeftLegs/SpiderLeg4,
		$RightLegs/SpiderLeg1,
		$RightLegs/SpiderLeg2,
		$RightLegs/SpiderLeg3,
		$RightLegs/SpiderLeg4
	]

	for _leg in legList:
		_leg.connect("legDestroyed",self,"decLegCnt")

var _resCache = []
var _wasCache = false

func getTargets():

	if(_wasCache):
		return _resCache
	_wasCache = true

	var _res = []
	var _cld = Global.get_all_children(self)
	for _node in _cld:
		if _node is Sprite or _node is Node2D or _node is Area2D:
			_res.push_back(_node)

	_resCache = _res
	return _res

func doFireBeam():
	if(laserInProgress):
		return false

	if(!Global.GameScene.isPlayerReady()):
		return false

	if(!Global.doThrottle("spiderFireBeam",Global.ifelse(isShieldDown,500,1000))):
		if(Global.isChance(randi(),10)):
			fireLaser()

func _process(_delta):
	if(!Global.doThrottle("spiderBossShield",500) && !isShieldDown):
		checkHideShield()

	doFireBeam()
