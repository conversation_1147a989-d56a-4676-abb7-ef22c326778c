[gd_scene load_steps=11 format=2]

[ext_resource path="res://addons/kenney_particle_pack/16/twirl_01_16.png" type="Texture" id=1]
[ext_resource path="res://addons/kenney_particle_pack/16/spark_05_16.png" type="Texture" id=2]

[sub_resource type="Gradient" id=4]
offsets = PoolRealArray( 0.00215983, 1 )
colors = PoolColorArray( 0, 0, 0, 0, 1, 1, 1, 1 )

[sub_resource type="GradientTexture" id=5]
gradient = SubResource( 4 )

[sub_resource type="Curve" id=2]
_data = [ Vector2( 0, 0 ), 0.0, 0.0, 0, 0, Vector2( 1, 1 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=3]
curve = SubResource( 2 )

[sub_resource type="ParticlesMaterial" id=1]
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
gravity = Vector3( 0, 0, 0 )
angular_velocity = 720.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
angle = 720.0
angle_random = 0.99
scale = 0.5
scale_random = 1.0
scale_curve = SubResource( 3 )
color = Color( 0, 0.356863, 1, 1 )
color_ramp = SubResource( 5 )

[sub_resource type="Gradient" id=6]
offsets = PoolRealArray( 0, 0.49676, 0.663067, 1 )
colors = PoolColorArray( 1, 1, 1, 0, 0, 0.992157, 0.85098, 1, 0, 1, 0.827148, 1, 0, 0.273438, 1, 0 )

[sub_resource type="GradientTexture" id=7]
gradient = SubResource( 6 )

[sub_resource type="ParticlesMaterial" id=8]
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
gravity = Vector3( 0, 0, 0 )
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
angle = 333.1
angle_random = 1.0
scale = 0.8
scale_random = 1.0
color = Color( 0, 1, 0.976471, 1 )
color_ramp = SubResource( 7 )

[node name="ShieldEnergyBall" type="Node2D"]

[node name="Particles2D" type="Particles2D" parent="."]
amount = 15
process_material = SubResource( 1 )
texture = ExtResource( 1 )

[node name="Particles2D2" type="Particles2D" parent="."]
process_material = SubResource( 8 )
texture = ExtResource( 2 )
