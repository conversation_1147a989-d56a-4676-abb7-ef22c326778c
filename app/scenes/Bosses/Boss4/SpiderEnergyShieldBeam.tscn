[gd_scene load_steps=7 format=2]

[ext_resource path="res://addons/kenney_particle_pack/16/spark_02_16.png" type="Texture" id=1]

[sub_resource type="Gradient" id=2]
colors = PoolColorArray( 0, 0.929688, 1, 1, 1, 0.984314, 0, 0.415686 )

[sub_resource type="GradientTexture" id=3]
gradient = SubResource( 2 )

[sub_resource type="Curve" id=4]
_data = [ Vector2( 0, 1 ), 0.0, 0.0, 0, 0, Vector2( 1, 0.367045 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=5]
curve = SubResource( 4 )

[sub_resource type="ParticlesMaterial" id=1]
flag_disable_z = true
direction = Vector3( 0, 1, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 30.0
initial_velocity_random = 0.55
orbit_velocity = 0.0
orbit_velocity_random = 0.0
angle = -271.1
angle_random = 1.0
scale_curve = SubResource( 5 )
color_ramp = SubResource( 3 )
hue_variation = 0.04
hue_variation_random = 0.42

[node name="SpiderEnergyShieldBeam" type="Node2D"]

[node name="Particles2D" type="Particles2D" parent="."]
amount = 15
process_material = SubResource( 1 )
texture = ExtResource( 1 )
