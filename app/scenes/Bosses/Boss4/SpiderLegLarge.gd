extends Node2D

export(NodePath) var EnergySource 
export(NodePath) var EnergySourceBeam

var Bullet = preload("res://scenes/EnemyBullet_6.tscn")

signal legDestroyed(legObject)

var health = 200
var maxHealth = 200

func incHealthDiff():
	var dict = {
		Global.GameDifficulty.EASY: 0,
		Global.GameDifficulty.NORMAL: 100,
		Global.GameDifficulty.HARD: 200,
		Global.GameDifficulty.EXTREME: 300
	}

	return self.health+dict[Global.GameScene.difficulty]

func bulletSpeedDiff():
	var dict = {
		Global.GameDifficulty.EASY: 0,
		Global.GameDifficulty.NORMAL: 00,
		Global.GameDifficulty.HARD: 50,
		Global.GameDifficulty.EXTREME: 100
	}

	return dict[Global.GameScene.difficulty]


func fireHomingBullet():
	if(isDead):
		return false

	if(Global.GameScene.isPlayerReady() == false):
		return false
	
	calcHomingDelay()

	var legEnd = get_node("Base/Big-leg/Big-connector/Big-leg-armored/Small-connector/Rod/Claw")

	var bullet = Bullet.instance()
	bullet.showTail()

	# bullet.modulate.a = 10.0
	# bullet.scale = Vector2(4.0,4.0)

	bullet.start(0)
	bullet.position = legEnd.global_position;
	bullet.z_index  = z_index-1;
	Global.GameScene.add_child(bullet);

var homingDelay = 10000
var isDead = false
var Explosion = preload("res://scenes/Explosion_1.tscn")

func explode(element):
	var explosion = Explosion.instance()
	explosion.position = element.global_position
	explosion.z_index  = element.z_index+1;
	explosion.scale = Vector2(1,1)
	Global.GameScene.add_child(explosion);
	element.visible = false



func hideBeam():
	get_node(EnergySource).visible = false
	get_node(EnergySourceBeam).visible = false
	self.visible = false


func die():
	if(isDead):
		return false

	emit_signal("legDestroyed", self)

	isDead = true

	Global.GameScene.call_deferred("handleLoot",get_node("Base/Big-leg/Big-connector/Big-leg-armored").global_position)

	var _ch = Global.get_all_children(self)
	_ch.invert()
	var cnt = 0
	for e in _ch:
		if e is Sprite:
			cnt=cnt+1
			Global.setTimeout(self, 0.1*cnt,self,"explode",[e])

	Global.setTimeout(self, 0.1*(cnt+1),self,"hideBeam")
	
	pass

func getSprite():
	return get_node("Base")

func _fon(_sprite):
	_c1.get_parent().material.set_shader_param("active", 1)
	_c2.get_parent().material.set_shader_param("active", 1)
	_c3.get_parent().material.set_shader_param("active", 1)
	_c4.get_parent().material.set_shader_param("active", 1)

func _foff(_sprite):
	_c1.get_parent().material.set_shader_param("active", 0)
	_c2.get_parent().material.set_shader_param("active", 0)
	_c3.get_parent().material.set_shader_param("active", 0)
	_c4.get_parent().material.set_shader_param("active", 0)

func flash(sprite):
	Global.playSound(SoundManager.HitSound, global_position, -10)
	_fon(sprite)
	Global.setTimeout(self,0.1,self,"_foff",[sprite])

func calcHomingDelay():
	# todo: add difficulty parameter
	var _hdBase = ((State.getValue(State.SPIDER_LEG_COUNT,8)+1)*(500-bulletSpeedDiff()))
	homingDelay = _hdBase + (randi()%_hdBase)

func _process(_delta):

	if(!Global.doThrottle("spider_homing_bullet_"+str(self.get_instance_id()), homingDelay)):
		fireHomingBullet()

func setScratch(_percentage):
	get_node("Base/Big-leg/Big-connector/Big-leg-armored/Crack").modulate.a = (100-_percentage)/100.0
	get_node("Base/Big-leg/Crack").modulate.a = (100-_percentage)/100.0
	
func takeDamage(damage, sprite):

	if(Global.doThrottle("SpiderBossDamage"+str(get_instance_id()),Config.BossDamageThrottle)):
		return false

	health=health-damage
	health = max(0, health)

	setScratch(Global.getPercentage(health,maxHealth))

	if(health>0):
		flash(sprite)
		return true
	
	die()

func _on_hit(area, sprite):
	if(area.has_method("getDamagePoint") && !isDead):
		Global.callIfExists(area,"destroy")
		takeDamage(area.getDamagePoint(), sprite)

var _c1 = null
var _c2 = null
var _c3 = null
var _c4 = null

func _ready():

	maxHealth = incHealthDiff()
	health = maxHealth

	_c1 = get_node("Base/Big-leg/Big-connector/Big-leg-armored/Small-connector/Rod/Claw/Area2D")
	_c2 = get_node("Base/Big-leg/Big-connector/Big-leg-armored/Small-connector/Rod/Area2D")
	_c3 = get_node("Base/Big-leg/Big-connector/Big-leg-armored/Area2D")
	_c4 = get_node("Base/Big-leg/Area2D")

	var _c1e = _c1.connect("area_entered",self,"_on_hit",[_c1.get_parent()])
	var _c2e = _c2.connect("area_entered",self,"_on_hit",[_c2.get_parent()])
	var _c3e = _c3.connect("area_entered",self,"_on_hit",[_c3.get_parent()])
	var _c4e = _c4.connect("area_entered",self,"_on_hit",[_c4.get_parent()])

	randomize()
	calcHomingDelay()

	Global.doThrottle("spider_homing_bullet_"+str(self.get_instance_id()), homingDelay)

	var _r = randi()%2
	if(_r==2):
		_r=-1
	$AnimationPlayer.playback_speed = 1+randf()*_r
