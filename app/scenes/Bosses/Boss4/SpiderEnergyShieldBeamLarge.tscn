[gd_scene load_steps=7 format=2]

[ext_resource path="res://addons/kenney_particle_pack/16/slash_01_16.png" type="Texture" id=1]

[sub_resource type="Gradient" id=2]
offsets = PoolRealArray( 0, 0.479482, 0.794816, 1 )
colors = PoolColorArray( 1, 0.984314, 0, 1, 0.0352941, 0.933333, 0.964706, 1, 0.00424701, 0.584788, 0.995753, 1, 0, 0.537255, 1, 0 )

[sub_resource type="GradientTexture" id=3]
gradient = SubResource( 2 )

[sub_resource type="Curve" id=4]
_data = [ Vector2( 0, 0 ), 0.0, 0.0, 0, 0, Vector2( 1, 1 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=5]
curve = SubResource( 4 )

[sub_resource type="ParticlesMaterial" id=6]
flag_disable_z = true
direction = Vector3( 0, 1, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 25.0
initial_velocity_random = 0.55
orbit_velocity = 0.0
orbit_velocity_random = 0.0
angle = 0.7
scale = 5.0
scale_curve = SubResource( 5 )
color_ramp = SubResource( 3 )
hue_variation = 0.04
hue_variation_random = 0.42

[node name="SpiderEnergyShieldBeam" type="Node2D"]

[node name="Particles2D" type="Particles2D" parent="."]
amount = 5
process_material = SubResource( 6 )
texture = ExtResource( 1 )
