[gd_scene load_steps=42 format=2]

[ext_resource path="res://assets/powerup/powerup_negative_red.png" type="Texture" id=1]
[ext_resource path="res://scripts/Powerup.gd" type="Script" id=2]
[ext_resource path="res://scenes/Themes/powerup_label_font.tres" type="DynamicFont" id=3]
[ext_resource path="res://addons/kenney_particle_pack/16/magic_05_16.png" type="Texture" id=4]
[ext_resource path="res://assets/powerup/powerup_weapon_gray.png" type="Texture" id=5]
[ext_resource path="res://assets/powerup/powerup_weapon_blue.png" type="Texture" id=6]
[ext_resource path="res://assets/powerup/powerup_instant_blue.png" type="Texture" id=7]
[ext_resource path="res://assets/powerup/powerup_stats_blue.png" type="Texture" id=8]
[ext_resource path="res://assets/powerup/powerup_weapon_green.png" type="Texture" id=9]
[ext_resource path="res://assets/powerup/powerup_time_red.png" type="Texture" id=10]
[ext_resource path="res://assets/powerup/powerup_time_blue.png" type="Texture" id=11]
[ext_resource path="res://assets/powerup/powerup_stats_yellow.png" type="Texture" id=12]
[ext_resource path="res://assets/powerup/powerup_instant_red.png" type="Texture" id=13]
[ext_resource path="res://assets/powerup/powerup_time_green.png" type="Texture" id=14]
[ext_resource path="res://assets/powerup/powerup_stats_purple.png" type="Texture" id=15]
[ext_resource path="res://assets/powerup/powerup_weapon_yellow.png" type="Texture" id=16]
[ext_resource path="res://assets/powerup/powerup_stats_green.png" type="Texture" id=17]
[ext_resource path="res://assets/powerup/powerup_instant_yellow.png" type="Texture" id=18]
[ext_resource path="res://assets/powerup/powerup_instant_green.png" type="Texture" id=19]
[ext_resource path="res://assets/powerup/powerup_instant_gray.png" type="Texture" id=20]
[ext_resource path="res://assets/powerup/powerup_base_purple.png" type="Texture" id=21]
[ext_resource path="res://assets/powerup/powerup_base_gray.png" type="Texture" id=22]
[ext_resource path="res://assets/powerup/powerup_base_red.png" type="Texture" id=23]
[ext_resource path="res://assets/powerup/powerup_base_blue.png" type="Texture" id=24]
[ext_resource path="res://assets/powerup/powerup_weapon_red.png" type="Texture" id=25]
[ext_resource path="res://assets/powerup/powerup_time_gray.png" type="Texture" id=26]
[ext_resource path="res://assets/powerup/powerup_stats_red.png" type="Texture" id=27]
[ext_resource path="res://assets/powerup/powerup_instant_purple.png" type="Texture" id=28]
[ext_resource path="res://assets/powerup/powerup_stats_gray.png" type="Texture" id=29]
[ext_resource path="res://assets/powerup/powerup_weapon_purple.png" type="Texture" id=30]
[ext_resource path="res://assets/powerup/powerup_base_yellow.png" type="Texture" id=31]
[ext_resource path="res://assets/powerup/powerup_time_yellow.png" type="Texture" id=32]
[ext_resource path="res://assets/powerup/powerup_base_green.png" type="Texture" id=33]
[ext_resource path="res://assets/powerup/powerup_time_purple.png" type="Texture" id=34]

[sub_resource type="Animation" id=6]
resource_name = "PowerUpAnimation"
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("Sprite:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5, 1 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Vector2( 1, 1 ), Vector2( 1.2, 1.2 ), Vector2( 1, 1 ) ]
}

[sub_resource type="Animation" id=11]
resource_name = "PowerUpAnimation"
length = 2.0
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("Sprite:rotation_degrees")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5, 1.5, 2 ),
"transitions": PoolRealArray( 1, 1, 1, 1 ),
"update": 0,
"values": [ 0.0, 10.0, -10.0, 0.0 ]
}

[sub_resource type="RectangleShape2D" id=4]
extents = Vector2( 11, 11 )

[sub_resource type="Gradient" id=8]
offsets = PoolRealArray( 0.785714, 1 )
colors = PoolColorArray( 1, 1, 1, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=9]
gradient = SubResource( 8 )

[sub_resource type="ParticlesMaterial" id=7]
emission_shape = 1
emission_sphere_radius = 10.79
flag_disable_z = true
direction = Vector3( 0, -1, 0 )
gravity = Vector3( 0, 10, 0 )
initial_velocity = 40.0
initial_velocity_random = 1.0
angular_velocity = 10.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 2.0
scale_random = 1.0
color_ramp = SubResource( 9 )
hue_variation = -1.0
hue_variation_random = 1.0

[sub_resource type="SpriteFrames" id=10]
animations = [ {
"frames": [ ExtResource( 22 ), ExtResource( 33 ), ExtResource( 24 ), ExtResource( 21 ), ExtResource( 23 ), ExtResource( 31 ) ],
"loop": true,
"name": "base",
"speed": 5.0
}, {
"frames": [ ExtResource( 20 ), ExtResource( 19 ), ExtResource( 7 ), ExtResource( 28 ), ExtResource( 13 ), ExtResource( 18 ) ],
"loop": true,
"name": "instant",
"speed": 5.0
}, {
"frames": [ ExtResource( 1 ) ],
"loop": true,
"name": "negative",
"speed": 5.0
}, {
"frames": [ ExtResource( 29 ), ExtResource( 17 ), ExtResource( 8 ), ExtResource( 15 ), ExtResource( 27 ), ExtResource( 12 ) ],
"loop": true,
"name": "stat",
"speed": 5.0
}, {
"frames": [ ExtResource( 26 ), ExtResource( 14 ), ExtResource( 11 ), ExtResource( 34 ), ExtResource( 10 ), ExtResource( 32 ) ],
"loop": true,
"name": "time",
"speed": 5.0
}, {
"frames": [ ExtResource( 5 ), ExtResource( 9 ), ExtResource( 6 ), ExtResource( 30 ), ExtResource( 25 ), ExtResource( 16 ) ],
"loop": true,
"name": "weapon",
"speed": 5.0
} ]

[node name="Powerup" type="Area2D"]
script = ExtResource( 2 )

[node name="AnimationPlayer_scale" type="AnimationPlayer" parent="."]
autoplay = "PowerUpAnimation"
anims/PowerUpAnimation = SubResource( 6 )

[node name="AnimationPlayer_rotate" type="AnimationPlayer" parent="."]
autoplay = "PowerUpAnimation"
anims/PowerUpAnimation = SubResource( 11 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
shape = SubResource( 4 )

[node name="Particles" type="Particles2D" parent="."]
modulate = Color( 1, 1, 1, 0.0392157 )
amount = 40
lifetime = 0.8
speed_scale = 1.5
local_coords = false
process_material = SubResource( 7 )
texture = ExtResource( 4 )

[node name="Sprite" type="Node2D" parent="."]

[node name="Block" type="AnimatedSprite" parent="Sprite"]
frames = SubResource( 10 )
animation = "negative"

[node name="letter1" type="Node2D" parent="Sprite"]
position = Vector2( 0.2, 1 )
scale = Vector2( 0.8, 0.8 )

[node name="Shadow" type="Label" parent="Sprite/letter1"]
modulate = Color( 0, 0, 0, 1 )
margin_left = -9.0
margin_top = -11.0
margin_right = 11.0
margin_bottom = 8.0
grow_horizontal = 2
grow_vertical = 2
custom_fonts/font = ExtResource( 3 )
text = "?"
align = 1
valign = 1

[node name="Letter" type="Label" parent="Sprite/letter1"]
margin_left = -11.0
margin_top = -11.0
margin_right = 11.0
margin_bottom = 7.0
grow_horizontal = 2
grow_vertical = 2
rect_clip_content = true
custom_fonts/font = ExtResource( 3 )
text = "?"
align = 1
valign = 1

[node name="letter2" type="Node2D" parent="Sprite"]
visible = false
position = Vector2( 0.5, 0 )
scale = Vector2( 0.8, 0.8 )

[node name="Shadow" type="Label" parent="Sprite/letter2"]
modulate = Color( 0, 0, 0, 1 )
margin_left = -11.0
margin_top = -11.0
margin_right = 26.0
margin_bottom = 10.0
grow_horizontal = 2
grow_vertical = 2
rect_scale = Vector2( 0.6, 1 )
custom_fonts/font = ExtResource( 3 )
text = "??"
align = 1
valign = 1

[node name="Letter" type="Label" parent="Sprite/letter2"]
margin_left = -11.0
margin_top = -11.0
margin_right = 24.0
margin_bottom = 9.0
grow_horizontal = 2
grow_vertical = 2
rect_scale = Vector2( 0.6, 1 )
rect_clip_content = true
custom_fonts/font = ExtResource( 3 )
text = "??"
align = 1
valign = 1
