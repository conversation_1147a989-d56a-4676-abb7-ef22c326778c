[gd_scene load_steps=10 format=2]

[ext_resource path="res://scripts/AchievmentScreen.gd" type="Script" id=1]
[ext_resource path="res://assets/fonts/c64.tres" type="DynamicFont" id=2]
[ext_resource path="res://assets/fonts/Arial_Narrow.tres" type="DynamicFont" id=3]
[ext_resource path="res://scenes/Themes/tryu_theme.tres" type="Theme" id=4]
[ext_resource path="res://scenes/Themes/item_list.tres" type="Theme" id=5]

[sub_resource type="DynamicFontData" id=6]
font_path = "res://assets/fonts/Commodore Pixelized v1.2.ttf"

[sub_resource type="DynamicFont" id=5]
size = 44
font_data = SubResource( 6 )

[sub_resource type="DynamicFontData" id=15]
font_path = "res://assets/fonts/Commodore Pixelized v1.2.ttf"

[sub_resource type="DynamicFont" id=16]
size = 44
font_data = SubResource( 15 )

[node name="AchievmentScreen" type="Control"]
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource( 1 )

[node name="ColorRect" type="ColorRect" parent="."]
margin_right = 1024.0
margin_bottom = 576.0
color = Color( 0, 0, 0, 1 )

[node name="ItemList" type="ItemList" parent="."]
anchor_top = 0.007
anchor_bottom = 0.007
margin_left = 20.0
margin_top = 73.968
margin_right = 1001.0
margin_bottom = 546.968
theme = ExtResource( 5 )
custom_fonts/font = ExtResource( 3 )
items = [ "Item 0 :", null, false, "Item 1", null, false, "Item 2", null, false, "Item 3", null, false, "Item 4", null, false, "Item 5", null, false, "Item 6", null, false, "Item 7 test  test  test  test  test  test  test  test  test  test  test  test  test  test  test ", null, false ]
max_text_lines = 2
max_columns = 2

[node name="Title" type="Label" parent="."]
margin_left = 20.0
margin_top = 14.0
margin_right = 156.0
margin_bottom = 65.0
custom_fonts/font = SubResource( 5 )
text = "Achievements"

[node name="CloseButton" type="Button" parent="."]
margin_left = 822.0
margin_top = 14.0
margin_right = 999.0
margin_bottom = 65.0
theme = ExtResource( 4 )
custom_fonts/font = ExtResource( 2 )
text = "Close"

[node name="Demo" type="Label" parent="."]
modulate = Color( 1, 0, 0, 0.772549 )
margin_left = 442.0
margin_top = 268.0
margin_right = 578.0
margin_bottom = 312.0
rect_rotation = 15.0
rect_scale = Vector2( 7, 7 )
rect_pivot_offset = Vector2( 67.0968, 22.5921 )
custom_fonts/font = SubResource( 16 )
text = "DEMO"
align = 1
valign = 1
