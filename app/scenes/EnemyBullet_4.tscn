[gd_scene load_steps=14 format=2]

[ext_resource path="res://assets/bullet_ball.png" type="Texture" id=1]
[ext_resource path="res://scripts/EnemyBullet_4.gd" type="Script" id=2]
[ext_resource path="res://assets/sounds/laser1.wav" type="AudioStream" id=3]
[ext_resource path="res://addons/kenney_particle_pack/16/twirl_01_16.png" type="Texture" id=4]

[sub_resource type="SpriteFrames" id=6]
animations = [ {
"frames": [ ExtResource( 1 ) ],
"loop": true,
"name": "default",
"speed": 10.0
} ]

[sub_resource type="RectangleShape2D" id=7]
extents = Vector2( 2, 2 )

[sub_resource type="Gradient" id=9]
offsets = PoolRealArray( 0, 0.4946, 1 )
colors = PoolColorArray( 1, 1, 1, 0, 0, 0.8125, 1, 0.419608, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=10]
gradient = SubResource( 9 )

[sub_resource type="Curve" id=11]
_data = [ Vector2( 0, 1 ), 0.0, 0.0, 0, 0, Vector2( 1, 0.490909 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=12]
curve = SubResource( 11 )

[sub_resource type="ParticlesMaterial" id=8]
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
gravity = Vector3( 0, -30, 0 )
angular_velocity = 331.53
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
angle = 100.0
angle_random = 1.0
scale_random = 1.0
scale_curve = SubResource( 12 )
color_ramp = SubResource( 10 )

[sub_resource type="Gradient" id=13]
colors = PoolColorArray( 0, 0.835294, 1, 0.427451, 1, 1, 1, 0 )

[sub_resource type="GradientTexture2D" id=14]
gradient = SubResource( 13 )
fill = 1
fill_from = Vector2( 0.5, 0.5 )
fill_to = Vector2( 0.5, 0 )

[node name="EnemyBullet_4" type="Area2D"]
script = ExtResource( 2 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
position = Vector2( 1.19209e-07, 0 )
rotation = 3.14159
scale = Vector2( 0.615551, 0.588588 )
frames = SubResource( 6 )
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource( 7 )

[node name="AudioStreamPlayer2D" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 3 )
volume_db = -10.0

[node name="Particles2D" type="Particles2D" parent="."]
amount = 10
lifetime = 0.1
local_coords = false
process_material = SubResource( 8 )
texture = ExtResource( 4 )

[node name="Sprite" type="Sprite" parent="."]
scale = Vector2( 0.238281, 0.25978 )
texture = SubResource( 14 )
