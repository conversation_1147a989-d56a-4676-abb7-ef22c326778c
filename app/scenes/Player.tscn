[gd_scene load_steps=29 format=2]

[ext_resource path="res://assets/spaceship_3.png" type="Texture" id=1]
[ext_resource path="res://assets/spaceship_5.png" type="Texture" id=2]
[ext_resource path="res://assets/spaceship_4.png" type="Texture" id=3]
[ext_resource path="res://assets/spaceship_6.png" type="Texture" id=4]
[ext_resource path="res://assets/spaceship_2.png" type="Texture" id=5]
[ext_resource path="res://assets/spaceship_1.png" type="Texture" id=6]
[ext_resource path="res://scripts/Player.gd" type="Script" id=7]
[ext_resource path="res://assets/Hero - Spaceship - Frame 03.png" type="Texture" id=8]
[ext_resource path="res://assets/Hero - Spaceship - Frame 02.png" type="Texture" id=9]
[ext_resource path="res://assets/Hero - Spaceship - Frame 01.png" type="Texture" id=10]
[ext_resource path="res://scenes/PlayerAvoidBonus.tscn" type="PackedScene" id=11]
[ext_resource path="res://addons/kenney_particle_pack/circle_04.png" type="Texture" id=12]
[ext_resource path="res://scenes/Indicator.tscn" type="PackedScene" id=13]
[ext_resource path="res://addons/kenney_particle_pack/16/magic_05_16.png" type="Texture" id=14]
[ext_resource path="res://scenes/PlayerDisplay.tscn" type="PackedScene" id=15]
[ext_resource path="res://scenes/direction_shader.tres" type="Material" id=16]

[sub_resource type="SpriteFrames" id=1]
animations = [ {
"frames": [ ExtResource( 6 ), ExtResource( 5 ), ExtResource( 1 ), ExtResource( 3 ), ExtResource( 2 ), ExtResource( 4 ) ],
"loop": true,
"name": "default",
"speed": 5.0
}, {
"frames": [ ExtResource( 10 ), ExtResource( 9 ), ExtResource( 8 ) ],
"loop": true,
"name": "hero",
"speed": 5.0
} ]

[sub_resource type="CapsuleShape2D" id=2]
radius = 17.3333
height = 0.0

[sub_resource type="Gradient" id=6]
interpolation_mode = 2
colors = PoolColorArray( 0, 0.789062, 1, 1, 0, 0.0859375, 1, 0 )

[sub_resource type="GradientTexture" id=14]
gradient = SubResource( 6 )

[sub_resource type="ParticlesMaterial" id=8]
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
gravity = Vector3( 0, 0, 0 )
initial_velocity = 15.0
initial_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
tangential_accel = 12.0
tangential_accel_random = 1.0
scale = 0.25
scale_random = 0.05
color_ramp = SubResource( 14 )

[sub_resource type="Gradient" id=9]
colors = PoolColorArray( 0.913725, 0.0980392, 0.0980392, 0.513726, 1, 0.635294, 0, 0 )

[sub_resource type="GradientTexture" id=10]
gradient = SubResource( 9 )

[sub_resource type="Curve" id=11]
_data = [ Vector2( 0, 1 ), 0.0, 0.0, 0, 0, Vector2( 1, 0 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=12]
curve = SubResource( 11 )

[sub_resource type="ParticlesMaterial" id=13]
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
gravity = Vector3( 0, 300, 0 )
angular_velocity = 100.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 2.0
scale_curve = SubResource( 12 )
color_ramp = SubResource( 10 )

[sub_resource type="ParticlesMaterial" id=15]
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
gravity = Vector3( 0, 300, 0 )
angular_velocity = 100.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 2.0
scale_curve = SubResource( 12 )
color_ramp = SubResource( 10 )

[sub_resource type="ParticlesMaterial" id=16]
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
gravity = Vector3( 0, 300, 0 )
angular_velocity = 100.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 2.0
scale_curve = SubResource( 12 )

[node name="Player" type="Area2D"]
script = ExtResource( 7 )
__meta__ = {
"_edit_horizontal_guides_": [ -33.0 ]
}

[node name="parts" parent="." instance=ExtResource( 15 )]

[node name="AnimatedSprite_Deprecated" type="AnimatedSprite" parent="."]
visible = false
material = ExtResource( 16 )
position = Vector2( 0, 5 )
scale = Vector2( 2, 2 )
frames = SubResource( 1 )
animation = "hero"
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
position = Vector2( 0, -5 )
scale = Vector2( 1.5, 1.5 )
shape = SubResource( 2 )

[node name="PlayerAvoidBonusDetectorCollider" parent="." instance=ExtResource( 11 )]
visible = false
position = Vector2( 0, -3 )

[node name="ShieldEffect" type="Particles2D" parent="."]
visible = false
process_material = SubResource( 8 )
texture = ExtResource( 12 )

[node name="BulletIndicator" parent="." instance=ExtResource( 13 )]
position = Vector2( 0, 5 )
scale = Vector2( 1, 1e-05 )

[node name="Overheat" type="Node2D" parent="."]

[node name="Particles2D" type="Particles2D" parent="Overheat"]
position = Vector2( 0, -11 )
lifetime = 0.5
speed_scale = 2.0
process_material = SubResource( 13 )
texture = ExtResource( 14 )

[node name="AccuracyIndicator" type="Particles2D" parent="."]
position = Vector2( 0, -11 )
lifetime = 0.5
speed_scale = 2.0
process_material = SubResource( 15 )
texture = ExtResource( 14 )

[node name="Left" type="Particles2D" parent="AccuracyIndicator"]
modulate = Color( 0.921569, 1, 0, 0.12549 )
position = Vector2( -21, -6 )
lifetime = 0.5
speed_scale = 2.0
process_material = SubResource( 16 )
texture = ExtResource( 14 )

[node name="center" type="Particles2D" parent="AccuracyIndicator"]
modulate = Color( 0.921569, 1, 0, 0.12549 )
position = Vector2( 0, -19 )
lifetime = 0.5
speed_scale = 2.0
process_material = SubResource( 16 )
texture = ExtResource( 14 )

[node name="Right" type="Particles2D" parent="AccuracyIndicator"]
modulate = Color( 0.921569, 1, 0, 0.12549 )
position = Vector2( 21, -5 )
lifetime = 0.5
speed_scale = 2.0
process_material = SubResource( 16 )
texture = ExtResource( 14 )
