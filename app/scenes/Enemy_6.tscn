[gd_scene load_steps=6 format=2]

[ext_resource path="res://scripts/Enemy_6.gd" type="Script" id=1]
[ext_resource path="res://assets/blue-1.png" type="Texture" id=2]
[ext_resource path="res://assets/blue-2.png" type="Texture" id=3]

[sub_resource type="SpriteFrames" id=3]
animations = [ {
"frames": [ ExtResource( 2 ), ExtResource( 3 ) ],
"loop": true,
"name": "default",
"speed": 3.0
} ]

[sub_resource type="RectangleShape2D" id=4]
extents = Vector2( 23, 21.5 )

[node name="Enemy_6" type="Area2D"]
script = ExtResource( 1 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
position = Vector2( -4.61936e-07, -4 )
scale = Vector2( 1.7, 1.7 )
frames = SubResource( 3 )
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
position = Vector2( 0, -7.5 )
shape = SubResource( 4 )
