[gd_scene load_steps=13 format=2]

[ext_resource path="res://assets/7_shoot_sheet.png" type="Texture" id=1]
[ext_resource path="res://scripts/EnemyBullet_1.gd" type="Script" id=2]
[ext_resource path="res://assets/sounds/laser1.wav" type="AudioStream" id=3]

[sub_resource type="Gradient" id=10]
colors = PoolColorArray( 0.172549, 1, 0, 0.368627, 1, 1, 1, 0 )

[sub_resource type="GradientTexture2D" id=9]
gradient = SubResource( 10 )
fill = 1
fill_from = Vector2( 0.5, 0.5 )
fill_to = Vector2( 0.5, 0 )

[sub_resource type="AtlasTexture" id=1]
atlas = ExtResource( 1 )
region = Rect2( 176, 176, 12, 60 )

[sub_resource type="AtlasTexture" id=2]
atlas = ExtResource( 1 )
region = Rect2( 188, 176, 12, 60 )

[sub_resource type="AtlasTexture" id=3]
atlas = ExtResource( 1 )
region = Rect2( 200, 176, 12, 60 )

[sub_resource type="AtlasTexture" id=4]
atlas = ExtResource( 1 )
region = Rect2( 212, 176, 12, 60 )

[sub_resource type="AtlasTexture" id=5]
atlas = ExtResource( 1 )
region = Rect2( 224, 176, 12, 60 )

[sub_resource type="SpriteFrames" id=6]
animations = [ {
"frames": [ SubResource( 1 ), SubResource( 2 ), SubResource( 3 ), SubResource( 4 ), SubResource( 5 ) ],
"loop": true,
"name": "default",
"speed": 10.0
} ]

[sub_resource type="RectangleShape2D" id=7]
extents = Vector2( 4, 8 )

[node name="EnemyBullet_1" type="Area2D"]
script = ExtResource( 2 )

[node name="Sprite" type="Sprite" parent="."]
position = Vector2( -7.7486e-07, 0.499999 )
scale = Vector2( 0.21875, 0.328125 )
texture = SubResource( 9 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
modulate = Color( 0.188235, 0.886275, 0.0862745, 1 )
rotation = 3.14159
scale = Vector2( 1, 0.3 )
frames = SubResource( 6 )
frame = 2
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
position = Vector2( 0, 1 )
shape = SubResource( 7 )

[node name="AudioStreamPlayer2D" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 3 )
volume_db = -10.0
