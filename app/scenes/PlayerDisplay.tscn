[gd_scene load_steps=27 format=2]

[ext_resource path="res://assets/hero_parts/flame03_01.png" type="Texture" id=1]
[ext_resource path="res://assets/hero_parts/wings_03.png" type="Texture" id=2]
[ext_resource path="res://assets/hero_parts/flame03_03.png" type="Texture" id=3]
[ext_resource path="res://assets/hero_parts/flame03_02.png" type="Texture" id=4]
[ext_resource path="res://assets/hero_parts/body_03.png" type="Texture" id=5]
[ext_resource path="res://assets/hero_parts/flame01_02.png" type="Texture" id=6]
[ext_resource path="res://assets/hero_parts/wings_04.png" type="Texture" id=7]
[ext_resource path="res://assets/hero_parts/flame01_03.png" type="Texture" id=8]
[ext_resource path="res://assets/hero_parts/flame02_02.png" type="Texture" id=9]
[ext_resource path="res://addons/kenney_particle_pack/16/star_09_16.png" type="Texture" id=10]
[ext_resource path="res://assets/hero_parts/flame02_01.png" type="Texture" id=11]
[ext_resource path="res://assets/hero_parts/flame01_01.png" type="Texture" id=12]
[ext_resource path="res://assets/hero_parts/flame02_03.png" type="Texture" id=13]
[ext_resource path="res://assets/hero_parts/body_04.png" type="Texture" id=14]
[ext_resource path="res://assets/hero_parts/wings_01.png" type="Texture" id=15]
[ext_resource path="res://assets/hero_parts/body_01.png" type="Texture" id=16]
[ext_resource path="res://scripts/PlayerDisplay.gd" type="Script" id=17]
[ext_resource path="res://scenes/direction_shader.tres" type="Material" id=18]

[sub_resource type="Gradient" id=22]
offsets = PoolRealArray( 0.0847458, 1 )
colors = PoolColorArray( 0, 0.859375, 1, 0.345098, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=19]
gradient = SubResource( 22 )

[sub_resource type="Curve" id=23]
_data = [ Vector2( 0, 0.422727 ), 0.0, 0.0, 0, 0, Vector2( 1, 1 ), 0.0, 0.0, 0, 0 ]

[sub_resource type="CurveTexture" id=21]
curve = SubResource( 23 )

[sub_resource type="ParticlesMaterial" id=24]
flag_disable_z = true
direction = Vector3( 0, 1, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 20.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
scale = 3.0
scale_curve = SubResource( 21 )
color_ramp = SubResource( 19 )

[sub_resource type="SpriteFrames" id=25]
animations = [ {
"frames": [ ExtResource( 12 ), ExtResource( 6 ), ExtResource( 8 ) ],
"loop": true,
"name": "0",
"speed": 5.0
}, {
"frames": [ ExtResource( 11 ), ExtResource( 9 ), ExtResource( 13 ) ],
"loop": true,
"name": "1",
"speed": 5.0
}, {
"frames": [ ExtResource( 1 ), ExtResource( 4 ), ExtResource( 3 ) ],
"loop": true,
"name": "2",
"speed": 5.0
} ]

[sub_resource type="SpriteFrames" id=26]
animations = [ {
"frames": [ ExtResource( 15 ), ExtResource( 2 ), ExtResource( 7 ) ],
"loop": true,
"name": "default",
"speed": 5.0
} ]

[sub_resource type="SpriteFrames" id=27]
animations = [ {
"frames": [ ExtResource( 16 ), ExtResource( 5 ), ExtResource( 14 ) ],
"loop": true,
"name": "default",
"speed": 5.0
} ]

[node name="PlayerDisplay" type="Node2D"]
script = ExtResource( 17 )

[node name="FlameEffectL" type="Particles2D" parent="."]
position = Vector2( -21, 17 )
amount = 6
process_material = SubResource( 24 )
texture = ExtResource( 10 )

[node name="FlameEffectR" type="Particles2D" parent="."]
position = Vector2( 21, 17 )
amount = 6
process_material = SubResource( 24 )
texture = ExtResource( 10 )

[node name="FlameEffectC" type="Particles2D" parent="."]
position = Vector2( 0, 22 )
amount = 6
process_material = SubResource( 24 )
texture = ExtResource( 10 )

[node name="Flame" type="AnimatedSprite" parent="."]
position = Vector2( 0, 5 )
scale = Vector2( 2, 2 )
frames = SubResource( 25 )
animation = "0"
playing = true

[node name="Wings" type="AnimatedSprite" parent="."]
material = ExtResource( 18 )
position = Vector2( 0, 5 )
scale = Vector2( 2, 2 )
frames = SubResource( 26 )

[node name="Body" type="AnimatedSprite" parent="."]
position = Vector2( 0, 5 )
scale = Vector2( 2, 2 )
frames = SubResource( 27 )
