[gd_scene load_steps=6 format=2]

[ext_resource path="res://scripts/levels/LevelBoss4.gd" type="Script" id=1]
[ext_resource path="res://scenes/Bosses/Boss4/SpiderBoss.tscn" type="PackedScene" id=2]

[sub_resource type="Animation" id=1]
resource_name = "Entrance"
length = 3.0
tracks/0/type = "value"
tracks/0/path = NodePath("SpiderBoss:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 3 ),
"transitions": PoolRealArray( 0.5, 2 ),
"update": 0,
"values": [ Vector2( 512, -192 ), Vector2( 512, 128 ) ]
}

[sub_resource type="Animation" id=2]
resource_name = "Wiggle"
length = 15.0
loop = true
tracks/0/type = "bezier"
tracks/0/path = NodePath("SpiderBoss:position:x")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"points": PoolRealArray( 512, -0.25, 0, 1.2, -26.7184, 256, -2.1, 95.54, 3.5, 140.121, 768, -2.6, -211.39, 1.9, -95.4803, 512, -1.3, 98.107, 0.25, 0, 512, -0.25, 0, 0.25, 0 ),
"times": PoolRealArray( 0, 2.5, 7.5, 10, 15 )
}

[sub_resource type="Animation" id=3]
resource_name = "Wiggle"
length = 8.0
loop = true
tracks/0/type = "bezier"
tracks/0/path = NodePath("SpiderBoss:position:y")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"points": PoolRealArray( 128, -0.25, 0, 0.6, 6.48384, 192, -1.2, -16.5161, 1.5, -4.51614, 90, -1.1, 5.48383, 1, 8.48383, 128, -0.7, -4.51616, 0.25, 0 ),
"times": PoolRealArray( 0, 2, 6, 8 )
}

[node name="LevelNode" type="Node"]
script = ExtResource( 1 )
__meta__ = {
"_edit_horizontal_guides_": [ 128.0, 64.0, 192.0 ],
"_edit_vertical_guides_": [ 128.0, 896.0 ]
}

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "Entrance"
anims/Entrance = SubResource( 1 )

[node name="SpiderBoss" parent="." instance=ExtResource( 2 )]
position = Vector2( 512, 128 )

[node name="AnimationPlayerWiggleX" type="AnimationPlayer" parent="."]
anims/Wiggle = SubResource( 2 )

[node name="AnimationPlayerWiggleY" type="AnimationPlayer" parent="."]
anims/Wiggle = SubResource( 3 )
