[gd_scene load_steps=4 format=2]

[ext_resource path="res://scripts/levels/LevelBoss2.gd" type="Script" id=1]
[ext_resource path="res://scenes/Bosses/Boss2/DragonBoss.tscn" type="PackedScene" id=2]

[sub_resource type="Curve2D" id=1]
_data = {
"points": PoolVector2Array( 83.1993, 11.118, -83.1993, -11.118, 1320.57, 168.689, 63.58, -12.454, -63.58, 12.454, 982.492, 198.881, 298.82, -5.02577, -298.82, 5.02577, 518, 250, 157.27, 26.7297, -157.27, -26.7297, 26.361, 214.136, 185.456, 22.7713, -185.456, -22.7713, -266, 184, -73.2142, -5.24662, 73.2142, 5.24662, -262.134, 258.407, -377.147, 44.8985, 377.147, -44.8985, 510.928, 219.265, -214.649, -20.9103, 214.649, 20.9103, 1151.4, 270.356, 0, 0, 0, 0, 1476.46, 316.73 )
}

[node name="LevelNode" type="Node"]
script = ExtResource( 1 )

[node name="Path2DLTR" type="Path2D" parent="."]
position = Vector2( 4, -113 )
rotation = 0.0389617
curve = SubResource( 1 )

[node name="PathFollow2D" type="PathFollow2D" parent="Path2DLTR"]
position = Vector2( 1320.57, 168.689 )
rotation = -3.13559
lookahead = 100.0

[node name="Dragon" parent="Path2DLTR/PathFollow2D" instance=ExtResource( 2 )]
position = Vector2( 208.185, 39.3585 )
rotation = 3.07335
