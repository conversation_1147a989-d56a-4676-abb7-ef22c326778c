[gd_scene load_steps=13 format=2]

[ext_resource path="res://scenes/Themes/tryu_theme.tres" type="Theme" id=1]
[ext_resource path="res://addons/kenney_particle_pack/star_01.png" type="Texture" id=2]
[ext_resource path="res://scripts/StatsDisplay.gd" type="Script" id=3]

[sub_resource type="Gradient" id=17]
offsets = PoolRealArray( 0, 0.274298, 1 )
colors = PoolColorArray( 1, 1, 1, 0, 1, 1, 1, 0.545098, 0, 0.882812, 1, 1 )

[sub_resource type="GradientTexture" id=2]
gradient = SubResource( 17 )

[sub_resource type="ParticlesMaterial" id=18]
flag_disable_z = true
direction = Vector3( 0, 1, 0 )
spread = 180.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 150.0
initial_velocity_random = 0.5
angular_velocity = 100.0
angular_velocity_random = 1.0
orbit_velocity = 0.05
orbit_velocity_random = 0.0
linear_accel = 10.0
tangential_accel = 100.0
tangential_accel_random = 1.0
angle = 360.0
angle_random = 1.0
scale = 0.02
scale_random = 0.01
color_ramp = SubResource( 2 )
hue_variation = 0.01
hue_variation_random = 1.0

[sub_resource type="Shader" id=7]
code = "shader_type canvas_item;

uniform float brightness : hint_range(0.001, 2.0) = 1.0;
uniform float anchor : hint_range(-1.0,0.0) = -0.5;
//The point where all the lines come from.
uniform float speed_scale = 1.0;
uniform float fov : hint_range(0.001, 1.0) = 0.2;
uniform float line_count = 1.0;
uniform vec4 background_color : hint_color = vec4(0.0, 0.1, 0.2, 1.0);
uniform vec4 grid_color : hint_color = vec4(1.0, 0.5, 1.0, 1.0);

float grid(vec2 uv, float batt) {
    vec2 size = vec2(uv.y, uv.y * uv.y * 0.2) * 0.01* (batt + 0.05);
    uv += vec2(0.0, TIME * speed_scale);
    uv = abs(fract(uv) - 0.5);
 	vec2 lines = smoothstep(size, vec2(0.0), uv);
 	lines += smoothstep(size * 5.0, vec2(0.0), uv) * 0.4 * batt;
    return lines.x + lines.y;
}
void fragment() {
	vec2 uv = UV;
	vec4 col = background_color;
    uv.y = 3.0 / (abs(uv.y + fov) + 0.05);
	uv.x += anchor;
    uv.x *= uv.y * line_count;
    float gridVal = grid(uv, brightness);
    col = mix(background_color, grid_color, gridVal);
	COLOR = col;
}"

[sub_resource type="ShaderMaterial" id=19]
shader = SubResource( 7 )
shader_param/brightness = 0.855
shader_param/anchor = -0.5
shader_param/speed_scale = 1.0
shader_param/fov = 0.2
shader_param/line_count = 3.0
shader_param/background_color = Color( 0, 0, 0, 1 )
shader_param/grid_color = Color( 0.207843, 0.0352941, 0.227451, 1 )

[sub_resource type="ProxyTexture" id=20]

[sub_resource type="DynamicFontData" id=22]
font_path = "res://assets/fonts/Commodore Pixelized v1.2.ttf"

[sub_resource type="DynamicFont" id=16]
size = 44
font_data = SubResource( 22 )

[sub_resource type="Animation" id=21]
resource_name = "HighScoreAnim"
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("HighScore:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5, 0.9 ),
"transitions": PoolRealArray( 1, 1, 1 ),
"update": 0,
"values": [ Color( 1, 1, 1, 1 ), Color( 1, 0.2, 0.2, 1 ), Color( 1, 1, 1, 1 ) ]
}

[node name="Stats" type="Node2D"]
script = ExtResource( 3 )
__meta__ = {
"_edit_vertical_guides_": [ 567.0, 582.0 ]
}

[node name="Particles2D" type="Particles2D" parent="."]
position = Vector2( 520, 272 )
amount = 100
lifetime = 6.0
speed_scale = 0.5
process_material = SubResource( 18 )
texture = ExtResource( 2 )

[node name="Sprite" type="Sprite" parent="."]
material = SubResource( 19 )
position = Vector2( 507, 473.5 )
scale = Vector2( 1062, 227 )
texture = SubResource( 20 )

[node name="Stats" type="Label" parent="."]
margin_left = 10.0
margin_top = 18.0
margin_right = 1015.0
margin_bottom = 95.0
focus_mode = 2
custom_fonts/font = SubResource( 16 )
text = "Stats"
align = 1
valign = 1

[node name="Ok" type="Button" parent="."]
margin_left = 832.0
margin_top = 18.0
margin_right = 1005.0
margin_bottom = 93.0
theme = ExtResource( 1 )
text = "OK"

[node name="FinalScore" type="Label" parent="."]
margin_left = 10.0
margin_top = 502.0
margin_right = 843.0
margin_bottom = 553.0
rect_scale = Vector2( 1.2, 1.2 )
custom_fonts/font = SubResource( 16 )
text = "000000000"
align = 2
valign = 1

[node name="HighScore" type="Label" parent="."]
modulate = Color( 1, 0.600077, 0.600077, 1 )
margin_left = 10.0
margin_top = 502.0
margin_right = 843.0
margin_bottom = 553.0
rect_scale = Vector2( 1.2, 1.2 )
custom_fonts/font = SubResource( 16 )
text = "NEW HIGH!"
valign = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "HighScoreAnim"
anims/HighScoreAnim = SubResource( 21 )

[node name="Details" type="HBoxContainer" parent="."]
margin_left = 12.0
margin_top = 121.0
margin_right = 1250.0
margin_bottom = 617.0
rect_scale = Vector2( 0.8, 0.8 )

[node name="Labels" type="VBoxContainer" parent="Details"]
margin_right = 508.0
margin_bottom = 496.0
size_flags_horizontal = 3
size_flags_stretch_ratio = 0.7

[node name="Level" type="Label" parent="Details/Labels"]
margin_right = 508.0
margin_bottom = 44.0
custom_fonts/font = SubResource( 16 )
text = "Waves: "
align = 2
valign = 1

[node name="Duration" type="Label" parent="Details/Labels"]
margin_top = 48.0
margin_right = 508.0
margin_bottom = 92.0
custom_fonts/font = SubResource( 16 )
text = "Duration: "
align = 2
valign = 1

[node name="Crystals" type="Label" parent="Details/Labels"]
margin_top = 96.0
margin_right = 508.0
margin_bottom = 140.0
custom_fonts/font = SubResource( 16 )
text = "Crystals: "
align = 2
valign = 1

[node name="Accuracy" type="Label" parent="Details/Labels"]
margin_top = 144.0
margin_right = 508.0
margin_bottom = 188.0
custom_fonts/font = SubResource( 16 )
text = "Accuracy: "
align = 2
valign = 1

[node name="EntryKills" type="Label" parent="Details/Labels"]
margin_top = 192.0
margin_right = 508.0
margin_bottom = 236.0
custom_fonts/font = SubResource( 16 )
text = "Entry Kills: "
align = 2
valign = 1

[node name="RowsCleared" type="Label" parent="Details/Labels"]
margin_top = 240.0
margin_right = 508.0
margin_bottom = 284.0
custom_fonts/font = SubResource( 16 )
text = "Rows Cleared: "
align = 2
valign = 1

[node name="CloseKills" type="Label" parent="Details/Labels"]
margin_top = 288.0
margin_right = 508.0
margin_bottom = 332.0
custom_fonts/font = SubResource( 16 )
text = "Close Kills: "
align = 2
valign = 1

[node name="Difficulty" type="Label" parent="Details/Labels"]
margin_top = 336.0
margin_right = 508.0
margin_bottom = 380.0
custom_fonts/font = SubResource( 16 )
text = "Difficulty: "
align = 2
valign = 1

[node name="Values" type="VBoxContainer" parent="Details"]
margin_left = 512.0
margin_right = 1238.0
margin_bottom = 496.0
size_flags_horizontal = 3

[node name="Level" type="Label" parent="Details/Values"]
margin_right = 726.0
margin_bottom = 44.0
custom_fonts/font = SubResource( 16 )
valign = 1
clip_text = true

[node name="Duration" type="Label" parent="Details/Values"]
margin_top = 48.0
margin_right = 726.0
margin_bottom = 92.0
custom_fonts/font = SubResource( 16 )
valign = 1

[node name="Crystals" type="Label" parent="Details/Values"]
margin_top = 96.0
margin_right = 726.0
margin_bottom = 140.0
custom_fonts/font = SubResource( 16 )
valign = 1

[node name="Accuracy" type="Label" parent="Details/Values"]
margin_top = 144.0
margin_right = 726.0
margin_bottom = 188.0
custom_fonts/font = SubResource( 16 )
valign = 1

[node name="EntryKills" type="Label" parent="Details/Values"]
margin_top = 192.0
margin_right = 726.0
margin_bottom = 236.0
custom_fonts/font = SubResource( 16 )
valign = 1

[node name="RowsCleared" type="Label" parent="Details/Values"]
margin_top = 240.0
margin_right = 726.0
margin_bottom = 284.0
custom_fonts/font = SubResource( 16 )
valign = 1

[node name="CloseKills" type="Label" parent="Details/Values"]
margin_top = 288.0
margin_right = 726.0
margin_bottom = 332.0
custom_fonts/font = SubResource( 16 )
valign = 1

[node name="Difficulty" type="Label" parent="Details/Values"]
margin_top = 336.0
margin_right = 726.0
margin_bottom = 380.0
custom_fonts/font = SubResource( 16 )
valign = 1

[node name="BackgroundMusicPlayer" type="AudioStreamPlayer" parent="."]
volume_db = -15.0

[node name="Void" type="Label" parent="."]
visible = false
modulate = Color( 1, 0, 0, 0.509804 )
margin_left = 457.0
margin_top = 260.0
margin_right = 593.0
margin_bottom = 304.0
rect_rotation = 24.2
rect_scale = Vector2( 7, 7 )
rect_pivot_offset = Vector2( 67.0968, 22.5921 )
custom_fonts/font = SubResource( 16 )
text = "VOID"
align = 1
valign = 1
