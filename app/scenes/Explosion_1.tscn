[gd_scene load_steps=24 format=2]

[ext_resource path="res://assets/1_asteroid_sprites.png" type="Texture" id=1]
[ext_resource path="res://scripts/Explosion_1.gd" type="Script" id=2]
[ext_resource path="res://assets/sounds/zap.wav" type="AudioStream" id=3]
[ext_resource path="res://addons/kenney_particle_pack/16/spark_02_16.png" type="Texture" id=4]
[ext_resource path="res://addons/kenney_particle_pack/light_03.png" type="Texture" id=5]

[sub_resource type="Gradient" id=15]
colors = PoolColorArray( 1, 1, 1, 1, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=16]
gradient = SubResource( 15 )

[sub_resource type="ParticlesMaterial" id=17]
flag_disable_z = true
direction = Vector3( 0, -1, 0 )
gravity = Vector3( 0, 100, 0 )
initial_velocity = 100.0
initial_velocity_random = 1.0
angular_velocity = 100.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
angle = 100.0
angle_random = 1.0
scale = 2.0
scale_random = 1.0
color_ramp = SubResource( 16 )
hue_variation_random = 1.0

[sub_resource type="AtlasTexture" id=1]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=2]
atlas = ExtResource( 1 )
region = Rect2( 128, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=3]
atlas = ExtResource( 1 )
region = Rect2( 256, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=4]
atlas = ExtResource( 1 )
region = Rect2( 384, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=5]
atlas = ExtResource( 1 )
region = Rect2( 512, 0, 128, 128 )

[sub_resource type="AtlasTexture" id=6]
atlas = ExtResource( 1 )
region = Rect2( 0, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=7]
atlas = ExtResource( 1 )
region = Rect2( 128, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=8]
atlas = ExtResource( 1 )
region = Rect2( 256, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=9]
atlas = ExtResource( 1 )
region = Rect2( 384, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=10]
atlas = ExtResource( 1 )
region = Rect2( 512, 128, 128, 128 )

[sub_resource type="AtlasTexture" id=11]
atlas = ExtResource( 1 )
region = Rect2( 0, 256, 128, 128 )

[sub_resource type="AtlasTexture" id=12]
atlas = ExtResource( 1 )
region = Rect2( 128, 256, 128, 128 )

[sub_resource type="SpriteFrames" id=13]
animations = [ {
"frames": [ SubResource( 1 ), SubResource( 2 ), SubResource( 3 ), SubResource( 4 ), SubResource( 5 ), SubResource( 6 ), SubResource( 7 ), SubResource( 8 ), SubResource( 9 ), SubResource( 10 ), SubResource( 11 ), SubResource( 12 ) ],
"loop": true,
"name": "default",
"speed": 25.0
} ]

[sub_resource type="ParticlesMaterial" id=14]
emission_shape = 1
emission_sphere_radius = 20.0
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
gravity = Vector3( 0, 1, 0 )
initial_velocity = 50.0
angular_velocity = 233.36
angular_velocity_random = 0.49
orbit_velocity = 1.0
orbit_velocity_random = 0.32
linear_accel = -75.89
radial_accel = 100.0
scale = 10.0
scale_random = 0.3
color = Color( 1, 0.631373, 0, 0.494118 )
hue_variation = -1.0
hue_variation_random = 1.0

[sub_resource type="Animation" id=18]
resource_name = "ExplosionTwirl"
tracks/0/type = "value"
tracks/0/path = NodePath("light:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8, 1 ),
"transitions": PoolRealArray( 1, 0.5, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0.2, 0.2 ), Vector2( 0.05, 0.05 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("light:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.7 ),
"transitions": PoolRealArray( 1, 0.5 ),
"update": 0,
"values": [ 0.0, -360.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("light:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.5, 1 ),
"transitions": PoolRealArray( 1, 0.5, 2 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.196078 ), Color( 0.372549, 0.960784, 0.380392, 1 ), Color( 1, 1, 1, 0 ) ]
}

[node name="Explosion_1" type="Node2D"]
script = ExtResource( 2 )

[node name="Particles2DParts" type="Particles2D" parent="."]
modulate = Color( 0, 1, 0.882353, 0.588235 )
amount = 15
lifetime = 1.5
speed_scale = 1.5
explosiveness = 1.0
randomness = 1.0
process_material = SubResource( 17 )
texture = ExtResource( 4 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
modulate = Color( 1, 0.376471, 0, 1 )
frames = SubResource( 13 )
frame = 11
playing = true

[node name="Particles2DExplosion" type="Particles2D" parent="."]
amount = 20
speed_scale = 4.79
randomness = 1.0
fixed_fps = 10
process_material = SubResource( 14 )

[node name="AudioStreamPlayer2D" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 3 )
volume_db = -10.0
pitch_scale = 0.2

[node name="light" type="Sprite" parent="."]
modulate = Color( 1, 1, 1, 0 )
rotation = -6.28319
scale = Vector2( 0.05, 0.05 )
texture = ExtResource( 5 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "ExplosionTwirl"
playback_speed = 2.0
anims/ExplosionTwirl = SubResource( 18 )
