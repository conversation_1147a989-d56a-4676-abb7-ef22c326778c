[gd_scene load_steps=10 format=2]

[ext_resource path="res://scripts/Enemy_8.gd" type="Script" id=1]
[ext_resource path="res://assets/eye_guy_1.png" type="Texture" id=2]
[ext_resource path="res://assets/eye_guy_2.png" type="Texture" id=3]
[ext_resource path="res://addons/kenney_particle_pack/slash_01.png" type="Texture" id=4]

[sub_resource type="SpriteFrames" id=1]
animations = [ {
"frames": [ ExtResource( 2 ), ExtResource( 3 ) ],
"loop": true,
"name": "default",
"speed": 1.0
} ]

[sub_resource type="RectangleShape2D" id=2]
extents = Vector2( 14.6667, 14.6667 )

[sub_resource type="Gradient" id=8]
interpolation_mode = 2
colors = PoolColorArray( 0, 0.789062, 1, 1, 0, 0.0859375, 1, 0 )

[sub_resource type="GradientTexture" id=10]
gradient = SubResource( 8 )

[sub_resource type="ParticlesMaterial" id=9]
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
gravity = Vector3( 0, 0, 0 )
initial_velocity = 15.0
initial_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
tangential_accel = 12.0
tangential_accel_random = 1.0
scale = 0.15
scale_random = 0.05
color_ramp = SubResource( 10 )

[node name="Enemy_8" type="Area2D"]
script = ExtResource( 1 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
scale = Vector2( 2.75, 2.75 )
frames = SubResource( 1 )
playing = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
position = Vector2( -7.15256e-07, 7.15256e-07 )
scale = Vector2( 1.5, 1.5 )
shape = SubResource( 2 )

[node name="ShieldEffect" type="Particles2D" parent="."]
position = Vector2( 0, 9 )
process_material = SubResource( 9 )
texture = ExtResource( 4 )
