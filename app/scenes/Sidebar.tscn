[gd_scene load_steps=7 format=2]

[sub_resource type="Shader" id=1]
code = "shader_type canvas_item;

uniform bool flip = false;

void fragment() {
	vec4 textureColor = texture(TEXTURE, UV + vec2(0, flip ? 0.08 : - 0.08 ) * TIME);
	COLOR = textureColor;
	COLOR.w = smoothstep(0.0, 2.2, UV.x);
	COLOR.w = int(UV.x*50.0) % 2 == 0 ? COLOR.w : 0.0;
}"

[sub_resource type="ShaderMaterial" id=2]
shader = SubResource( 1 )
shader_param/flip = false

[sub_resource type="OpenSimplexNoise" id=3]
seed = 2
period = 14.9
persistence = 0.0
lacunarity = 1.63

[sub_resource type="NoiseTexture" id=4]
width = 100
height = 700
seamless = true
noise = SubResource( 3 )

[sub_resource type="ShaderMaterial" id=6]
shader = SubResource( 1 )
shader_param/flip = true

[sub_resource type="RectangleShape2D" id=5]
extents = Vector2( 48, 360 )

[node name="Sidebar" type="Area2D"]

[node name="Sprite2" type="Sprite" parent="."]
material = SubResource( 2 )
position = Vector2( 976, 300 )
scale = Vector2( 1, 0.857143 )
texture = SubResource( 4 )

[node name="Sprite" type="Sprite" parent="."]
material = SubResource( 6 )
position = Vector2( 48, 298.44 )
rotation = 3.14159
scale = Vector2( 1, 0.854668 )
texture = SubResource( 4 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
position = Vector2( 48, 312 )
shape = SubResource( 5 )

[node name="CollisionShape2D2" type="CollisionShape2D" parent="."]
visible = false
position = Vector2( 976, 312 )
shape = SubResource( 5 )
