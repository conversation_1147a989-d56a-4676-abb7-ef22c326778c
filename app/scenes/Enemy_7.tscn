[gd_scene load_steps=14 format=2]

[ext_resource path="res://assets/boss-no-wings.png" type="Texture" id=1]
[ext_resource path="res://assets/boss-no-wings2.png" type="Texture" id=2]
[ext_resource path="res://scripts/Enemy_7.gd" type="Script" id=3]
[ext_resource path="res://scenes/Enemy_7_right_wing.tscn" type="PackedScene" id=4]
[ext_resource path="res://scenes/Enemy_7_left_wing.tscn" type="PackedScene" id=5]
[ext_resource path="res://assets/boss-no-wings3.png" type="Texture" id=6]
[ext_resource path="res://addons/kenney_particle_pack/spark_04.png" type="Texture" id=7]

[sub_resource type="SpriteFrames" id=3]
animations = [ {
"frames": [ ExtResource( 1 ), ExtResource( 2 ), ExtResource( 6 ), ExtResource( 2 ) ],
"loop": true,
"name": "default",
"speed": 3.0
} ]

[sub_resource type="CircleShape2D" id=4]
radius = 28.0179

[sub_resource type="Animation" id=5]
resource_name = "wings"
loop = true
tracks/0/type = "value"
tracks/0/path = NodePath("Wing_left:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.5, 1 ),
"transitions": PoolRealArray( 1, 0.5, 0.5 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, -5 ), Vector2( 0, 0 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("Wing_right:position")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.5, 1 ),
"transitions": PoolRealArray( 1, 0.5, 0.5 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0, -5 ), Vector2( 0, 0 ) ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("AnimatedSprite:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.5, 1 ),
"transitions": PoolRealArray( 1, 2, 2 ),
"update": 0,
"values": [ Vector2( 0.0844648, 7.99998 ), Vector2( 0.084, 3 ), Vector2( 0.0844648, 7.99998 ) ]
}

[sub_resource type="Gradient" id=62]
offsets = PoolRealArray( 0.002849, 0.381766, 0.666667 )
colors = PoolColorArray( 0.997559, 0.440329, 0.962732, 1, 1, 0, 0, 1, 0, 1, 0.227451, 0 )

[sub_resource type="GradientTexture" id=61]
gradient = SubResource( 62 )

[sub_resource type="ParticlesMaterial" id=63]
emission_shape = 1
emission_sphere_radius = 500.0
flag_align_y = true
flag_rotate_y = true
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
orbit_velocity = 0.0
orbit_velocity_random = 0.0
radial_accel = 100.0
radial_accel_random = 1.0
scale = 0.1
scale_random = 0.1
color_ramp = SubResource( 61 )

[node name="Enemy_7" type="Area2D"]
script = ExtResource( 3 )

[node name="AnimatedSprite" type="AnimatedSprite" parent="."]
position = Vector2( 0.0844648, 7.99998 )
scale = Vector2( 0.985916, 0.985916 )
frames = SubResource( 3 )
frame = 3
playing = true

[node name="Wing_left" parent="." instance=ExtResource( 5 )]
scale = Vector2( 1.5, 1.5 )

[node name="Wing_right" parent="." instance=ExtResource( 4 )]
scale = Vector2( 1.5, 1.5 )

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2( -0.10387, 3.8125 )
shape = SubResource( 4 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "wings"
anims/wings = SubResource( 5 )

[node name="Scratch" type="Particles2D" parent="."]
modulate = Color( 1, 1, 1, 0.713726 )
position = Vector2( -3.8147e-06, 6 )
rotation = -0.0191986
scale = Vector2( 0.1, 0.048 )
amount = 15
lifetime = 0.5
local_coords = false
process_material = SubResource( 63 )
texture = ExtResource( 7 )
