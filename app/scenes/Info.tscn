[gd_scene load_steps=10 format=2]

[ext_resource path="res://scripts/Info.gd" type="Script" id=1]
[ext_resource path="res://assets/fonts/c64.tres" type="DynamicFont" id=2]
[ext_resource path="res://scenes/Themes/tryu_theme.tres" type="Theme" id=3]
[ext_resource path="res://scenes/Themes/tab_theme.tres" type="Theme" id=4]
[ext_resource path="res://scenes/Themes/item_list.tres" type="Theme" id=5]

[sub_resource type="DynamicFontData" id=13]
font_path = "res://assets/fonts/Commodore Pixelized v1.2.ttf"

[sub_resource type="DynamicFont" id=12]
size = 44
font_data = SubResource( 13 )

[sub_resource type="DynamicFontData" id=14]
font_path = "res://assets/fonts/arial_narrow_7.ttf"

[sub_resource type="DynamicFont" id=15]
size = 20
font_data = SubResource( 14 )

[node name="Control" type="Control"]
anchor_right = 1.0
anchor_bottom = 1.0
rect_pivot_offset = Vector2( 80, 14 )
script = ExtResource( 1 )
__meta__ = {
"_edit_horizontal_guides_": [ 555.0 ],
"_edit_vertical_guides_": [ 999.0, 27.0 ]
}

[node name="ColorRect" type="ColorRect" parent="."]
margin_right = 1024.0
margin_bottom = 576.0
color = Color( 0, 0, 0, 1 )

[node name="Title" type="Label" parent="."]
margin_left = 21.0
margin_top = 15.0
margin_right = 157.0
margin_bottom = 66.0
custom_fonts/font = SubResource( 12 )
text = "Help"

[node name="CloseButton" type="Button" parent="."]
margin_left = 821.0
margin_top = 14.0
margin_right = 998.0
margin_bottom = 65.0
theme = ExtResource( 3 )
custom_fonts/font = ExtResource( 2 )
text = "Close"

[node name="TabContainer" type="TabContainer" parent="."]
margin_left = 40.0
margin_top = 83.0
margin_right = 978.0
margin_bottom = 540.0
focus_mode = 2
theme = ExtResource( 4 )
tab_align = 0

[node name="Help" type="Control" parent="TabContainer"]
anchor_right = 1.0
anchor_bottom = 1.0
margin_top = 47.0

[node name="About" type="Label" parent="TabContainer/Help"]
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 16.0
margin_top = 17.0
margin_right = -165.0
margin_bottom = -73.0
rect_scale = Vector2( 1.2, 1.2 )
text = "- Maneuvering Your Vessel: Command your ship with the wisdom of the movement keys and unleash your fury with the 'Fire' key. Let your shots rain upon the enemies of the galaxy.
- Gathering Cosmic Power-Ups: Seize the gifts of the cosmos – power-ups, born from the remnants of your fallen adversaries.
- Crystals of Power: Claim crystals from your conquests. They are the keys to the enhancement of your vessel.
- Preparing for Battle: Before each new skirmish, upgrade your ship in the shop. More firepower, more glory!
- Confronting Galactic Tyrants: Face the monstrous bosses with courage. Strike with precision and valor.
- Life and Honor: Your lives are precious. Guard them as you accumulate points, symbols of your bravery.
- Finish the Game: Triumph in your journey to unlock new ships and expand your fleet.
- Achievements of Valor: Test your skills and earn achievements that prove your mastery of the stars.

Game Modes:

- Campaign: Dive into hand-crafted levels, perfect for those who seek to learn patterns, master strategies, and uncover every secret of the cosmos.
- Flow: For the thrill-seekers, this mode delivers action-packed battles with randomized enemy patterns and a faster pace. Pure chaos, pure adrenaline!"
autowrap = true

[node name="UI" type="Control" parent="TabContainer"]
visible = false
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 4.0
margin_top = 32.0
margin_right = -4.0
margin_bottom = -4.0

[node name="About" type="Label" parent="TabContainer/UI"]
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 16.0
margin_top = 17.0
margin_right = -165.0
margin_bottom = -73.0
rect_scale = Vector2( 1.2, 1.2 )
text = "Game UI help:
-------------------

S: [ speed ] - how fast you can move
B: [ max bullets ] - how many bullets you can have on the screen
W: [ weapon type ] - your weapon type]
SH: [ number of shields ] - you can take extra hits
A: [ auto fire ] - you don't have to press the fire button
/abc/: [ collected cursed modules ] - try to combine them for secrets

Effects: 

SR - super rapidfire, IV - invincibility, CX - cristal explosion, LW - extra luck, CM - cristal magnet"
autowrap = true

[node name="Powerups" type="Control" parent="TabContainer"]
visible = false
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 4.0
margin_top = 32.0
margin_right = -4.0
margin_bottom = -4.0

[node name="ItemList" type="ItemList" parent="TabContainer/Powerups"]
margin_left = -2.0
margin_top = 2.0
margin_right = 930.0
margin_bottom = 421.0
theme = ExtResource( 5 )
custom_colors/guide_color = Color( 0.262745, 0.262745, 0.262745, 0 )
custom_colors/font_color_selected = Color( 1, 1, 1, 1 )
custom_colors/font_color = Color( 1, 1, 1, 1 )
custom_fonts/font = SubResource( 15 )
items = [ "Item 0", null, false, "Item 1", null, false, "Item 2", null, false, "Item 3", null, false, "Item 4", null, false, "Item 5", null, false, "Item 6", null, false, "Item 7", null, false ]
allow_search = false
max_columns = 3
fixed_column_width = 290

[node name="About" type="Control" parent="TabContainer"]
visible = false
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 4.0
margin_top = 32.0
margin_right = -4.0
margin_bottom = -4.0

[node name="About" type="Label" parent="TabContainer/About"]
anchor_right = 1.0
anchor_bottom = 1.0
margin_left = 16.0
margin_top = 17.0
margin_right = -165.0
margin_bottom = -77.0
rect_scale = Vector2( 1.2, 1.2 )
text = "Development:
  Boldizsar Bednarik
  <EMAIL>
  https://tryusora.com

Design, Creativity:
  Bertalan Bednarik , Biborka Bednarik, Daniil Luss

Testing:
  Biborka Bednarik, Bertalan Bednarik, Csaba Dzsurnik, Simon Gaudin

Music:
  Aries Beats, Karl Casey @ White Bat Audio, Infraction Music
	
Sound Effects:
  https://freesound.org

All Rights Reserved."
