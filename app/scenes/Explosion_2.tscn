[gd_scene load_steps=6 format=2]

[ext_resource path="res://scripts/Explosion_1.gd" type="Script" id=2]
[ext_resource path="res://assets/sounds/zap.wav" type="AudioStream" id=3]
[ext_resource path="res://addons/kenney_particle_pack/light_03.png" type="Texture" id=5]

[sub_resource type="ParticlesMaterial" id=19]
emission_shape = 1
emission_sphere_radius = 0.01
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 180.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 20.0
orbit_velocity = 1.0
orbit_velocity_random = 0.25
scale = 5.0
scale_random = 1.0
color = Color( 1, 1, 1, 0.494118 )
hue_variation_random = 1.0

[sub_resource type="Animation" id=18]
resource_name = "ExplosionTwirl"
tracks/0/type = "value"
tracks/0/path = NodePath("light:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/keys = {
"times": PoolRealArray( 0, 0.8, 1 ),
"transitions": PoolRealArray( 1, 0.5, 1 ),
"update": 0,
"values": [ Vector2( 0, 0 ), Vector2( 0.07, 0.1 ), Vector2( 0.05, 0.05 ) ]
}
tracks/1/type = "value"
tracks/1/path = NodePath("light:rotation_degrees")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/keys = {
"times": PoolRealArray( 0, 0.7 ),
"transitions": PoolRealArray( 1, 0.5 ),
"update": 0,
"values": [ 0.0, -360.0 ]
}
tracks/2/type = "value"
tracks/2/path = NodePath("light:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/keys = {
"times": PoolRealArray( 0, 0.5, 1 ),
"transitions": PoolRealArray( 1, 0.5, 2 ),
"update": 0,
"values": [ Color( 1, 1, 1, 0.196078 ), Color( 0.901961, 0.960784, 0.372549, 1 ), Color( 1, 1, 1, 0 ) ]
}

[node name="Explosion_2" type="Node2D"]
script = ExtResource( 2 )

[node name="Particles2DExplosion" type="Particles2D" parent="."]
amount = 20
speed_scale = 4.79
randomness = 1.0
fixed_fps = 10
process_material = SubResource( 19 )

[node name="AudioStreamPlayer2D" type="AudioStreamPlayer2D" parent="."]
stream = ExtResource( 3 )
volume_db = -10.0
pitch_scale = 0.2

[node name="light" type="Sprite" parent="."]
modulate = Color( 1, 1, 1, 0.196078 )
scale = Vector2( 1e-05, 1e-05 )
texture = ExtResource( 5 )

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
autoplay = "ExplosionTwirl"
playback_speed = 2.0
anims/ExplosionTwirl = SubResource( 18 )
