[gd_scene load_steps=31 format=2]

[ext_resource path="res://assets/Debris.png" type="Texture" id=1]
[ext_resource path="res://scripts/DebrisBase.gd" type="Script" id=2]
[ext_resource path="res://addons/kenney_particle_pack/16/dirt_03_16.png" type="Texture" id=3]

[sub_resource type="CircleShape2D" id=1]
radius = 8.0

[sub_resource type="Gradient" id=30]
offsets = PoolRealArray( 0, 0.273551, 0.570652, 1 )
colors = PoolColorArray( 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0.933333, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=33]
gradient = SubResource( 30 )

[sub_resource type="ParticlesMaterial" id=32]
emission_shape = 1
emission_sphere_radius = 5.0
flag_disable_z = true
direction = Vector3( 0, -1, 0 )
spread = 0.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 10.0
angular_velocity = 100.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
linear_accel = 10.0
radial_accel_random = 1.0
damping = 10.0
damping_random = 1.0
scale = 0.5
scale_random = 1.0
color_ramp = SubResource( 33 )
hue_variation = 0.22
hue_variation_random = 0.48

[sub_resource type="AtlasTexture" id=2]
atlas = ExtResource( 1 )
region = Rect2( 0, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=4]
atlas = ExtResource( 1 )
region = Rect2( 32, 0, 16, 16 )

[sub_resource type="AtlasTexture" id=5]
atlas = ExtResource( 1 )
region = Rect2( 0, 16, 16, 16 )

[sub_resource type="AtlasTexture" id=6]
atlas = ExtResource( 1 )
region = Rect2( 16, 16, 16, 16 )

[sub_resource type="AtlasTexture" id=7]
atlas = ExtResource( 1 )
region = Rect2( 32, 16, 16, 16 )

[sub_resource type="AtlasTexture" id=8]
atlas = ExtResource( 1 )
region = Rect2( 48, 16, 16, 16 )

[sub_resource type="AtlasTexture" id=9]
atlas = ExtResource( 1 )
region = Rect2( 64, 16, 16, 16 )

[sub_resource type="AtlasTexture" id=10]
atlas = ExtResource( 1 )
region = Rect2( 80, 16, 16, 16 )

[sub_resource type="AtlasTexture" id=11]
atlas = ExtResource( 1 )
region = Rect2( 0, 32, 16, 16 )

[sub_resource type="AtlasTexture" id=12]
atlas = ExtResource( 1 )
region = Rect2( 16, 32, 16, 16 )

[sub_resource type="AtlasTexture" id=13]
atlas = ExtResource( 1 )
region = Rect2( 32, 32, 16, 16 )

[sub_resource type="AtlasTexture" id=14]
atlas = ExtResource( 1 )
region = Rect2( 48, 32, 16, 16 )

[sub_resource type="AtlasTexture" id=15]
atlas = ExtResource( 1 )
region = Rect2( 64, 32, 16, 16 )

[sub_resource type="AtlasTexture" id=16]
atlas = ExtResource( 1 )
region = Rect2( 80, 32, 16, 16 )

[sub_resource type="AtlasTexture" id=17]
atlas = ExtResource( 1 )
region = Rect2( 0, 48, 16, 16 )

[sub_resource type="AtlasTexture" id=18]
atlas = ExtResource( 1 )
region = Rect2( 32, 48, 16, 16 )

[sub_resource type="AtlasTexture" id=19]
atlas = ExtResource( 1 )
region = Rect2( 64, 48, 16, 16 )

[sub_resource type="AtlasTexture" id=22]
atlas = ExtResource( 1 )
region = Rect2( 16, 64, 16, 16 )

[sub_resource type="AtlasTexture" id=23]
atlas = ExtResource( 1 )
region = Rect2( 32, 64, 16, 16 )

[sub_resource type="AtlasTexture" id=24]
atlas = ExtResource( 1 )
region = Rect2( 48, 64, 16, 16 )

[sub_resource type="AtlasTexture" id=25]
atlas = ExtResource( 1 )
region = Rect2( 64, 64, 16, 16 )

[sub_resource type="AtlasTexture" id=26]
atlas = ExtResource( 1 )
region = Rect2( 80, 64, 16, 16 )

[sub_resource type="SpriteFrames" id=29]
animations = [ {
"frames": [ SubResource( 2 ), SubResource( 4 ), SubResource( 5 ), SubResource( 6 ), SubResource( 7 ), SubResource( 8 ), SubResource( 9 ), SubResource( 10 ), SubResource( 11 ), SubResource( 12 ), SubResource( 13 ), SubResource( 14 ), SubResource( 15 ), SubResource( 16 ), SubResource( 17 ), SubResource( 18 ), SubResource( 19 ), SubResource( 22 ), SubResource( 23 ), SubResource( 24 ), SubResource( 25 ), SubResource( 26 ) ],
"loop": true,
"name": "default",
"speed": 5.0
} ]

[node name="SmallDebris" type="Area2D"]
position = Vector2( 1, 0 )
script = ExtResource( 2 )
isSmall = true

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource( 1 )

[node name="Particles2D" type="Particles2D" parent="."]
modulate = Color( 1, 1, 1, 0.313726 )
amount = 10
local_coords = false
process_material = SubResource( 32 )
texture = ExtResource( 3 )

[node name="Sprite" type="AnimatedSprite" parent="."]
scale = Vector2( 2, 2 )
frames = SubResource( 29 )
