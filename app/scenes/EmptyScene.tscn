[gd_scene load_steps=5 format=2]

[ext_resource path="res://addons/kenney_particle_pack/16/twirl_01_16.png" type="Texture" id=1]

[sub_resource type="Gradient" id=1]
offsets = PoolRealArray( 0, 0.515556 )
colors = PoolColorArray( 1, 1, 1, 0.0745098, 1, 1, 1, 0 )

[sub_resource type="GradientTexture" id=2]
gradient = SubResource( 1 )

[sub_resource type="ParticlesMaterial" id=3]
emission_shape = 1
emission_sphere_radius = 7.05
flag_disable_z = true
direction = Vector3( 0, 0, 0 )
spread = 90.0
gravity = Vector3( 0, 0, 0 )
initial_velocity = 10.0
initial_velocity_random = 1.0
angular_velocity = 10.0
angular_velocity_random = 1.0
orbit_velocity = 0.0
orbit_velocity_random = 0.0
linear_accel = 10.0
linear_accel_random = 1.0
radial_accel = 1.0
radial_accel_random = 1.0
angle = 720.0
angle_random = 1.0
scale = 4.0
scale_random = 1.0
color_ramp = SubResource( 2 )
hue_variation = 1.0
hue_variation_random = 1.0

[node name="EmptyScene" type="Node2D"]

[node name="Particles2D" type="Particles2D" parent="."]
amount = 30
randomness = 1.0
process_material = SubResource( 3 )
texture = ExtResource( 1 )
