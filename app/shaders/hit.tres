[gd_resource type="Shader" format=2]

[resource]
code = "shader_type canvas_item;

uniform bool active = false;
uniform bool gray = false;
uniform vec4 flash_color: hint_color = vec4(1.0, 1.0, 1.0, 1.0);

void fragment() {

	vec4 previous_color = texture(TEXTURE, UV);

	vec4 white_color = flash_color;
	white_color.a = previous_color.a;
	float avg = (previous_color.r + previous_color.g + previous_color.b)/3.0;
	vec4 black_color = vec4(avg/2.0, avg/2.0, avg/2.0, previous_color.a);

	vec4 new_color = previous_color;

	if (active == true)
	{
		new_color = white_color;

		if (gray == true)
		{
			new_color = black_color;
		}
	}


	COLOR = new_color;
}"
