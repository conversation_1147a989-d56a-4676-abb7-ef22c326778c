extends Area2D

var speed = 30
var hit = 200
var maxHit = 200
var explodeCrystalNumber = 250
var isLargeType = true

signal CrystalOver()

var Explosion = preload("res://scenes/Explosion_1.tscn")

func setType(isLarge = false):
	isLargeType = isLarge
	if(!isLarge):
		scale.x = 0.15
		scale.y = 0.15
		hit = 30
		maxHit = 30
		explodeCrystalNumber = 30
		speed = 80
		modulate = Color(1,1,1,0.8)
		currentPosition.x = 3*(Global.getWindowSize().x/4) - (randf()*Global.getWindowSize().x/2)
	else:
		Global.GameScene.displayNotification("Huge Object Ahead!","Alert")
		Global.playTts(SoundManager.tts_huge_object_ahead)
		scale.x = 0.5
		scale.y = 0.5
		hit = 200
		maxHit = 200
		explodeCrystalNumber = 250
		speed = 30
		modulate = Color(1.1,1.1,1.1)
		currentPosition.x = Global.getWindowSize().x / 2

func setVisual(_percentage):
	$HugeCrystalBase.modulate.a = (_percentage)/100.0

func _fon():
	$HugeCrystalBase.material.set_shader_param("active", 1)
	$HugeCrystalEmpty.material.set_shader_param("active", 1)

func _foff():
	$HugeCrystalBase.material.set_shader_param("active", 0)
	$HugeCrystalEmpty.material.set_shader_param("active", 0)

func flash():
	Global.playSound(SoundManager.HitSound, global_position, -10)
	setVisual(Global.getPercentage(hit,maxHit))
	_fon()
	Global.setTimeout(self,0.1,self,"_foff")

var isDead = false

var damageFreq = 0

func takeDamage():

	if(Global.doThrottle("LargeCrystalHit",15)):
		return false

	hit-=1
	hit = max(0, hit)

	if(hit>0):
		var _tpos = global_position-Vector2(50,100)+Vector2(randi()%100,randi()%200)

		if(!isLargeType):
			_tpos = global_position-Vector2(20,50)+Vector2(randi()%40,randi()%100)
		var crNum = Global.ifelse(isLargeType, 3, 2)
		var crList = [Global.CrystalType.c5, Global.CrystalType.c10]

		if(!isLargeType):
			crList = [Global.CrystalType.c5, Global.CrystalType.c10, Global.CrystalType.c20, Global.CrystalType.c50]

		Global.GameScene.spawnManyCrystals(crNum,_tpos,false,true,crList)

		flash()

		return true
	else:
		startExplosions()

func _removeFromScene():
	Global.GameScene.largeCrystalCount-=1
	call_deferred("queue_free")
	
func finalExplode():

	if(isLargeType):
		Achievments.acquire("destroyed_diamond")

	emit_signal("CrystalOver")
	_removeFromScene()

func bodyExplosion():
	var explosion = Explosion.instance()

	if(isLargeType):
		explosion.position = global_position-Vector2(80,150)+Vector2(randi()%160,randi()%300)
	else:
		explosion.position = global_position-Vector2(40,80)+Vector2(randi()%80,randi()%160)

	explosion.explosionColorMod = Color(0.5,0.5,1,1)
	explosion.z_index  = z_index+1;
	explosion.scale = Vector2(1+randf(),1+randf())

	explosion.volume = Global.ifelse(isLargeType,5,0)

	if(isLargeType):
		Global.GameScene.FlashWorldEnv(0.3, 10)
	else:
		Global.GameScene.FlashWorldEnv()

	Global.GameScene.add_child(explosion);

func startExplosions():
	if(isDead):
		return false
	isDead = true

	var timeSum = 0
	var timebase = 0.1

	var eCount = Global.ifelse(isLargeType, 20, 3)

	for _i in range(eCount):
		var _t = timebase+(randf()/10)
		Global.setTimeout(self, timeSum+_t, self,"bodyExplosion")
		timeSum+=_t

	if(isLargeType):
		Global.GameScene.spawnBonusCrystals(explodeCrystalNumber,true)
		Global.GameScene.displayNotification("Crystal Shower!","Bonus")

	Global.setTimeout(self, timeSum, self,"finalExplode")

func flashEnv():
	if(!Global.doThrottle("LargeCrystalFlash",500)):
		Global.GameScene.FlashWorldEnv()

func _on_hit(area):

	# prevent crystal damage off screen
	if(Global.isOffScreen(global_position,Global.ifelse(isLargeType,200,50))):
		return false

	if(area.has_method("getDamagePoint")):
		flashEnv()
		Global.callIfExists(area,"destroy")
		takeDamage()

var positionOffset = Vector2(0,0)
var currentPosition = Vector2(0,0)

func init(isLarge = false):

	var _tmp = connect("area_entered",self,"_on_hit")
	Global.GameScene.largeCrystalCount+=1
	z_index  = Config.TopZIndex-15
	setType(isLarge)
	currentPosition.y = -200
	global_position = currentPosition

func _ready():
	pass

var maxShakeValue = 5.0

func actualPos(delta):
	var mul = ((100-Global.getPercentage(hit,maxHit))*maxShakeValue)/100.0
	var actShakeValue = maxShakeValue*mul*delta
	rotation_degrees = -actShakeValue+2*randf()*actShakeValue
	position.y = currentPosition.y-actShakeValue+2*randf()*actShakeValue
	position.x = currentPosition.x-actShakeValue+2*randf()*actShakeValue

func checkIfDone():
	if(Global.isOffScreenBottom(global_position,200)):

		if(hit==maxHit && isLargeType):
			Achievments.acquire("spared_diamond")

		emit_signal("CrystalOver")
		_removeFromScene()

func _process(delta):
	currentPosition.y += speed*delta
	actualPos(delta)
	checkIfDone()

