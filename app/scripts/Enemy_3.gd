extends "res://scripts/EnemyBase.gd"

# enemy health
func _ready():
	Bullet = preload("res://scenes/EnemyBullet_2.tscn")
	health = 60
	ExplosionColor = Color(0.2,1,1)
	pointValue = 3000
	species = "YellowMonoEye"

func selfCoordinatedBullet():

	if Global.doThrottle(getId("",".BulletSelfFire"), Config.GlobalEnemyBulletThrottle):
		return false

	if Global.isChance(randi(), 30-max(10, Global.GameScene.getProperDifficulty())):
		fireBullet()

func _process(_delta):
	if Global.GameScene.isPlayerReady():
		selfCoordinatedBullet()
