extends Node

const prefix = "user://tryu_"
const suffix = ".storage"
const salt = "0%%k3MRl*#4U8I.-{XZi?L[i=MmBQ/Ko"

func generateHash(json_str):
	return ("["+json_str+"]["+salt+"]").sha256_text();

func addHash(json):
	return json+generateHash(json)

func readJustHash(hashed_json):
	return hashed_json.substr(hashed_json.length()-64)

func readJustJson(hashed_json):
	return hashed_json.substr(0, hashed_json.length()-64)

func SaveData(filename, object):
	var file = File.new()
	file.open(prefix+filename+suffix, File.WRITE)
	var json_text = to_json(object)

	file.store_line(addHash(json_text))

	file.close()

var aes = AESContext.new()

func validateHash(hashed_json):

	if(!Config.DoProtectStorageFiles):
		return true

	var jsond = readJustJson(hashed_json)
	var hashd = readJustHash(hashed_json)
	var expected_hash = generateHash(jsond)

	return expected_hash == hashd


func LoadData(filename, default_object = {}, do_save_after_load = false):
	var file = File.new()

	if not file.file_exists(prefix+filename+suffix):
		file.close()
		if(do_save_after_load):
			SaveData(filename, default_object)
		return default_object
	
	file.open(prefix+filename+suffix, File.READ)

	if file.get_position()>=file.get_len():
		file.close()
		if(do_save_after_load):
			SaveData(filename, default_object)
		return default_object

	var content = file.get_line()

	content = content

	# invalid hash
	if(!validateHash(content)):
		SaveData(filename, default_object)
		return default_object
	
	# extract json only
	content = readJustJson(content)

	# returns empty string if valid - stupid , but here we are
	if validate_json(content):
		file.close()
		if(do_save_after_load):
			SaveData(filename, default_object)
		return default_object
	
	
	var object = parse_json(content)

	object.merge(default_object)

	file.close()

	if(do_save_after_load):
		SaveData(filename, object)

	return object
