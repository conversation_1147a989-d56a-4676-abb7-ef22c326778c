# Test script to validate all BulletPool types functionality
# This script tests the pooling system for all bullet types

extends Node

# Test all bullet pool types
func test_all_bullet_pools():
	print("=== All BulletPool Types Test Started ===")
	
	# Test regular bullet pool
	print("\n--- Testing Regular Bullet Pool ---")
	test_bullet_pool_type("Bullet", BulletPool.create_bullet_pool(self))
	
	# Test super bullet pool
	print("\n--- Testing Super Bullet Pool ---")
	test_bullet_pool_type("BulletSuper", BulletPool.create_bullet_super_pool(self))
	
	# Test homing bullet pool
	print("\n--- Testing Homing Bullet Pool ---")
	test_bullet_pool_type("BulletHoming", BulletPool.create_bullet_homing_pool(self))
	
	# Test laser bullet pool
	print("\n--- Testing Laser Bullet Pool ---")
	test_bullet_pool_type("BulletLaser", BulletPool.create_bullet_laser_pool(self))
	
	print("\n=== All BulletPool Types Test Completed ===")

func test_bullet_pool_type(type_name, pool):
	print("Testing ", type_name, " pool...")
	print("Initial stats: ", pool.get_stats())
	
	# Test getting bullets from pool
	var bullets = []
	for i in range(5):
		var bullet = pool.get_bullet()
		if bullet:
			bullets.append(bullet)
			print("Got ", type_name, " ", i, " - Pool stats: ", pool.get_stats())
		else:
			print("Failed to get ", type_name, " ", i)
	
	print("Retrieved 5 ", type_name, " bullets")
	print("Current stats: ", pool.get_stats())
	
	# Test returning bullets to pool
	for i in range(3):
		if i < bullets.size():
			pool.return_bullet(bullets[i])
			print("Returned ", type_name, " ", i, " - Pool stats: ", pool.get_stats())
	
	print("Returned 3 ", type_name, " bullets")
	print("Final stats: ", pool.get_stats())
	
	# Test getting bullets again (should reuse returned ones)
	for i in range(2):
		var bullet = pool.get_bullet()
		if bullet:
			print("Reused ", type_name, " ", i, " - Pool stats: ", pool.get_stats())

func _ready():
	# Run the test
	call_deferred("test_all_bullet_pools")
