extends "res://scripts/EnemyBase.gd"

func _ready():
	health = 30
	pointValue = 2500
	ExplosionColor = Color(1,0.2,0.2)
	species = "SpicyRoid"

func fireBullet(doForce = false):

	if(!doForce):
		if (Global.doThrottle(getId("",".Bullet") ,Config.GlobalEnemyBulletThrottle) || !Global.isEnemyActive(mode)):
			return false

	# don't shoot if too close to bottom
	if Global.isOffScreenBottom(getCenterPosition(), -200):
		return false

	var bullet = Bullet.instance()
	bullet.position = getCenterPosition();
	bullet.z_index  = z_index-1;
	bullet.velocity.x=-0.1

	var bullet2 = Bullet.instance()
	bullet2.position = getCenterPosition();
	bullet2.z_index  = z_index-1;
	bullet2.velocity.x=0.1

	Global.GameScene.add_child(bullet2);
	Global.GameScene.add_child(bullet);

	Global.EnemyBulletsOnScreen += 1
