extends "res://scripts/EnemyBase.gd"

func _ready():
	Bullet = preload("res://scenes/EnemyBullet_4.tscn")
	health = 60
	ExplosionColor = Color(0.2,0.2,1)
	species = "Alien Wings"
	pointValue = 6000
	setScratch(100.0)

var _cnt = 0

func throttleDamage():
	return Global.doThrottle("Enemy7WingThrottle" + str(get_instance_id()), Config.BossDamageThrottleShort)

func getWingHealth():
	var h = 40
	if(has_node("Wing_left")):
		h = $Wing_left.health
	return h

func changeWingHealth(health):

	if(has_node("Wing_left")):
		$Wing_left.set("health",health)

	if(has_node("Wing_right")):
		$Wing_right.set("health",health)


func beforeDie():

	if((!has_node("Wing_left") and !has_node("Wing_right"))):
		Global.GameScene.spawnBonusCrystals(5,false,getCenterPosition(),true)

	for _i in range(0,10):
		var pos = getCenterPosition()+Vector2(randi()%50-50, randi()%20-20);
		Global.GameScene.spawnExplosion(pos,0.1*_i,Color(randf(),randf(),randf(),1))

	if(has_node("Wing_left")):
		for _i in range(0,10):
			var pos = getCenterPosition()+Vector2(-randi()%100, randi()%20-20);
			Global.GameScene.spawnExplosion(pos,0.1*_i,Color(randf(),randf(),randf(),1))

	if(has_node("Wing_right")):
		for _i in range(0,10):
			var pos = getCenterPosition()+Vector2(randi()%100, randi()%20-20);
			Global.GameScene.spawnExplosion(pos,0.1*_i,Color(randf(),randf(),randf(),1))

	return true

func setScratch(_percentage):
	if is_instance_valid($"Scratch"):
		$"Scratch".modulate.a  =(100-_percentage)/100.0

# overwrite damage function and if the enemy has wings cause 1/4 damage
func causeDamage(points, doSpawnCrystal = false):

	var divider = 1

	if(has_node("Wing_left")):
		divider *= 2

	if(has_node("Wing_right")):
		divider *= 2

	self.health-=points/divider

	var _percentage = Global.getPercentage(health,initialHealth);
	self.setScratch(_percentage)

	checkHealth(doSpawnCrystal)

func addBullet():
	var bullet = Bullet.instance()
	var _xskw = -0.2+(_cnt*0.1)
	_cnt+=1
	bullet.position = getCenterPosition();
	bullet.z_index  = z_index-1;
	bullet.velocity.x= _xskw
	bullet.randomJiggle = false;
	bullet.doJiggle = false;

	Global.GameScene.add_child_deferred(bullet);

func fireBullet(doForce = false):

	if(!doForce):
		if (Global.doThrottle(getId("",".Bullet"), Config.GlobalEnemyBulletThrottle) || !Global.isEnemyActive(mode)):
			return false

	# don't shoot if too close to bottom
	if Global.isOffScreenBottom(getCenterPosition(), -200):
		return false

	_cnt=0;
	for i in range(1, 6):
		Global.setTimeout(self, i*0.2, self, "addBullet")
	
	# fire bullet from wings
	if(has_node("Wing_left")):
		$Wing_left.call_deferred("fireBullet",true);

	if(has_node("Wing_right")):
		$Wing_right.call_deferred("fireBullet",true);

	Global.EnemyBulletsOnScreen += 1
