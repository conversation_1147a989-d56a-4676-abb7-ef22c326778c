extends Control


func _ready():

	var _ch = $CloseButton.connect("pressed", self, "_on_CloseButton_pressed")
	var listControl = $TabContainer/Powerups/ItemList
	var items = Global.powerups
	var id = 0

	listControl.clear()
	listControl.add_item( "Char" , null, false)
	listControl.add_item( "Description" , null, false)
	listControl.add_item( "Type" , null, false)

	listControl.add_item( "-------" , null, false)
	listControl.add_item( "-------" , null, false)
	listControl.add_item( "-------" , null, false)

	for i in items:

		var item = items[i]

		listControl.add_item( "[ "+item.char+" ]" , null, false)
		listControl.set_item_disabled(id , false)
		id+=1


		listControl.add_item( item.text, null, false)
		listControl.set_item_disabled(id, false)
		id+=1

		listControl.add_item( item.type, null, false)
		listControl.set_item_disabled(id, false)
		id+=1
	
	$CloseButton.grab_focus()

func _process(_delta):

	if Input.is_action_just_pressed("button_left"):
		# go to previous tab
		$TabContainer.current_tab = ($TabContainer.current_tab - 1) % $TabContainer.get_tab_count()

	if Input.is_action_just_pressed("button_right"):
		$TabContainer.current_tab = ($TabContainer.current_tab + 1) % $TabContainer.get_tab_count()
	
	var _pd = Input.is_action_just_pressed("button_down")
	var _pu = Input.is_action_just_pressed("button_up")

	if _pd or _pu:
		var scrollBar = $TabContainer/Powerups/ItemList.get_v_scroll()  # Ensure correct path
		scrollBar.value+=100 if _pd else -100

func _input(event):
	if event.is_action_pressed("button_back"):
		_on_CloseButton_pressed()

func _on_CloseButton_pressed():
	queue_free()
