# Test script to validate the optimized crystal system
# Compares behavior between RigidBody2D and Node2D implementations

extends Node

func _ready():
	print("=== Crystal Optimization Test ===")
	
	# Test if we can create the optimized crystal
	var crystal_scene = preload("res://scenes/CrystalRigid.tscn")
	var crystal = crystal_scene.instance()
	
	print("Crystal created successfully: ", crystal != null)
	
	if crystal:
		print("Crystal type: ", crystal.get_class())
		print("Crystal script: ", crystal.get_script())
		
		# Test basic properties
		print("Has velocity property: ", "velocity" in crystal)
		print("Has gravity property: ", "gravity" in crystal)
		print("Has init method: ", crystal.has_method("init"))
		print("Has apply method: ", crystal.has_method("apply"))
		print("Has doPushToPlayer method: ", crystal.has_method("doPushToPlayer"))
		
		# Test Area2D collision detection
		var area2d = crystal.get_node("Area2D")
		print("Has Area2D: ", area2d != null)
		if area2d:
			print("Area2D collision layers: ", area2d.collision_layer)
			print("Area2D collision mask: ", area2d.collision_mask)
		
		# Test AnimatedSprite
		var sprite = crystal.get_node("AnimatedSprite")
		print("Has AnimatedSprite: ", sprite != null)
		if sprite:
			print("Sprite frames: ", sprite.frames != null)
			print("Sprite playing: ", sprite.playing)
		
		# Test AnimationPlayer
		var anim_player = crystal.get_node("AnimationPlayer")
		print("Has AnimationPlayer: ", anim_player != null)
		if anim_player:
			print("Animation autoplay: ", anim_player.autoplay)
		
		# Clean up
		crystal.queue_free()
	
	print("=== Crystal Test Complete ===")

# Performance comparison function
func compare_performance():
	print("=== Performance Comparison ===")
	
	var start_time = OS.get_ticks_msec()
	
	# Create multiple crystals to test performance
	var crystals = []
	for i in range(100):
		var crystal_scene = preload("res://scenes/CrystalRigid.tscn")
		var crystal = crystal_scene.instance()
		add_child(crystal)
		crystal.init(Global.CrystalType.c5)
		crystals.append(crystal)
	
	var creation_time = OS.get_ticks_msec() - start_time
	print("Time to create 100 crystals: ", creation_time, "ms")
	
	# Simulate some physics frames
	start_time = OS.get_ticks_msec()
	for i in range(60):  # Simulate 1 second at 60fps
		for crystal in crystals:
			if is_instance_valid(crystal):
				crystal._physics_process(1.0/60.0)
	
	var physics_time = OS.get_ticks_msec() - start_time
	print("Time for 60 physics frames: ", physics_time, "ms")
	
	# Clean up
	for crystal in crystals:
		if is_instance_valid(crystal):
			crystal.queue_free()
	
	print("=== Performance Test Complete ===")

# Test magnet effect specifically
func test_magnet_effect():
	print("=== Magnet Effect Test ===")

	# Create a crystal
	var crystal_scene = preload("res://scenes/CrystalRigid.tscn")
	var crystal = crystal_scene.instance()
	add_child(crystal)
	crystal.init(Global.CrystalType.c50)
	crystal.position = Vector2(400, 300)  # Position it away from player

	# Force enable magnet effect for testing
	crystal.cached_has_magnet_effect = true
	crystal.cached_permanent_crystal_magnet = true

	print("Crystal created at: ", crystal.position)
	print("Magnet effect enabled: ", crystal.doPushToPlayer())
	print("Magnet power: ", crystal.magnet_power)
	print("Magnet range: ", crystal.magnet_range)

	# Test for a few seconds
	var test_timer = Timer.new()
	test_timer.wait_time = 3.0
	test_timer.one_shot = true
	test_timer.connect("timeout", self, "_cleanup_magnet_test", [crystal])
	add_child(test_timer)
	test_timer.start()

	print("Watch the crystal - it should move toward the player and glow brighter!")
	print("=== Magnet Test Running for 3 seconds ===")

func _cleanup_magnet_test(crystal):
	if is_instance_valid(crystal):
		print("Final crystal position: ", crystal.position)
		crystal.queue_free()
	print("=== Magnet Test Complete ===")

# Call this to run performance test
func _input(event):
	if event is InputEventKey and event.pressed:
		if event.scancode == KEY_P:
			compare_performance()
		elif event.scancode == KEY_M:
			test_magnet_effect()
