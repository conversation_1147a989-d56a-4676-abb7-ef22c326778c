extends Node

func getDefaultItem():
	return {
		"key": "",
		"name": "",
		"description": "",
		"private": false,
		"acquired": false
	}

func init_item(_key, _name, _description = "", _private = false, _acquired = false):
	var new_achi = getDefaultItem()
	new_achi.key = _key
	new_achi.name = _name
	new_achi.description = _description
	new_achi.private = _private
	new_achi.acquired = _acquired
	return new_achi

var achievements = {}

func getItems():
	var res = []
	var res2 = []
	for i in achievements:
		res.append(achievements[i])
	
	# sort by acquired

	for i in res:
		if i.acquired == true:
			res2.append(i)
	
	for i in res:
		if i.acquired == false:
			res2.append(i)

	return res2

# add achievment to the list
func _add(_key, _name, _description = "", _private = false, _acquired = false):
	var new_achi = init_item(_key, _name, _description, _private, _acquired)
	achievements[_key] = new_achi

# acquire achievment
func acquire(key, _doForce = false):

	if(Config.Env.IsDemo):
		return

	# if was cheat, don't add achievment
	if Global.getCheatState() && !_doForce:
		Global.dbg("Cheat mode, no achievment")
		return

	# if user have not acquired this achievment yet
	if (achievements.has(key)) && (achievements[key].acquired == false):
		achievements[key].acquired = true
		var aname = achievements[key].name
		Global.GameScene.displayNotification(aname, "Achievment")

		if(key!="first_game"):
			Global.playTts(SoundManager.tts_wow)

		self.save()
		# @todo call game object to show achievment - or call api ( steam etc )

	if StoreIntegration.isStoreType("steam"):
		StoreIntegration.setAchievement(key)

func resetAll():
	for i in achievements:
		achievements[i]["acquired"] = false
		StoreIntegration.clearAchievement(i)
	
var wasInit = false

# initialize achievments
func initializeAchievments(doLoad = true):

	# init only once

	if wasInit:
		return true
	
	wasInit = true

	achievements = {}

	# add all achievments here

	self._add("first_game", "First Game", "Playing the game for the first time")
	self._add("first_close_kill", "First Close Kill", "First Enemy killed from close range")
	self._add("passive_agressive", "Passive agressive", "Don't do anything until they kill you", true)
	self._add("dragon_tickler", "Tickling the dragon's tail", "Get every Gem from the Dragon's tail", true)
	self._add("party_pooper", "Party Pooper", "Destroy the disco ball", true)
	self._add("hadouken_clash", "Hadouken Clash", "Hit enemy bullet and get a diamond", true)
	self._add("curse_burst", "Curse to Burst", "Collect cursed modules", true)
	self._add("extra_life", "Another Chance", "Get an extra life")
	self._add("unlucky_bastard", "Unlucky Bastard", "Die while you have extra luck", true)
	self._add("mined", "Mined!", "Killed by a mine", true)
	self._add("no_kill_death", "Die and Let Live", "Die without killing anything", true)
	self._add("yolo", "YOLO", "10+ close encounters", true)
	self._add("dangerous_dance", "Dangerous Dance", "Make 10+ close kills", true)
	self._add("high_accuracy", "Bullseye", "High hit rate (95%)", false)
	self._add("perfect_accuracy", "Space Sniper", "Perfect hit rate (99%)", false)

	self._add("finished_easy", "Cadet Cruise", "Finish game on easy", false)
	self._add("finished_normal", "Astronaut's Challenge", "Finish game on normal", false)
	self._add("finished_hard", "Cosmic Warrior's Trial", "Finish game on hard", false)
	self._add("finished_extreme", "Galaxy Legend's Gauntlet", "Finish game on extreme", false)

	self._add("finished_2x", "Deja Vu Voyager", "Finish all levels 2x", false)
	self._add("finished_5x", "Repeat Odyssey", "Finish all levels 5x", false)
	self._add("finished_10x", "Time Loop Pilot", "Finish all levels 10x", false)
	self._add("finished_10x_extreme", "Legend of the Galaxy", "Finish levels 10x on extreme", false)

	self._add("destroyed_diamond", "Greed's Fall", "Destroy the large diamond", true)
	self._add("spared_diamond", "Pacifist's Path", "Leave the diamond alone", true)

	self._add("spared_powerup_ship", "Merciful Guardian", "Do not shoot the powerup ship at all", true)
	self._add("destroyed_powerup_ship", "Rebel Attack", "Destroy the powerup ship", true)

	self._add("multiple_cursed_combos", "Curse Collector", "Discover more than one cursed module combination in a single game", false)
	self._add("cursed_trio_fail", "Unlucky Alchemist", "Collect 3 cursed modules without unlocking secrets", true)
	self._add("chain_reaction", "Chain Reaction", "Detonate multiple mines", true)
	self._add("fleet_master", "Fleet Master", "All ships unlocked", true)


	# @todo - read statuses from file for the current user

	# how to iterate
	# Global.dbg(achievements)
	# for i in achievements:
	# 	Global.dbg(achievements[i].name)

	if doLoad:
		self.load()

func _ready():
	self.initializeAchievments();

const KEY_ACHI_STORAGE = "achievments"

func save():
	Storage.SaveData(KEY_ACHI_STORAGE, achievements)

func load():
	var achi = Storage.LoadData(KEY_ACHI_STORAGE, self.achievements, true);
	# set achi one by one, so if we add new we still have it
	for i in achi:
		achievements[i] = achi[i]
	
func updateStatuses(statusArray):
	for i in statusArray:
		achievements[i]["acquired"] = statusArray[i]
