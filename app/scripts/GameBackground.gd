extends Node2D

# Variables for speed and the sprite node
export (int) var scroll_speed = 30

var Sprite2

func setX():
	var diff = $Sprite1.texture.get_width()-Global.getWindowSize().x
	$Sprite1.position.x = int(Global.getWindowSize().x/2)+int(diff/2)-(randi()%int(diff/2))
	Sprite2.position.x = $Sprite1.position.x

func setY():
	$Sprite1.position.y = 0
	Sprite2.position.y = -$Sprite1.texture.get_height()

func resetPos():
	$Sprite1.position.y = 0
	setY()
	setX()

func _ready():
	Sprite2 = $Sprite1.duplicate()
	add_child(Sprite2)
	resetPos()

var newIndex = null

func changeFadeIn():

	var _target = get_node("Sprite_"+str(newIndex))

	#change img
	$Sprite1.texture = _target.texture
	Sprite2.texture = _target.texture

	$Sprite1.modulate = _target.modulate
	Sprite2.modulate = _target.modulate

	resetPos()

	var tween = Global.createTween($Sprite1)
	tween.interpolate_property(self,"modulate:a",0,1,2, Tween.TRANS_LINEAR, Tween.EASE_IN)

	var tween2= Global.createTween(Sprite2)
	tween2.interpolate_property(self,"modulate:a",0,1,2, Tween.TRANS_LINEAR, Tween.EASE_IN)

	tween.start()
	tween2.start()


func changeImage(imgIndex):

	newIndex = imgIndex

	# fade out
	var tween = Global.createTween($Sprite1)
	tween.interpolate_property(self,"modulate:a",1,0,2, Tween.TRANS_LINEAR, Tween.EASE_IN)
	tween.connect("tween_all_completed",self,"changeFadeIn")

	var tween2= Global.createTween(Sprite2)
	tween2.interpolate_property(self,"modulate:a",1,0,2, Tween.TRANS_LINEAR, Tween.EASE_IN)

	tween.start()
	tween2.start()

func _process(delta):
	# Move the background down
	$Sprite1.position.y += scroll_speed * delta
	Sprite2.position.y += scroll_speed * delta

	# Check if the background has moved beyond the screen and reset position
	if $Sprite1.position.y >= $Sprite1.texture.get_height():
		setY()


