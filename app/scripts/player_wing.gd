extends Area2D

var Bullet = preload("res://scenes/Bullet.tscn")
var BulletSuper = preload("res://scenes/BulletSuper.tscn")
var BulletHoming = preload("res://scenes/BulletHoming.tscn")
var Explosion = preload("res://scenes/Explosion_1.tscn")

signal wing_hit(wingObject)

func _ready():
	# hitbox
	var _c = connect("area_entered",self,"_on_hit")

	# subscribe to player bullet fired
	Global.GameScene.connect("bullet_fired",self,"_on_bullet_fired")

	# todo: subscribe to player death, and explode all
	pass

func _on_hit(target):

	if(!isActive):
		return false

	if(target.has_method("isDeadly")):

		if(!target.isDeadly()):
			return false

		# kill bullet
		if(target.has_method("destroy")):
			target.destroy()

		emit_signal("wing_hit", self)

		Global.getPlayer().destroyWings(self.wingPosition)

func _on_bullet_fired(_bType):

	#throttle rules for homing bullet - balancing
	if(bulletType==Global.PlayerBulletTypes.HOMING_SINGLE):

		if(_bType==Global.PlayerBulletTypes.LASER_SINGLE):
			if(Global.doThrottle("throttle_wing_bullet"+str(wingPosition)+"_"+str(wingType), Config.LaserWingBulletThrottle)):
				return false
		else:
			if(Global.doThrottle("throttle_wing_bullet"+str(wingPosition)+"_"+str(wingType), Config.DefaultWingBulletThrottle)):
				return false

	var bullet = null

	match bulletType:
		Global.PlayerBulletTypes.STRONG_SINGLE:
			bullet = Global.GameScene.bullet_super_pool.get_bullet()
		Global.PlayerBulletTypes.HOMING_SINGLE:
			bullet = Global.GameScene.bullet_homing_pool.get_bullet()
		_:
			bullet = Global.GameScene.bullet_pool.get_bullet()

	bullet.canClash = false
	bullet.position = self.global_position - Vector2(0, BULLET_OFFSET)

	bullet.doCountTowardBulletsOnScreen = false

	Global.GameScene.add_child(bullet);

	pass

var isActive = false
var isInPlace = false

func activate():
	# tween wings from the bottom of the screen to place - fast
	# x following activated
	# only make it voulnerabe after it's in place
	var tp = self.getTargetPosition()
	var stPos = Vector2(0,0)

	if(wingType == Global.PlayerWingType.LEFT):
		stPos.x = tp.x - X_ANIM_OFFSET
		stPos.y = tp.y + Y_ANIM_OFFSET
	else:
		stPos.x = tp.x + X_ANIM_OFFSET
		stPos.y = tp.y + Y_ANIM_OFFSET

	global_position = stPos
	moveToPlace(tp, stPos)

	isActive = true

# can be 0,1,2,3...etc
var wingPosition = 0

func removeFromScene():
	self.call_deferred("queue_free")

func destroy():

	var explosion = Explosion.instance()
	explosion.position = position;
	explosion.z_index  = z_index+1;
	explosion.scale = Vector2(1,1)
	Global.GameScene.add_child(explosion);

	var tween = Global.createTween(self)
	tween.interpolate_property(self,"scale",Vector2(1,1),Vector2(0,0),0.1, Tween.TRANS_EXPO, Tween.EASE_OUT)
	tween.interpolate_property(self,"modulate:a",1,0,0.1, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.connect("tween_all_completed",self,"removeFromScene")
	tween.start()

	Global.GameScene.levelConductor.levelInstance.hideAllStuffOnLevel();

func setWingPosition(pos):
	wingPosition = pos

var wingType = Global.PlayerWingType.LEFT

var bulletType = Global.PlayerBulletTypes.SINGLE

func setBulletType(bType):
	bulletType = bType
	match bType:
		Global.PlayerBulletTypes.STRONG_SINGLE:
			$WingSprite.animation = "green"
		Global.PlayerBulletTypes.HOMING_SINGLE:
			$WingSprite.animation = "red"
		_:
			$WingSprite.animation = "blue"
	setType(self.wingType)

func setType(wType):
	wingType = wType
	if(wingType==Global.PlayerWingType.LEFT):
		$WingSprite.frame = 0
	else:
		$WingSprite.frame = 1

const WING_DISTANCE = 28
const PLAYER_DISTANCE = 40
const Y_OFFSET_INIT = 5
const Y_OFFSET = 2
const BULLET_OFFSET = 23
const Y_ANIM_DURATION = 0.3
const X_ANIM_OFFSET = 20
const Y_ANIM_OFFSET = 50

func entryDone():
	Global.playSound(SoundManager.Clunk, Global.getPlayerPosition(), -5, 0.7+(randf()*0.6));
	isInPlace = true;

func moveToPlace(finalPos, stPos):

	var tweenY = Global.createTween(self)
	tweenY.interpolate_property(self,"position:y",stPos.y,finalPos.y,Y_ANIM_DURATION+(wingPosition*0.15), Tween.TRANS_QUAD, Tween.EASE_OUT)

	tweenY.connect("tween_all_completed",self,"entryDone")

	tweenY.start()

func getTargetPosition(offset = Vector2(0,0)):

	var playerPos = Global.getPlayer().global_position
	var targetPosition = Vector2(0,playerPos.y+Y_OFFSET_INIT+Y_OFFSET*wingPosition+offset.y) 

	if(wingType==Global.PlayerWingType.LEFT):
		targetPosition.x = playerPos.x - PLAYER_DISTANCE - (wingPosition*WING_DISTANCE) - offset.x
	else:
		targetPosition.x = playerPos.x + PLAYER_DISTANCE + (wingPosition*WING_DISTANCE) + offset.x

	return targetPosition

func _process(_delta):

	if(!isActive):
		return false

	# follow player around
	if(self.isInPlace):
		self.global_position = getTargetPosition()
	else:
		self.global_position.x = getTargetPosition(Vector2( X_ANIM_OFFSET,0 )).x

