extends "res://scripts/EnemyBulletBase.gd"

var target = Config.ShipStartPos

func _ready():

	if $AudioStreamPlayer2D != null:
		$AudioStreamPlayer2D.autoplay = false
		if(Global.OptionsData.isSoundOn):
			$AudioStreamPlayer2D.play()

	velocity = Vector2(0,0)
	target = Global.getPlayerPosition()
	look_at(target)
	rotate(PI/2)
	velocity =  position.direction_to(target) * (Config.EnemyBulletSpeed*(0.6+(0.1*Global.GameScene.getProperDifficulty())))

func moveBullet(delta):

	# move bullet
	position += velocity * delta

	# destroy bullet if it goes off screen
	if Global.isOffScreenBottom(position, 100):
		destroy()

