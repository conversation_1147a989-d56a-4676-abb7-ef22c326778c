extends "res://scripts/EnemyBulletBase.gd"

var target = Config.ShipStartPos

func _ready():
	velocity = Vector2(0,0)
	target = Global.getPlayerPosition()
	look_at(target)
	rotate(PI/2)
	velocity =  position.direction_to(target) * getBulletSpeed()

func moveBullet(delta):

	# move bullet
	position += velocity * delta

	# destroy bullet if it goes off screen
	if Global.isOffScreenBottom(position, 100):
		destroy()
