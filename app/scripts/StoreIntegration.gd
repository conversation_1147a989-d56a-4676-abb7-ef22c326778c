extends Node

onready var Steam = preload("res://addons/godotsteam/godotsteam.gdns").new()

signal overlay_toggled(object)

var steam_is_free_weekend: bool = false
var steam_is_owned: bool = false
var steam_is_online: bool = false
var steam_is_on_steam_deck: bool = false
var steam_id: int = 0
var steam_username: String = ""

func _ready():
	self.initStore()

func openStore():
	if isStoreType("steam"):
		OS.shell_open(Config.Env.SteamUrl)

func _process(_delta):
	Steam.run_callbacks()

var chs = 0
var fhs = 0
var cry = 0
var shu = 0

# func _on_steam_stats_ready(_game: int, _result: int, _user: int) -> void:
# 	loadStatsData()

func isStoreType(sType):
	return Config.Env.StoreType == sType

var initialAchievementStatus = {}

func initStore():

	# STEAM

	if isStoreType("steam"):

		OS.set_environment("SteamAppId", Config.Env.SteamAppId)
		OS.set_environment("SteamGameId", Config.Env.SteamAppId)

		# Steam.connect("user_stats_received", self, "_on_steam_stats_ready")
		Steam.connect("overlay_toggled",self,"_on_overlay_toggled")

		var initialize_response: Dictionary = Steam.steamInitEx(true)
		Global.dbg("Did Steam initialize?: %s " % initialize_response)

		if initialize_response['status'] > 0:
			Global.dbg("Failed to initialize Steam, shutting down: %s" % initialize_response)
			get_tree().quit()

		# Steam.requestCurrentStats()

		#Is the user online?
		steam_is_online = Steam.loggedOn()

		# Get the user's Stean name and ID
		steam_id = Steam.getSteamID()
		steam_username = Steam.getPersonaName()

		# Is this app owned or is it a free weekend?
		steam_is_owned = Steam.isSubscribed()
		steam_is_free_weekend = Steam.isSubscribedFromFreeWeekend()

		# Is the game running on the Steam Deck
		steam_is_on_steam_deck = Steam.isSteamRunningOnSteamDeck()

		var achi: Dictionary = Steam.getAchievement("first_game")
		Global.dbg("achi %s", achi)

		# var num_achievements = Steam.getNumAchievements()

		# Global.dbg("achnum")
		# Global.dbg(num_achievements)

		# if achi['achieved']:
		# Steam.clearAchievement("first_game")

		# Steam.setAchievement("first_game")
		# Steam.storeStats()

		# fetch achievement status from steam
		getAllAchievementData()

		Achievments.updateStatuses(initialAchievementStatus)

		self.initLeaderboard()

		# debug
		# self.saveHighScore(102,"campaign")
		# self.saveHighScore(102,"test", "test")

		return true
	
	# android

	# default
	
	return false

func _on_overlay_toggled(active: bool, _user_initiated: bool, _app_id: int):
	emit_signal("overlay_toggled", active)


func clearAchievement(key):
	if isStoreType("steam"):
		Steam.clearAchievement(key)

func setAchievement(key):
	if isStoreType("steam"):
		Steam.setAchievement(key)
		Steam.storeStats()


func getAllAchievementData():
	if isStoreType("steam"):
		Achievments.initializeAchievments(false)
		for item in Achievments.getItems():
			var steamItem = Steam.getAchievement(item.key)
			initialAchievementStatus[item.key] = steamItem.achieved
			Global.dbg(steamItem.achieved)

		Global.dbg("all achi data")
		Global.dbg(initialAchievementStatus)
		return true

func saveStats(statsData):
	if isStoreType("steam"):

		Global.dbg(statsData)

		Steam.setStatInt("campaign_high_score", int(statsData["highScoreData"]["campaign"]["score"]))
		Steam.setStatInt("flow_high_score", int(statsData["highScoreData"]["flow"]["score"]))
		Steam.setStatInt("crystals", int(statsData["crystals"]))
		Steam.setStatInt("unlocked_ships", int(statsData["available_ships"]))

		Steam.storeStats()

func loadStatsData():
	if isStoreType("steam"):
		# todo overwrite stats data from steam

		chs = Steam.getStatInt("campaign_high_score")
		fhs = Steam.getStatInt("flow_high_score")
		cry = Steam.getStatInt("crystals")
		shu = Steam.getStatInt("unlocked_ships")

		Global.dbg("shit")
		Global.dbg(chs)
		Global.dbg(fhs)
		Global.dbg(cry)
		Global.dbg(shu)

		Profile.data["highScoreData"]["flow"]["score"] = fhs if (fhs is int) else 0
		Profile.data["highScoreData"]["campaign"]["score"] = chs if (chs is int) else 0
		Profile.data["crystals"] = cry if (cry is int) else 0
		Profile.data["unlocked_ships"] = shu if (shu is int) else 0

		Global.dbg("debug")
		Global.dbg(Profile.data)

var current_leaderboard = ""
var current_score = 0

var leaderboard_handles = {
	"campaign_high_score": -1,
	"flow_high_score": -1,
	"test": -1
}

func saveHighScore(score: int, mode, force_name=null):
	Global.dbg("save hs log")
	Global.dbg(score)
	Global.dbg(mode)
	if isStoreType("steam"):
		Global.dbg("save high to store")
		var lb_name = "campaign_high_score" if mode=="campaign" else "flow_high_score"

		if(force_name):
			lb_name = force_name

		Global.dbg(lb_name)
		current_leaderboard = lb_name
		current_score = score
		Steam.findLeaderboard(lb_name)

func initLeaderboard():
	# initialize callbacks
	Steam.connect("leaderboard_find_result", self, "_on_leaderboard_find_result")
	Steam.connect("leaderboard_score_uploaded", self, "_on_leaderboard_score_uploaded")
	Steam.connect("leaderboard_scores_downloaded", self, "_on_leaderboard_scores_downloaded")

func _on_leaderboard_find_result(handle: int, found: int) -> void:
	if found == 1:

		Global.dbg("LB DEBUG")

		Global.dbg(leaderboard_handles[current_leaderboard])
		Global.dbg(handle)

		leaderboard_handles[current_leaderboard] = handle
		# maybe send some data for high score details like duration, cristals collected, maybe accuracy? etc
		Global.dbg("Leaderboard handle found: %s" % handle)
		Steam.uploadLeaderboardScore( int(current_score), false, [], handle)
	else:
		Global.dbg("No handle was found")

func _on_leaderboard_score_uploaded(success: int, this_handle: int, this_score: Dictionary) -> void:
	Global.dbg("DBG!!!")
	Global.dbg(success)
	Global.dbg(this_handle)
	Global.dbg(this_score)
	Global.dbg("-----")

	if success == 1:
		Global.dbg("Successfully uploaded scores!")
		Global.dbg(this_score)
		# Add additional logic to use other variables passed back
	else:
		Global.dbg("Failed to upload scores!")
		Global.dbg(this_score)

func _on_leaderboard_scores_downloaded(message: String, _this_leaderboard_handle: int, _result: Array) -> void:
	Global.dbg("Scores downloaded message: %s" % message)

	# Save this for later leaderboard interactions, if you want
	# var leaderboard_handle: int = _this_leaderboard_handle

	# Add logic to display results
	# for this_result in result:
		# Use each entry that is returned

func eraseStats():
	if isStoreType("steam"):
		Steam.setStatInt("campaign_high_score",0)
		Steam.setStatInt("flow_high_score",0)
		Steam.setStatInt("crystals",0)
		Steam.setStatInt("unlocked_ships",0)
