extends Area2D

var randomJiggle = true
var doJiggle = false
export var canKillPlayer = true

func isDeadly():
    return canKillPlayer

func canClash():
	return false

func _ready():

	if $AudioStreamPlayer2D != null:
		$AudioStreamPlayer2D.autoplay = false
		if(Global.OptionsData.isSoundOn):
			$AudioStreamPlayer2D.play()

	if randomJiggle && (randf()<Config.EnemyBulletJiggleChance):
		doJiggle = true
	
func destroy():
	Global.EnemyBulletsOnScreen -= 1
	Global.EnemyBulletsOnScreen = max(0, Global.EnemyBulletsOnScreen)
	queue_free()

func isEnemyBullet():
	return true

var velocity = Vector2(0 ,1)

func getBulletSpeed():
	var speed = Config.EnemyBulletSpeed

	# slightly higher on normal
	if(Global.GameScene.getProperDifficulty() == Global.GameDifficulty.NORMAL):
		speed = int(float(speed) * 0.8)
	
	# if mouse control, speed up bullets to equalize challenge
	if(Global.OptionsData.controlType == Global.GameControlMode.MOUSE):
		speed = int(float(speed) * 1.2)

	# lower bullet speed on easy
	if(Global.GameScene.getProperDifficulty() == Global.GameDifficulty.EASY):
		speed = int(float(speed) * 0.5)

	return speed

func moveBullet(delta):

	# move bullet
	var _velocity = velocity

	if(doJiggle):
		_velocity.x += (randf()-0.5);

	_velocity = _velocity * getBulletSpeed()
	position += _velocity * delta

	# destroy bullet if it goes off screen
	if position.y > Global.getWindowSize().y+100:
		destroy()

func _process(delta):
	moveBullet(delta)