extends Area2D

var Sparkle = preload("res://scenes/Sparkle.tscn")
var Explosion = preload("res://scenes/Explosion_2.tscn")

# we can set this to true to make the bullet pass through enemies
export var isPassThrough = false

var Speed = Config.BulletSpeed

var isDestroyed = false

# if true it can destroy basic enemy bullets
var canClash = false

var doCountTowardBulletsOnScreen = true

# Pool reference for returning bullets to pool
var bullet_pool = null

func destroy(force = false):

	if isDestroyed:
		return false
	
	isDestroyed = true

	# if we don't force the bullet to be destroyed, we only destroy it if it's not a passthrough bullet
	if(not force):
		if(isPassThrough):
			return false

	if(doCountTowardBulletsOnScreen):
		Global.GameScene.BulletsOnScreen -= 1
		Global.GameScene.BulletsOnScreen = max(0, Global.GameScene.BulletsOnScreen)

	var sparkle = Sparkle.instance()
	sparkle.position = position
	Global.GameScene.add_child(sparkle)

	# Return to pool instead of queue_free if pool is available
	if bullet_pool != null:
		bullet_pool.return_bullet(self)
	else:
		queue_free()

func getDamagePoint():

	if(Global.GameScene.isBulletOP):
		return 1000

	var _result = 10*ShipSpecs.getSpecs().weapon_strength_multiplier

	# if easy mode, them make bullets twice as powerful
	if(Global.GameScene.difficulty == Global.GameDifficulty.EASY):
		_result = _result*2

	return _result

func getSpeedMod():
	return 1.0

func setSpeed(speed):
	Speed = speed

var velocity = Vector2(0,0)

func setXVelocity(xVelocity):
	velocity.x = xVelocity

func _ready():
	Global.GameScene.levelConductor.logBullet()
	velocity = Vector2(0, -1)
	var _c = connect("area_entered",self,"_on_hit")

func _on_hit(area):

	var _canEnemyBulletClash = area.has_method("canClash") && area.canClash()

	if(area.has_method("isEnemyBullet")):
		if(area.isEnemyBullet() && canClash && _canEnemyBulletClash):

			var explosion = Explosion.instance()
			explosion.position = global_position;
			explosion.z_index  = z_index+1;

			explosion.scale = Vector2(0.1,0.1)

			explosion.modulate.b = 1.0;
			explosion.modulate.g = 1.0;
			explosion.modulate.r = 1.0;

			Global.GameScene.add_child(explosion);

			if(Global.isChance(randi(),5)):
				Global.GameScene.spawnCrystal(global_position,Global.CrystalType.c100,true);
				Achievments.acquire("hadouken_clash")
			
			Global.playSound(SoundManager.BulletZap,global_position, -10);

			area.destroy()
			self.destroy()
			pass

var wasLogged = false

func _process(delta):

	var _velocity = velocity.normalized() * Speed
	_velocity.x = velocity.x * Speed
	position += _velocity * delta * Vector2(1, getSpeedMod())

	# destroy bullet if it goes off screen
	if position.y < -100:

		if(!wasLogged):
			Global.GameScene.levelConductor.logBullet(true)
			wasLogged = true

		destroy(true)

# Set the pool reference (called by pool when creating bullets)
func set_bullet_pool(pool):
	bullet_pool = pool

# Reset method for object pooling
func reset_for_pool():
	# Reset position and transform
	position = Vector2.ZERO
	rotation = 0
	scale = Vector2.ONE
	modulate = Color(1, 1, 1, 1)

	# Reset bullet state variables
	isDestroyed = false
	doCountTowardBulletsOnScreen = true
	isPassThrough = false
	canClash = false

	# Reset movement variables
	velocity = Vector2(0, -1)  # Default upward movement

	# Reset speed to default
	Speed = Config.BulletSpeed

	# Reset logging flag
	wasLogged = false

	# Reset particle effects if they exist
	if has_node("Particles2D"):
		$Particles2D.visible = true
		$Particles2D.emitting = true
