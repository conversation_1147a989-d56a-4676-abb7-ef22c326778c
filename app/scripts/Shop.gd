extends Control

var prices = Config.ShopPrices

func _process(_delta):
	if(!Global.doThrottle("shop_screen_refresh", 1000)):
		setData()

func setPrices():
	$HBoxContainer/Prices/Speed.text = "$ "+str(prices["speed"])
	$HBoxContainer/Prices/Shield.text = "$ "+str(prices["shield"])
	$HBoxContainer/Prices/Bullet.text = "$ "+str(prices["bullet"])
	$HBoxContainer/Prices/Weapon.text = "$ "+str(prices["weapon"])
	$HBoxContainer/Prices/RapidFire.text = "$ "+str(prices["rapidfire"])
	$HBoxContainer/Prices/Lives.text = "$ "+str(prices["lives"])
	$HBoxContainer/Prices/Wing1.text = "$ "+str(prices["wing1"])
	$HBoxContainer/Prices/Wing2.text = "$ "+str(prices["wing2"])
	$HBoxContainer/Prices/Wing3.text = "$ "+str(prices["wing3"])

func setData():
	$HBoxContainer/Values/Speed.text = str(Global.getSpeedIndex(Profile.getStat("speed", Config.ShipSpeedBase)))
	$HBoxContainer/Values/Bullet.text = str(Global.getBulletIndex(Profile.getStat("bullets", Config.MaxBulletsBase)))

	var weapon_data = Global.getBulletData(Profile.getStat("weapon", Global.PlayerBulletTypes.SINGLE))
	var weapon_name = weapon_data["text"]
	
	$HBoxContainer/Values/Weapon.text = weapon_name
	$HBoxContainer/Values/RapidFire.text = "YES" if Profile.getStat("rapidfire", false) else "NO"
	$HBoxContainer/Values/Lives.text = str(Profile.getStat("lives", Config.ShipLifeBase))

	$HBoxContainer/Values/Wing1.text = str(Profile.getStat("wing1", 0))
	$HBoxContainer/Values/Wing2.text = str(Profile.getStat("wing2", 0))
	$HBoxContainer/Values/Wing3.text = str(Profile.getStat("wing3", 0))

	$HBoxContainer/Values/Shield.text = str(Profile.getStat("shield", 0))

	$Money.text = "$"+str(Profile.getData("crystals", 0))

func setSpeedVisibility():

	var is_visible = Global.OptionsData["controlType"] != Global.GameControlMode.MOUSE

	$HBoxContainer/Buttons/Speed.visible = is_visible
	$HBoxContainer/Labels/Speed.visible = is_visible
	$HBoxContainer/Prices/Speed.visible = is_visible
	$HBoxContainer/Values/Speed.visible = is_visible

var BUY_TEXT = "<--- BUY"
var NOT_ENOUGH = "NEED $!"
var MAXED = "MAXED"

func validateData():

	var lives = Profile.getStat("lives", Config.ShipLifeBase)

	if(lives >= Config.ShipMaxLives):
		$HBoxContainer/Buttons/Lives.disabled = true
		$HBoxContainer/Buttons/Lives.text = MAXED
	else:
		if(!Profile.spendMoney(prices["lives"], true)):
			$HBoxContainer/Buttons/Lives.disabled = true
			$HBoxContainer/Buttons/Lives.text = NOT_ENOUGH
		else:
			$HBoxContainer/Buttons/Lives.disabled = false
			$HBoxContainer/Buttons/Lives.text = BUY_TEXT

	if(Profile.getStat("speed", Config.ShipSpeedBase) >= Config.ShipSpeedMax):
		$HBoxContainer/Buttons/Speed.disabled = true
		$HBoxContainer/Buttons/Speed.text = MAXED
	else:
		if(!Profile.spendMoney(prices["speed"], true)):
			$HBoxContainer/Buttons/Speed.disabled = true
			$HBoxContainer/Buttons/Speed.text = NOT_ENOUGH
		else:
			$HBoxContainer/Buttons/Speed.disabled = false
			$HBoxContainer/Buttons/Speed.text = BUY_TEXT

	if(Profile.getStat("bullets", Config.MaxBulletsBase) >= Config.MaxBulletsLimit):
		$HBoxContainer/Buttons/Bullet.disabled = true
		$HBoxContainer/Buttons/Bullet.text = MAXED
	else:
		if(!Profile.spendMoney(prices["bullet"], true)):
			$HBoxContainer/Buttons/Bullet.disabled = true
			$HBoxContainer/Buttons/Bullet.text = NOT_ENOUGH
		else:
			$HBoxContainer/Buttons/Bullet.disabled = false
			$HBoxContainer/Buttons/Bullet.text = BUY_TEXT

	if(Profile.getStat("weapon", Global.PlayerBulletTypes.SINGLE) >= (Global.PlayerBulletTypes.size() - 1)):
		$HBoxContainer/Buttons/Weapon.disabled = true
		$HBoxContainer/Buttons/Weapon.text = MAXED
	else:
		if(!Profile.spendMoney(prices["weapon"], true)):
			$HBoxContainer/Buttons/Weapon.disabled = true
			$HBoxContainer/Buttons/Weapon.text = NOT_ENOUGH
		else:
			$HBoxContainer/Buttons/Weapon.disabled = false
			$HBoxContainer/Buttons/Weapon.text = BUY_TEXT

	if(Profile.getStat("rapidfire", false)):
		$HBoxContainer/Buttons/RapidFire.disabled = true
		$HBoxContainer/Buttons/RapidFire.text = MAXED
	else:
		if(!Profile.spendMoney(prices["rapidfire"], true)):
			$HBoxContainer/Buttons/RapidFire.disabled = true
			$HBoxContainer/Buttons/RapidFire.text = NOT_ENOUGH
		else:
			$HBoxContainer/Buttons/RapidFire.disabled = false
			$HBoxContainer/Buttons/RapidFire.text = BUY_TEXT

	if(Profile.getStat("shield", 0) >= Config.MaxShields):
		$HBoxContainer/Buttons/Shield.disabled = true
		$HBoxContainer/Buttons/Shield.text = MAXED
	else:
		if(!Profile.spendMoney(prices["shield"], true)):
			$HBoxContainer/Buttons/Shield.disabled = true
			$HBoxContainer/Buttons/Shield.text = NOT_ENOUGH
		else:
			$HBoxContainer/Buttons/Shield.disabled = false
			$HBoxContainer/Buttons/Shield.text = BUY_TEXT
	
	if(Profile.wingSum() >= Config.MaxWingCount):
		$HBoxContainer/Buttons/Wing1.disabled = true
		$HBoxContainer/Buttons/Wing2.disabled = true
		$HBoxContainer/Buttons/Wing3.disabled = true
		$HBoxContainer/Buttons/Wing1.text = MAXED
		$HBoxContainer/Buttons/Wing2.text = MAXED
		$HBoxContainer/Buttons/Wing3.text = MAXED
	else:
		if(!Profile.spendMoney(prices["wing1"], true)):
			$HBoxContainer/Buttons/Wing1.disabled = true
			$HBoxContainer/Buttons/Wing1.text = NOT_ENOUGH
		else:
			$HBoxContainer/Buttons/Wing1.disabled = false
			$HBoxContainer/Buttons/Wing1.text = BUY_TEXT

		if(!Profile.spendMoney(prices["wing2"], true)):
			$HBoxContainer/Buttons/Wing2.disabled = true
			$HBoxContainer/Buttons/Wing2.text = NOT_ENOUGH
		else:
			$HBoxContainer/Buttons/Wing2.disabled = false
			$HBoxContainer/Buttons/Wing2.text = BUY_TEXT

		if(!Profile.spendMoney(prices["wing3"], true)):
			$HBoxContainer/Buttons/Wing3.disabled = true
			$HBoxContainer/Buttons/Wing3.text = NOT_ENOUGH
		else:
			$HBoxContainer/Buttons/Wing3.disabled = false
			$HBoxContainer/Buttons/Wing3.text = BUY_TEXT

func _ready():
	var _ch = $CloseButton.connect("pressed", self, "_on_CloseButton_pressed")
	setData()
	setPrices()
	initButtons()
	validateData()
	$CloseButton.grab_focus()
	setSpeedVisibility()

func initButtons():
	var _btn1 = $HBoxContainer/Buttons/Speed.connect("pressed", self, "_incSpeed")
	var _btn2 = $HBoxContainer/Buttons/Bullet.connect("pressed", self, "_incBullets")
	var _btn3 = $HBoxContainer/Buttons/Weapon.connect("pressed", self, "_incWeapon")
	var _btn4 = $HBoxContainer/Buttons/RapidFire.connect("pressed", self, "_buyRapidFire")
	var _btn5 = $HBoxContainer/Buttons/Lives.connect("pressed", self, "_incLives")
	var _btn6 = $HBoxContainer/Buttons/Wing1.connect("pressed", self, "_incWing1")
	var _btn7 = $HBoxContainer/Buttons/Wing2.connect("pressed", self, "_incWing2")
	var _btn8 = $HBoxContainer/Buttons/Wing3.connect("pressed", self, "_incWing3")
	var _btn9 = $HBoxContainer/Buttons/Shield.connect("pressed", self, "_incShield")

func _incSpeed():

	if(!Profile.spendMoney(prices["speed"])):
		return false

	Profile.incSpeed()
	setData()
	validateData()

func _incBullets():

	if(!Profile.spendMoney(prices["bullet"])):
		return false

	Profile.incBullet()
	setData()
	validateData()

func _incWeapon():

	if(!Profile.spendMoney(prices["weapon"])):
		return false

	Profile.incWeapon()
	setData()
	validateData()

func _buyRapidFire():

	if(!Profile.spendMoney(prices["rapidfire"])):
		return false

	Profile.setRapidFire()
	setData()
	validateData()

func _incLives():

	if(!Profile.spendMoney(prices["lives"])):
		return false

	Profile.incLives()
	setData()
	validateData()

func _incShield():
	if(!Profile.spendMoney(prices["shield"])):
		return false

	Profile.incShield()
	setData()
	validateData()

func _incWing1():
	if(!Profile.spendMoney(prices["wing1"])):
		return false

	Profile.addWing1()
	setData()
	validateData()

func _incWing2():

	if(!Profile.spendMoney(prices["wing2"])):
		return false

	Profile.addWing2()
	setData()
	validateData()

func _incWing3():
	if(!Profile.spendMoney(prices["wing3"])):
		return false

	Profile.addWing3()
	setData()
	validateData()

func _input(event):
	if event.is_action_pressed("button_back"):
		_on_CloseButton_pressed()

func _on_CloseButton_pressed():
	queue_free()
