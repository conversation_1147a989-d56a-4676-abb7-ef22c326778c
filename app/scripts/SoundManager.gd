extends Node

# sounds

var HitSound = preload("res://assets/sounds/zap.wav")
var BonusSound = preload("res://assets/sounds/bonus.wav")
var BulletZap = preload("res://assets/sounds/512471__michael_grinnell__electric-zap.wav")
var MoneySound = preload("res://assets/sounds/money.wav")
var AttackSound = preload("res://assets/sounds/attack1.wav")
var Bullet1Sound = preload("res://assets/sounds/laser2.wav")
var Clunk = preload("res://assets/sounds/122103__greatmganga__dshk_01.wav")
var PowerupSound = preload("res://assets/sounds/powerup.wav")
var BuzzerSound = preload("res://assets/sounds/wrong-buzzer.wav")
var PowerupSoundWeapon = preload("res://assets/sounds/powerup_weapon.wav")
var MetalThud = preload("res://assets/sounds/metal_thud.wav")
var ExtraLife = preload("res://assets/sounds/211783__taira-komori__powerup04.mp3")
var Shield = preload("res://assets/sounds/49695__ejfortin__energy-whip-2.wav")
var BigLaser = preload("res://assets/sounds/575804__rickplayer__big-laser-beam.mp3")
var Notification = preload("res://assets/sounds/523424__andersmmg__ding-3.wav")
var Notification2 = preload("res://assets/sounds/582706__ironcross32__short-chirp-09.wav")
var BulletLaser = preload("res://assets/sounds/428638__iut_paris8__quillard_charles_2018_laser.wav")
var ShortBeep = preload("res://assets/sounds/582697__ironcross32__short-beep-02.wav")


# music

var music_bg1 = preload("res://assets/music/Aries_Beats_-_Beverly_Hills_Party.mp3")
var music_bg2 = preload("res://assets/music/Aries_Beats_-_Synth_Funk.mp3")
var music_bg3 = preload("res://assets/music/Aries_Beats_-_Retro_Electro_EDM.mp3")
var music_bg4 = preload("res://assets/music/Aries_Beats_-_Escape.mp3")
var music_boss = preload("res://assets/music/Aries_Beats_-_Synth_Rock.mp3")
var music_menu = preload("res://assets/music/Aries_Beats_-_Chill_Trap.mp3")
var music_gameover = preload("res://assets/music/Aries_Beats_x_Seira_BeatS_-_Space_Chill_Trap.mp3")
var music_final_boss = preload("res://assets/music/final_boss_1.mp3")
var music_chill = preload("res://assets/music/chill.mp3")
var music_shmup1 = preload("res://assets/music/Infraction-Lethal-Choice-pr.mp3")
var music_shmup2 = preload("res://assets/music/endless.mp3")
var music_shmup3 = preload("res://assets/music/Infraction-First-Shot.mp3")
var music_shmup4 = preload("res://assets/music/cyberduck.mp3")

# tts

var tts_extra_speed = preload("res://assets/tts/extra_speed.mp3")
var tts_extra_bullet = preload("res://assets/tts/extra_bullet.mp3")
var tts_shield = preload("res://assets/tts/shield.mp3")
var tts_single = preload("res://assets/tts/single.mp3")
var tts_double = preload("res://assets/tts/double.mp3")
var tts_tripple = preload("res://assets/tts/tripple.mp3")
var tts_simple_wing = preload("res://assets/tts/simple_wing.mp3")
var tts_super_wing = preload("res://assets/tts/super_wing.mp3")
var tts_homing_wing = preload("res://assets/tts/homing_wing.mp3")
var tts_bullet_hell = preload("res://assets/tts/bullet_hell.mp3")
var tts_auto_fire = preload("res://assets/tts/auto_fire.mp3")
var tts_super_bullet = preload("res://assets/tts/super_bullet.mp3")
var tts_super_tripple = preload("res://assets/tts/super_tripple.mp3")
var tts_homing_single = preload("res://assets/tts/homing_single.mp3")
var tts_homing_tripple = preload("res://assets/tts/homing_tripple.mp3")
var tts_super_homing = preload("res://assets/tts/super_homing.mp3")
var tts_laser = preload("res://assets/tts/laser.mp3")
var tts_super_rapidfire = preload("res://assets/tts/super_rapidfire.mp3")
var tts_crystal_magnet = preload("res://assets/tts/crystal_magnet.mp3")
var tts_invincibility = preload("res://assets/tts/invincibility.mp3")
var tts_crystal_explosion = preload("res://assets/tts/crystal_explosion.mp3")
var tts_increased_luck = preload("res://assets/tts/increased_luck.mp3")
var tts_crystal_doubler = preload("res://assets/tts/crystal_doubler.mp3")
var tts_mine_field = preload("res://assets/tts/mine_field.mp3")
var tts_cursed = preload("res://assets/tts/cursed.mp3")
var tts_cursed_module = preload("res://assets/tts/cursed_module.mp3")
var tts_extra_life = preload("res://assets/tts/extra_life.mp3")
var tts_bonus = preload("res://assets/tts/bonus.mp3")
var tts_massacre = preload("res://assets/tts/massacre.mp3")
var tts_secret_found = preload("res://assets/tts/secret_found.mp3")
var tts_game_over = preload("res://assets/tts/game_over.mp3")
var tts_heeeeeeeee_yaaaaaaaaa = preload("res://assets/tts/heyaa.mp3")
var tts_weeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee = preload("res://assets/tts/weeee.mp3")
var tts_yaaaaaaaaa_hooooooooo = preload("res://assets/tts/yahoo.mp3")
var tts_boss_fight = preload("res://assets/tts/boss_fight.mp3")
var tts_space_junk_incomming = preload("res://assets/tts/space_junk_incomming.mp3")
var tts_bonus_stage = preload("res://assets/tts/bonus_stage.mp3")
var tts_get_ready = preload("res://assets/tts/get_ready.mp3")
var tts_campaign = preload("res://assets/tts/campaign.mp3")
var tts_flow = preload("res://assets/tts/flow.mp3")
var tts_welcome = preload("res://assets/tts/welcome.mp3")
var tts_damn_it = preload("res://assets/tts/damn_it.mp3")
var tts_maxed_out = preload("res://assets/tts/maxed_out.mp3")
var tts_that_was_close = preload("res://assets/tts/that_was_close.mp3")
var tts_splash = preload("res://assets/tts/splash.mp3")
var tts_enemy_rush = preload("res://assets/tts/enemy_rush.mp3")
var tts_huge_object_ahead = preload("res://assets/tts/huge_object_ahead.mp3")
var tts_cheater_cheater = preload("res://assets/tts/cheater_cheater.mp3")
var tts_wow = preload("res://assets/tts/wow.mp3")
