extends Node

# The level conductor is responsible for loading the level, spawning enemies, and coordinating the level's enemies
# it contains the level instance, and the enemy coordinator

var currentLevel = Config.StartFromLevel
var nextLevel = Config.StartFromLevel+1

var levelInstance = null
var nextLevelData = null

var levelOffset = 0
var playThroughCounter = 0
var enemyCoordinator = null

var levelStartTs = 0
var lastLevelDurationTs = 0

func _init():
	Global.setCheatState(false)

func getSummaryStatsLabel(key):
	return {
		Global.StatsKeys.LEVEL: "Level",
		Global.StatsKeys.ACCURACY: "Accuracy",
		Global.StatsKeys.DURATION: "Duration",
		Global.StatsKeys.CLOSE_KILLS: "Close Kills",
		Global.StatsKeys.ENTRY_KILLS: "Entry Kills",
		Global.StatsKeys.ROWS_CLEARED: "Rows Cleared",
		Global.StatsKeys.GEMS: "Gems Collected",
		Global.StatsKeys.SCORE: "Score",
		Global.StatsKeys.IS_HIGH_SCORE: "High Score",
		Global.StatsKeys.DIFFICULTY: "Difficulty",
		Global.StatsKeys.REAL_DIFFICULTY: "Real Difficulty",
	}[key]

func getSummaryStatsPrefix(key):
	return {
		Global.StatsKeys.LEVEL: "",
		Global.StatsKeys.ACCURACY: "",
		Global.StatsKeys.DURATION: "",
		Global.StatsKeys.CLOSE_KILLS: "",
		Global.StatsKeys.ENTRY_KILLS: "",
		Global.StatsKeys.ROWS_CLEARED: "",
		Global.StatsKeys.GEMS: "$",
		Global.StatsKeys.SCORE: "",
		Global.StatsKeys.IS_HIGH_SCORE: "",
		Global.StatsKeys.DIFFICULTY: "",
		Global.StatsKeys.REAL_DIFFICULTY: "",
	}[key]

func getSummaryStatsSuffix(key):
	return {
		Global.StatsKeys.LEVEL: "",
		Global.StatsKeys.ACCURACY: " %",
		Global.StatsKeys.DURATION: " sec",
		Global.StatsKeys.CLOSE_KILLS: " %",
		Global.StatsKeys.ENTRY_KILLS: " %",
		Global.StatsKeys.ROWS_CLEARED: " %",
		Global.StatsKeys.GEMS: "",
		Global.StatsKeys.SCORE: "",
		Global.StatsKeys.IS_HIGH_SCORE: "",
		Global.StatsKeys.DIFFICULTY: "",
		Global.StatsKeys.REAL_DIFFICULTY: "",
	}[key]

func getLevelNumber():
	return currentLevel+levelOffset

func getNextLevelNumber():
	return getLevelNumber()+1

func getCurrent():
	return levelInstance	

func loadLevelInstances():

	var ldata = Global.GameScene.levelData['data'][currentLevel-1]
	var ltype = ldata['Type']

	if(ltype=="Boss"):
		levelInstance = load("res://scenes/levels/Level" + ldata['Scene'] + ".tscn").instance()
	else:
		levelInstance = load("res://scenes/levels/Level" + ltype + ".tscn").instance()

	levelInstance.levelData = ldata

	if isLastLevel(currentLevel+1):
		nextLevelData = Global.GameScene.levelData['data'][0]
	else:
		nextLevelData = Global.GameScene.levelData['data'][currentLevel]


var didStart = false

signal row_eliminated(object, lastEnemy)
signal all_row_eliminated(object, lastEnemy)
signal close_kill(object, lastEnemy)

func getEnemyCoordinator():
	return enemyCoordinator

# check if user cleared the whole row while enemies entering or leaving the level
func checkIfRowCleared(enemy, killedCount):

	# check only if it's an enemy that was killed while entering or leaving the level
	if not (enemy.mode in [ Global.EnemyMode.ENTRY, Global.EnemyMode.ESCAPE ]):
		return false
	
	# get enemy count in the current row
	var enemyCountAll = getCurrent().getEnemyCountPerRow()[enemy.rowPosition]

	return enemyCountAll == killedCount

# check if user killed an enemy that was close
func checkIfCloseKill(enemy):
	
	var player = Global.getPlayer()

	if(enemy.position.y>player.position.y-Config.CloseKillDistance and not enemy.position.y>player.position.y):
		if(enemy.position.x>player.position.x-Config.CloseKillDistance and enemy.position.x<player.position.x+Config.CloseKillDistance):
			return true

	return false

# how many enemies were killed in each row while entering the level
var enemyKillLog = {}

func getKillLog(level=-1):
	if(level<=0):
		level = getLevelNumber()
	
	if(!enemyKillLog.has(level)):
		Global.dbg("killlog has no level index")
		return []

	return enemyKillLog[level]

var wasAnyKill = false
var bulletLog = {}
var levelStat = {}

# accuracy, you have to fire minimum 10 bullets
# after that we start to count

func getAccuracy(level = 0):

	if(level==0):
		level = currentLevel

	var blog = getBulletLog(level)

	if(!blog || blog['fired']<=5):
		return 0
	
	return 100 - int((float(blog['missed']) / float(blog['fired'])*100.0))


func getBulletLog(level):

	if(!bulletLog.has(level)):
		return null
	
	return bulletLog[level]

func logBullet(didMiss = false):

	var level = getLevelNumber()

	var bulletCnt = "fired"
	var missCnt = "missed"

	if(!bulletLog.has(level)):

		bulletLog[level] = {}

		bulletLog[level][bulletCnt] = 0
		bulletLog[level][missCnt] = 0

	if(didMiss):
		if(bulletLog[level][missCnt]<bulletLog[level][bulletCnt]):
			bulletLog[level][missCnt]+=1
	else:
		bulletLog[level][bulletCnt]+=1

func setStat(level, key, value ):

	if(!levelStat.has(level)):
		levelStat[level] = {}

	levelStat[level][key] = value

func getStat(level,key):

	if(!levelStat.has(level)):
		return 0

	if(!levelStat[level].has(key)):
		return 0

	return levelStat[level][key]

func incStat(level, key, incBy = 1 ):

	if(!levelStat.has(level)):
		levelStat[level] = {}
	
	if(!levelStat[level].has(key)):
		levelStat[level][key] = 0

	levelStat[level][key] += incBy
	
	
var closeKillCnt = 0

# add log entry and number for statistics and bonuses
func addEnemyKillLog(enemy):

	wasAnyKill = true

	var level = getLevelNumber()

	var cntKey = str(level)+"_"+str(enemy.rowPosition)+"_killedWhileEnterExitCnt"
	var closeKillKey = str(level)+"_"+str(enemy.rowPosition)+"_closeKillCnt"
	var rowKillKey = str(level)+"_rowKillCnt"

	if not enemyKillLog.has(level):
		enemyKillLog[level] = []

	if not enemyKillLog.has(cntKey):
		enemyKillLog[cntKey] = 0

	if not enemyKillLog.has(closeKillKey):
		enemyKillLog[closeKillKey] = 0

	if not enemyKillLog.has(rowKillKey):
		enemyKillLog[rowKillKey] = 0
		
	var closeKill = false

	if(checkIfCloseKill(enemy) && !enemy.wasCollisionKill):
		Achievments.acquire("first_close_kill");

		Global.GameScene.spawnBonusLabel(enemy.position, "Close Kill!\n+"+str(Config.CloseKillPoints));
		Global.playTts(SoundManager.tts_splash)
		closeKillCnt+=1

		if(closeKillCnt==10):
			Achievments.acquire("dangerous_dance")

		Global.GameScene.addScore(Config.CloseKillPoints);
		closeKill = true
		enemyKillLog[closeKillKey] += 1
		emit_signal("close_kill", enemy)
	
	if(closeKill):
		incStat(level,"close_kill_cnt")
	
	enemyKillLog[level].append({
		"row": enemy.rowPosition,
		"species": enemy.species,
		"mode": enemy.mode,
		"closeKill": closeKill
	})

	if(enemy.mode in [ Global.EnemyMode.ENTRY, Global.EnemyMode.ESCAPE ]):
		enemyKillLog[cntKey] += 1
		incStat(level,"entry_kill_cnt")

	if(checkIfRowCleared(enemy, enemyKillLog[cntKey])):
		enemyKillLog[rowKillKey] += 1
		Global.GameScene.shakeCamera(0.2)
		Global.GameScene.spawnBonusLabel(enemy.position, "Cleared!\n+"+str(Config.CleanRowPoints));
		Global.GameScene.spawnBonusCrystals(3,false, enemy.position, true)
		Global.GameScene.addScore(Config.CleanRowPoints)
		emit_signal("row_eliminated", enemy)
		incStat(level,"row_kill_cnt")

		Global.GameScene.FlashWorldEnv(0.5)
	
	# check if all row enemies were killed while entering or leaving the level
	if(enemyKillLog[rowKillKey] == levelInstance.getRowCount()):
		if !Global.doThrottle("massacre_throttle",1000):

			Global.GameScene.spawnBonusLabel(Global.getWindowSize()/Vector2(2.0,2.0), "MASSACRE!\n+"+str(Config.CleanAllRowPoints),0.3, false, true, 2.0);
			Global.GameScene.displayNotification("Massacre!","Bonus")
			Global.playTts(SoundManager.tts_massacre)

			if(randi()%10<5):
				var _xc = Global.getWindowSize().x*randf()
				_xc = max(150, _xc)
				_xc = min(Global.getWindowSize().x-150,_xc)
				Global.GameScene.spawnPowerup( Vector2(_xc,-100) , Global.PowerupType.CRYSTAL_MAGNET, Config.PowerupMaxSpeed, Config.TimedEffectBase)

			Global.GameScene.shakeCamera(0.5)
			#Global.GameScene.spawnBonusCrystals(50,true)
			Global.GameScene.spawnLargeCrystal(false)
			Global.GameScene.addScore(Config.CleanAllRowPoints)
			emit_signal("all_row_eliminated", enemy)

			Global.GameScene.FlashWorldEnv(1.0, 5.0)


	

func startNow():

	if(!Global.GameScene.player):
		Global.GameScene.addPlayer(true)

	if(Global.GameScene.isMode(Global.GameMode.FLOW)):
		Global.GameScene.hideLabel()

	loadLevelInstances()

	if(Global.GameScene.isMode(Global.GameMode.CAMPAIGN)):
		Global.GameScene.displayMessage("\""+levelInstance.getLevelTitle()+"\"",1.5)

	# remove instances from previous level

	for _c in Global.CurrentLevelScene().get_children():
		_c.call_deferred("queue_free")
	
	# spawn current level - just in case it has anything on it
	Global.CurrentLevelScene().add_child(levelInstance)

	# initialize stuff

	levelInstance.initLevel()
	didStart = true
	
	Global.GameScene.didLevelStart = true

	Global.playMusic(Global.GameScene.get_node("BackgroundMusicPlayer"), self.getCurrent().getMusic())

	# set background

	Global.GameScene.changeBg(self.getCurrent().getBg())

# start the game
func start():
	# initialize variables
	Global.GameScene.isCurrentBossReady = false
	Global.GameScene.didLevelStart = false

	# delay start
	Global.setTimeout(self,2,self,"startNow");

func backToTitle():
	var _s = get_tree().change_scene("res://scenes/TitleScreen.tscn")

func isLastLevel(levelNumber):
	return levelNumber>Global.GameScene.levelData['data'].size()

func giveFinalAchievment():

	if(Global.GameScene.difficulty == Global.GameDifficulty.EASY):
		Achievments.acquire("finished_easy")

	if(Global.GameScene.difficulty == Global.GameDifficulty.NORMAL):
		Achievments.acquire("finished_normal")

	if(Global.GameScene.difficulty == Global.GameDifficulty.HARD):
		Achievments.acquire("finished_hard")

	if(Global.GameScene.difficulty == Global.GameDifficulty.EXTREME):
		Achievments.acquire("finished_extreme")
		if(playThroughCounter==10):
			Achievments.acquire("finished_10x_extreme")
	
	if(playThroughCounter==2):
		Achievments.acquire("finished_2x")

	if(playThroughCounter==5):
		Achievments.acquire("finished_5x")

	if(playThroughCounter==10):
		Achievments.acquire("finished_10x")

func goToNextLevel():
	currentLevel += 1

	# we have no more levels, increase challenge and repeat
	if isLastLevel(currentLevel):

		# unlock next ship
		Global.GameScene.unlockNextShip()

		levelOffset += (currentLevel-1)
		playThroughCounter+=1
		currentLevel = 1
		giveFinalAchievment()

	start()

func playLevelTts(type):

	if('LabelOverride' in nextLevelData):
		return false

	if type == "Boss":
		Global.playTts(SoundManager.tts_boss_fight)
	elif type == "Bonus":
		Global.playTts(SoundManager.tts_bonus_stage)
	elif type == "Rush":
		Global.playTts(SoundManager.tts_enemy_rush)
	elif type == "Debris":
		Global.playTts(SoundManager.tts_space_junk_incomming)
	else:
		Global.playTts(SoundManager.tts_get_ready)

func getLevelTypeString(levelNumber, type, overrider = ""):

	if(overrider!=""):
		return overrider

	if type == "Boss":
		return "BOSS FIGHT"
	elif type == "Bonus":
		return "BONUS LEVEL"
	elif type == "Rush":
		return "ENEMY RUSH"
	elif type == "Debris":
		return "DEBRIS AHEAD"
	else:
		return "Level " + str(levelNumber)

func didLevelStart():
	return didStart;

func _initLevelLoad():

	# display stats here
	var accuracy = getAccuracy()
	levelStartTs = Tick.ms()

	if(accuracy>=99):

		Global.GameScene.spawnBonusLabel(Vector2(Global.getPlayerPosition().x,Global.getWindowSize().y-70) , "Perfect Accuracy: "+str(getAccuracy())+"% !", 0.3, true,true,1);
		Global.GameScene.addScore(Config.MaxAccuracyPoints)
		Global.GameScene.spawnBonusCrystals(10,true)
		Global.playTts(SoundManager.tts_wow)
		Achievments.acquire("perfect_accuracy")


	elif(accuracy>=95):

		Global.GameScene.spawnBonusLabel(Vector2(Global.getPlayerPosition().x,Global.getWindowSize().y-70) , "High Accuracy: "+str(getAccuracy())+"% !", 0.3, true,false,1);
		Global.GameScene.addScore(Config.HighAccuracyPoints)
		Global.GameScene.spawnBonusCrystals(20,true)
		Global.playTts(SoundManager.tts_wow)
		Achievments.acquire("high_accuracy")

	if(!didStart):
		return false

	didStart = false
	levelInstance.hideAllStuffOnLevel()

	if(Global.GameScene.isMode(Global.GameMode.CAMPAIGN)):
		var _lbl = ""

		if("LabelOverride" in nextLevelData):
			_lbl = nextLevelData['LabelOverride']

		Global.GameScene.displayMessage(getLevelTypeString(getNextLevelNumber(),nextLevelData['Type'], _lbl),1.5)
		playLevelTts(nextLevelData['Type'])

	Global.setTimeout(self,2,self,"goToNextLevel");

var summaryStatsLog = {}

func getSummaryStatsText(doDisplay = false):
	var _stats = getSummaryStats()
	var _text = "GAME STATS :\n-------------\n"

	for _k in Global.StatsKeys:
		var _ks = Global.StatsKeys[_k]
		var line = getSummaryStatsLabel(_ks) +": "+ getSummaryStatsPrefix(_ks) + str(_stats["key_"+str(_ks)]) + getSummaryStatsSuffix(_ks)+ "\n"

		_text+=line

	if(doDisplay):
		Global.GameScene.displayNotification(_text,"","",8)

	return _text

func getSummaryStats():

	var summaryStat = {

		# ---------------- STATIC --------------------

		"key_"+str(Global.StatsKeys.LEVEL): 0,
		"key_"+str(Global.StatsKeys.DURATION): 0,
		"key_"+str(Global.StatsKeys.GEMS): 0,
		"key_"+str(Global.StatsKeys.SCORE): 0,
		"key_"+str(Global.StatsKeys.IS_HIGH_SCORE): 0,
		"key_"+str(Global.StatsKeys.DIFFICULTY): 0,
		"key_"+str(Global.StatsKeys.REAL_DIFFICULTY): 0,

		"cnt_"+str(Global.StatsKeys.LEVEL): 0,
		"cnt_"+str(Global.StatsKeys.GEMS): 0,
		"cnt_"+str(Global.StatsKeys.SCORE): 0,
		"cnt_"+str(Global.StatsKeys.IS_HIGH_SCORE): 0,
		"cnt_"+str(Global.StatsKeys.DURATION): 0,
		"cnt_"+str(Global.StatsKeys.DIFFICULTY): 0,
		"cnt_"+str(Global.StatsKeys.REAL_DIFFICULTY): 0,

		# ---------------- PERCENT -------------------

		"key_"+str(Global.StatsKeys.ACCURACY): 0,
		"key_"+str(Global.StatsKeys.ENTRY_KILLS): 0,
		"key_"+str(Global.StatsKeys.ROWS_CLEARED): 0,
		"key_"+str(Global.StatsKeys.CLOSE_KILLS): 0,

		"cnt_"+str(Global.StatsKeys.ACCURACY): 0,
		"cnt_"+str(Global.StatsKeys.ENTRY_KILLS): 0,
		"cnt_"+str(Global.StatsKeys.ROWS_CLEARED): 0,
		"cnt_"+str(Global.StatsKeys.CLOSE_KILLS): 0
	}

	# gather relevant data

	for _lvl in summaryStatsLog:
		for _entry in summaryStatsLog[_lvl]:
			if(summaryStat.has("key_"+str(_entry["key"]))):
				summaryStat["key_"+str(_entry["key"])]+=_entry["percent"]
				summaryStat["cnt_"+str(_entry["key"])]+=1

	# calculate average

	for _k in Global.StatsKeys:
		var _ks = Global.StatsKeys[_k]
		if(summaryStat.has("key_"+str(_ks))):
			if(summaryStat["cnt_"+str(_ks)]>0):
				summaryStat["key_"+str(_ks)]=summaryStat["key_"+str(_ks)]/summaryStat["cnt_"+str(_ks)]
	
	# set static data

	summaryStat["key_"+str(Global.StatsKeys.LEVEL)] = getLevelNumber()
	summaryStat["key_"+str(Global.StatsKeys.DURATION)] = Global.GameScene.getGameDuration()
	summaryStat["key_"+str(Global.StatsKeys.GEMS)] = Global.GameScene.money
	summaryStat["key_"+str(Global.StatsKeys.SCORE)] = Global.GameScene.score
	summaryStat["key_"+str(Global.StatsKeys.IS_HIGH_SCORE)] = Profile.wasHighScore(Global.GameScene.score, Global.GameScene.getModeStr())
	summaryStat["key_"+str(Global.StatsKeys.DIFFICULTY)] = Global.GameScene.difficulty
	summaryStat["key_"+str(Global.StatsKeys.REAL_DIFFICULTY)] = Global.GameScene.getProperDifficulty()
	
	return summaryStat


func calculateSummaryStats():
	var statsData = []

	var blog = getBulletLog(getLevelNumber())

	if(!blog):
		blog= {
			"missed": 0,
			"fired": 0
		}

	if(!blog.has("missed")):
		blog["missed"] = 0

	if(!blog.has("fired")):
		blog["fired"] = 0

	var bpercent = 0

	if(blog["fired"]>0):
		bpercent = 100 - int((float(blog['missed']) / float(blog['fired'])*100.0))

	statsData.push_back({
		"key": Global.StatsKeys.ACCURACY,
		"type": "Accuracy",
		"text": str(getAccuracy())+" %",
		"max": blog['fired'],
		"val": blog['fired']-blog['missed'],
		"percent": bpercent
	});

	var _durTitle = "Level Duration"

	if(Global.GameScene.isMode(Global.GameMode.FLOW)):
		_durTitle = "Wave Duration"

	statsData.push_back({
		"key": Global.StatsKeys.DURATION,
		"type": _durTitle,
		"text": str(int(lastLevelDurationTs/1000))+" sec",
		"max": 0,
		"val": int(lastLevelDurationTs/1000),
		"percent": 0
	});

	if(getCurrent().getLevelType() == Global.LevelType.NORMAL || getCurrent().getLevelType() == Global.LevelType.BONUS):
		var closeKills = getStat(getLevelNumber(),"close_kill_cnt")
		var entryKills = getStat(getLevelNumber(),"entry_kill_cnt")

		var rowKills = getStat(getLevelNumber(),"row_kill_cnt")

		var all = getCurrent().getEnemyCnt()
		var allRows = getCurrent().getRowCnt()

		var closeKillPercent = int(Global.getPercentage(closeKills, all))
		var entryKillPercent = int(Global.getPercentage(entryKills, all))
		var rowKillPercent = int(Global.getPercentage(rowKills, allRows))

		if(closeKills>0):
			statsData.push_back({
				"key": Global.StatsKeys.CLOSE_KILLS,
				"type": "Close Kills",
				"text": str(closeKills)+" / "+str(all) + " ("+ str(closeKillPercent) +"%)",
				"max": all,
				"val": closeKills,
				"percent": closeKillPercent
			});

		if(entryKills>0):
			statsData.push_back({
				"key": Global.StatsKeys.ENTRY_KILLS,
				"type": "Entry Kills",
				"text": str(entryKills)+" / "+str(all) + " ("+ str(entryKillPercent) +"%)",
				"max": all,
				"val": entryKills,
				"percent": entryKillPercent
			});

		if(rowKills>0):
			statsData.push_back({
				"key": Global.StatsKeys.ROWS_CLEARED,
				"type": "Rows Cleared",
				"text": str(rowKills)+" / "+str(allRows) + " ("+ str(rowKillPercent) +"%)",
				"max": all,
				"val": entryKills,
				"percent": rowKillPercent
			});

	for _line in getCurrent().getLevelStats():
		statsData.push_back({
			"key": _line[5],
			"type": _line[0],
			"text": _line[1],
			"max": _line[2],
			"val": _line[3],
			"percent": _line[4]
		});
	
	summaryStatsLog[getLevelNumber()] = statsData

	return statsData

func displayStats():

	var statsData = calculateSummaryStats()
	var _text = "LEVEL STATS :\n-------------\n"

	if(Global.GameScene.isMode(Global.GameMode.FLOW)):
		_text = "STATS :\n-------\n"

	for _stat in statsData:
		_text += _stat["type"]+": "+_stat["text"] + "\n"
	
	Global.GameScene.displayNotification(_text,"","",8)

func checkIfLevelIsOver():

	if Global.doThrottle("checkIfLevelIsOver",1000):
		return false
	
	if levelInstance.isLevelOver() && !Global.GameScene.isPowerShipOnScreen() && !Global.GameScene.isLargeCrystalOnScreen():
		lastLevelDurationTs = Tick.ms()-levelStartTs
		displayStats()
		self._initLevelLoad()

func _process(_delta):

	if not didStart:
		return false

	checkIfLevelIsOver()

	if !levelInstance.doForceEnemyCoordinator() && levelInstance.getLevelType() == Global.LevelType.BOSS:
		return false

	if Global.GameScene.isPlayerReady():
		enemyCoordinator.coordinateBullets()
		enemyCoordinator.coordinateAttacks()
