class_name LevelBase
extends Node

#map resources to enemy objects
var enemyResources = Global.GameScene.enemyResources

func getRowCnt():

	if(!levelData.has('Config')):
		return 0
	
	return levelData['Config'].size()

func getEnemyCnt():

	if(!levelData.has('Config')):
		return 0

	var _sum = 0
	for _d in levelData['Config']:
		for _e in _d:
			if(_e>0):
				_sum+=1
	return _sum

func getLevelStats():
	# custom stats format: [ ["notification", "prefix", "max", "val", "percent", "key"],...]
	return []

func transalteWiggleConfig(array):
	var res = []
	for s in array:
		res.push_back(Global.WiggleMode[s])
	return res

var levelData = {}

func getEntryPathPreset( index, type = "normal", mirrorx = 0, mirrory = 0, sharpness = 0, duration = 0):

	var entryPathsList = Global.GameScene.patternData[type]

	var path = entryPathsList[index].duplicate()

	path['mirrorx'] = mirrorx
	path['mirrory'] = mirrory
	path['sharpness'] = sharpness
	if(duration>0):
		path['duration'] = duration

	return path


func translateEntryPattern(array):

	var res = []

	for o in array:

		if(o['type']=="BasicPreset"):
			res.push_back(getEntryPathPreset(o['value'][0], "normal", o['value'][1],o['value'][2],o['value'][3],o['value'][4]))
		elif(o['type']=="BonusPreset"):
			res.push_back(getEntryPathPreset(o['value'][0], "bonus", o['value'][1],o['value'][2],o['value'][3],o['value'][4]))
		elif(o['type']=="ShmupPreset"):
			res.push_back(getEntryPathPreset(o['value'][0], "shmup", o['value'][1],o['value'][2],o['value'][3],o['value'][4]))
		else:
			res.push_back(o['value'])
		
	return res

var spaceMine = preload("res://scenes/SpaceMine.tscn");

# Enemy wiggle mode per row
func getEnemyWiggleType():
	return [
		Global.WiggleMode.NORMAL,
		Global.WiggleMode.NORMAL,
		Global.WiggleMode.NORMAL,
		Global.WiggleMode.NORMAL,
		Global.WiggleMode.NORMAL
	]

func getEnemyEntryDelay():
	return levelData['EntryDelay']

func getMusic():

	var music = self.getBgMusic()

	# if self.getLevelType()==Global.LevelType.BOSS:
	# 	music = "boss"
	
	return music

func getBg():
	var _bg = levelData['Bg'] if levelData.has('Bg') else 1
	return _bg

func getBgMusic():
	return levelData['Music']

func getLevelTitle():
	return levelData['Title']

# get enemy count per row
func getEnemyCountPerRow():

	var result = []

	for row in getEnemyConfig():
		var count = 0
		for column in row:
			if column != 0:
				count += 1
		result.append(count)

	return result

func getRowCount():
	return len(getEnemyConfig())


# enemy types and location on the grid
func getEnemyConfig():
	return [
		[0,0,0,0,0,0,0,0,0,0],
		[0,0,0,0,0,0,0,0,0,0],
		[0,0,0,0,0,0,0,0,0,0],
		[0,0,0,0,0,0,0,0,0,0],
		[0,0,0,0,0,0,0,0,0,0],
	]

# where do they come from and anymation type
# mid point location
# [ entry point, mid point, exit point ( oly for bonus stages) ]
func getEntryPattern(): 
	return []

func translateLevelType(typestr):
	return Global.LevelType[typestr.to_upper()]
	
# leve type
func getLevelType():
	return translateLevelType(levelData['Type'])

func getCoordsBasedOnLocation(row, column):

	var margin = 100;
	var margin_bottom = 250;
	var margin_top = 0;

	var x = margin+((Global.getWindowSize().x - 2*margin) / (len(getEnemyConfig()[row]) + 1) * (column + 1))
	var y = margin_top+ ((Global.getWindowSize().y - margin_top - margin_bottom) / (len(getEnemyConfig()) + 1) * (row + 1))

	return Vector2(x,y)

func getEnemiesOverPlayer():
	var enemies = []
	for child in Global.GameScene.get_children():
		if child.has_method("isEnemy") and child.mode != Global.EnemyMode.DEAD:
			if abs(child.position.x-Global.getPlayer().position.x) < 100:
				enemies.append(child)
	return enemies

func canPlayerRespawn():
	return true # because we have shield now
	# return (isAllEnemyIdle() or getEnemiesWithMode(Global.EnemyMode.ENTRY).size() > 0) and Global.EnemyBulletsOnScreen <= 0

func isAllEnemyIdle():
	var enemies = getAllEnemiesOnLevel()

	if len(enemies) == 0:
		return false

	for e in enemies:
		if e.mode != Global.EnemyMode.IDLE:
			return false
	return true

func getEnemiesWithMode(enemyMode):
	var enemies = []
	for child in Global.GameScene.get_children():
		if child.has_method("isEnemy") and child.mode == enemyMode:
				enemies.append(child)
	return enemies

func getAllItemsWithFunctionOnLevel(functionName):
	var items = []

	for child in Global.GameScene.get_children():
		if child.has_method(functionName):
			items.append(child)
	return items

func hideAllStuffOnLevel():
	var mines = getAllItemsWithFunctionOnLevel("isSpaceMine")
	var wings = getAllItemsWithFunctionOnLevel("isEnemyWing")

	if len(mines) > 0:
		for e in mines:
			e.hideAnim();

	if len(wings) > 0:
		for e in wings:
			e.hideAnim();

func getSpecialTargets():
	return [];

func getARandomTarget(prioritizeOverPlayer = false):

	var enemies = getAllEnemiesOnLevel()
	var _result = []
	var priorityEnemies = []

	enemies.append_array(getSpecialTargets());

	if(prioritizeOverPlayer):
		#remove every object not over player

		var _px = Global.getPlayer().position.x

		for e in enemies:
			if is_instance_valid(e):
				if(!(e.position.x<_px-100 || e.position.x>_px+100)):
					priorityEnemies.push_back(e)
		
		if(priorityEnemies.size()>0):
			enemies = priorityEnemies

	# only enemies on screen
	for e in enemies:
		if is_instance_valid(e):
			if !Global.isOffScreen(e.position,50):
				_result.push_back(e);

	if(_result.size()<=0):
		return false
	
	return _result[randi()%_result.size()]

func getAllEnemiesOnLevel():
	var enemies = []
	for child in Global.GameScene.get_children():
		if child.has_method("isEnemy") and child.mode != Global.EnemyMode.DEAD:
			enemies.append(child)
	return enemies

func spawnSpaceMine(instantActivate=false):

	var SpaceMine = self.spaceMine.instance();
	SpaceMine.position.x = 3*(Global.getWindowSize().x/4) - (randf()*Global.getWindowSize().x/2)
	SpaceMine.position.y = -100
	# no sound if instant activated
	SpaceMine.disableBeep = instantActivate

	Global.GameScene.add_child_deferred(SpaceMine)

	if(instantActivate):
		 SpaceMine.call_deferred("activateMine")

	if(instantActivate):
		Global.GameScene.addIndicator(SpaceMine.global_position.x, Global.OISize.Small,Global.OIType.Red,"Armed Mine")
	else:
		Global.GameScene.addIndicator(SpaceMine.global_position.x, Global.OISize.Small,Global.OIType.Yellow,"Mine")


func spawnAttackAndDie(spawnPosition, enemyType):
	var Enemy = enemyResources[str(enemyType)].instance();

	Enemy.position = spawnPosition
	Enemy.shouldDieAfterAttack = true
	Enemy.isAgressive = true
	Enemy.changeState(Global.EnemyMode.ATTACK);
	Enemy.msec_offset = randi()%10000

	Global.GameScene.add_child(Enemy)

func spawn(pathData, enemyType, startDestination, row, col, cnt):

	var Enemy = enemyResources[str(enemyType)].instance();

	Enemy.setPathData(pathData, startDestination, isNormalLevelStyle())
	Enemy.set("wiggleMode", getEnemyWiggleType()[row])
	Enemy.set("entryDestination", startDestination)
	Enemy.set("rowPosition", row)
	Enemy.set("colPosition", col)
	Enemy.set("count", cnt)

	# on shmups eney is agressive
	if Global.GameScene.isMode(Global.GameMode.FLOW) && getLevelType()==Global.LevelType.BONUS:
		Enemy.isAgressive = true
		Enemy.agressionMultiplier = 2.0

	Global.GameScene.add_child(Enemy)

	modifyEnemy(Enemy)
	Enemy.init(getLevelType())



# overwrite to modify enemy
func modifyEnemy(_enemyObject):
	# placeholder
	pass

# spawn enemies off screen based on their entry start point location
func spawnAll():
	var row = 0
	var cnt = 0
	for e in getEnemyConfig():
		for i in range(0, len(e)):
			if e[i] != 0:
				cnt+=1
				spawn(getEntryPattern()[row], e[i], getCoordsBasedOnLocation(row,i), row, i, cnt)
				yield(get_tree(),"idle_frame")
		row += 1

var thread = null

func initLevel():
	spawnAll()
	Global.GameScene.levelConductor.enemyCoordinator = preload("res://scripts/EnemyCoordinator.gd").new()
	Global.GameScene.levelConductor.enemyCoordinator.applyDifficulty()

func customKillCleanup():
	# kill custom shit here, like boss
	pass

func killEverything():

	for _e in getAllEnemiesOnLevel():
		_e.die(true)

	customKillCleanup()

func isLevelOver():
	return getAllEnemiesOnLevel().size() == 0

func doForceEnemyCoordinator():
	return false

func isBossLevel():
	return getLevelType()==Global.LevelType.BOSS;

func isNormalLevelStyle():
	return getLevelType()==Global.LevelType.NORMAL || doForceEnemyCoordinator()
