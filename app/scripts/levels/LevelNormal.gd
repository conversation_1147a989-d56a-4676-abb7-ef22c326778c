extends "res://scripts/levels/LevelBase.gd"


# enemy types and location on the grid
func getEnemyConfig():
	return levelData['Config']

func getEnemyWiggleType():
	return transalteWiggleConfig(levelData['WiggleType'])

func getEnemyEntryDelay():
	return levelData['EntryDelay']

func getEntryPattern(): 
	return translateEntryPattern(levelData['EntryPattern'])

func getLevelType():
	return Global.LevelType.NORMAL

func _ready():
	# TEST ONLY
	# for _i in range(1,5):
	# 	self.spawnSpaceMine();
	randomize()
	pass

func _process(_delta):

	# spawn random mine and debris

	if(!Global.doThrottle("normal_level_do_spawn_mine",1000)):
		if(Global.isChance(randi(),Config.RandomMineChance)):
			if(randi()%10>5):
				spawnSpaceMine(randi()%10>5)
			else:
				Global.GameScene.spawnDebris(Global.DebrisType.Random)
