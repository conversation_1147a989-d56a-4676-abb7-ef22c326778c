
extends "res://scripts/levels/LevelBase.gd"

var maxEnemy = 20 + (Global.GameScene.getProperDifficulty()*10)
var baseEnemyDelayMS = 1000

var enemyCount = 0

func getEnemyRepo():
	return [1,2]

func getMaxEnemy():
	return maxEnemy

func getLevelStats():
	# custom stats format: [ ["notification", "prefix"],...]
	var percent = int(Global.getPercentage(Global.GameScene.levelConductor.getKillLog().size(),getMaxEnemy()))
	var txt = str(Global.GameScene.levelConductor.getKillLog().size()) + " / " + str(getMaxEnemy()) + " (" + str(percent) + "%)"
	return [["KILL COUNT", txt, getMaxEnemy(), Global.GameScene.levelConductor.getKillLog().size(), percent,Global.StatsKeys.ACCURACY]]

func getLevelType():
	return Global.LevelType.RUSH

var _isRushOver = false
var _levelOverDelay = false
var _spawnPoingCnt = 0
var _wasBonus = false

func canPlayerRespawn():
	return Global.GameScene.getLevelObject().getAllEnemiesOnLevel().size()==0

func _process(_delta):

	if(!Global.GameScene.isPlayerReady()):
		return 0

	if(enemyCount<getMaxEnemy()):
		if(!Global.doThrottle("enemy_rush_spawn", baseEnemyDelayMS + randi()%int(baseEnemyDelayMS*0.2))):
			
			_spawnPoingCnt+=1
			if(_spawnPoingCnt==4):
				_spawnPoingCnt=1

			var _re = randi()%getEnemyRepo().size()
			var p1 = Vector2(randf()*Global.getWindowSize().x/4,-100);

			if(_spawnPoingCnt==2):
				p1.x = (3*Global.getWindowSize().x/4)+randf()*Global.getWindowSize().x/4

			elif(_spawnPoingCnt==3):
				p1.x = (Global.getWindowSize().x/3)+randf()*Global.getWindowSize().x/4
				self.spawnSpaceMine(true)

			self.spawnAttackAndDie( p1, str(getEnemyRepo()[_re]) )
			enemyCount+=1
	else:
		if(!_isRushOver):
			_isRushOver = true
			Global.setTimeout(self,3,self,"setOverDelay")
	
	if(isLevelOver() && !_wasBonus):
		_wasBonus = true
		if(Global.GameScene.levelConductor.getKillLog().size()>=getMaxEnemy()):
			if !Global.doThrottle("killed_them_all_throttle",1000):
				Global.GameScene.spawnBonusLabel(Global.getWindowSize()/Vector2(2.0,2.0), "GOT 'EM ALL!\n+"+str(Config.CleanAllRowPoints),0.3, false, true, 2.0);
				Global.playTts(SoundManager.tts_wow)
				Global.GameScene.shakeCamera(0.5)
				Global.GameScene.addScore(Config.CleanAllRowPoints)
				Global.GameScene.FlashWorldEnv(1.0, 5.0)
				Global.GameScene.spawnPowerupShip()


func setOverDelay():
	_levelOverDelay = true

func isLevelOver():
	return _isRushOver && _levelOverDelay && Global.GameScene.getLevelObject().getAllEnemiesOnLevel().size()==0
