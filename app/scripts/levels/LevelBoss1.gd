extends "res://scripts/levels/LevelBase.gd"

func getLevelType():
	return Global.LevelType.BOSS

var Boss1 = preload("res://scenes/Bosses/Boss1/Boss1.tscn")
var bossInstance = null

var wasInit = false

func initLevel():
	bossInstance = Boss1.instance()
	bossInstance.position.y = -200
	bossInstance.position.x = -200
	Global.GameScene.add_child(bossInstance)
	bossInstance.init()
	wasInit = true

func getSpecialTargets():
	var _targets = []

	if is_instance_valid(bossInstance):
		_targets.push_back(bossInstance.getBoss());
		_targets.push_back(bossInstance.getDiscoBall());
		_targets.append_array(bossInstance.getBricks());
		_targets.append_array(bossInstance.getScrollingBricks());
	
	return _targets;

func isLevelOver():

	if(!wasInit):
		return false

	if(not is_instance_valid(bossInstance)):
		return true

	return bossInstance.isDead()

func customKillCleanup():
	if(is_instance_valid(bossInstance)):
		bossInstance.queue_free()

func canPlayerRespawn():
	return true