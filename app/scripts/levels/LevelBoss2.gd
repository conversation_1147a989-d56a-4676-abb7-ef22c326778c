extends "res://scripts/levels/LevelBase.gd"

func initLevel():
	Global.GameScene.isCurrentBossReady = true

func getLevelType():
	return Global.LevelType.BOSS

func isLevelOver():
	return !is_instance_valid($"Path2DLTR/PathFollow2D/Dragon")

func canPlayerRespawn():
	return true

func getLevelTitle():
	return "Monster!"

func getSpecialTargets():
	var _targets = []

	if is_instance_valid($"Path2DLTR/PathFollow2D/Dragon"):
		_targets.append_array($"Path2DLTR/PathFollow2D/Dragon".getTargetArray());
	
	return _targets;

func customKillCleanup():
	if(is_instance_valid($"Path2DLTR/PathFollow2D/Dragon")):
		$"Path2DLTR/PathFollow2D/Dragon".queue_free()

func _ready():
	pass

func _physics_process(_delta):
	pass

func _process(_delta):
	if(!Global.doThrottle("boss2comet",1000)):
		if(Global.isChance(randi(),3)):
			Global.GameScene.spawnDebris()