extends "res://scripts/levels/LevelBaseDebris.gd"

func getLevelType():
	return Global.LevelType.DEBRIS

var _isLevelOver = false

func canPlayerRespawn():
	return objectList.size()==0

func getLevelStats():
	# custom stats format: [ ["notification", "prefix"],...]
	var percent = int(Global.getPercentage(destroyedCnt,objectCnt))
	var txt = str(destroyedCnt) + " / " + str(objectCnt) + " (" + str(percent) + "%)"
	return [["DESTROYED OBJECTS", txt, objectCnt, destroyedCnt, percent, Global.StatsKeys.ACCURACY]]

func isLevelOver():
	return _isLevelOver

var minIntervalSec = 0.5
var maxIntervalSec = 4
var intervalStep = 1

func getIntervalSec():
	var _sec = maxIntervalSec-(duration*intervalStep)
	var _resSec =  max(minIntervalSec,_sec)
	return _resSec

var addAtOnce = 2
var levelDuration = 60
var duration = 0

var objectList = []

func getSpecialTargets():
	var _res = []

	for o in objectList:
		if(is_instance_valid(o) && !Global.isOffScreen(o.global_position,-100)):
			_res.push_back(o)

	return _res

func _ready():
	levelDuration = levelData["DurationBase"]

	if(Global.GameScene.difficulty == Global.GameDifficulty.HARD || Global.GameScene.difficulty == Global.GameDifficulty.EXTREME):
		addAtOnce = 3
		minIntervalSec = 0.75
		levelDuration = int(levelDuration*1.5)

	intervalStep = ((maxIntervalSec-minIntervalSec)/levelDuration)
	duration = 0

	randomize()
	Global.setTimeout(self,2,self,"addDebris")

	Global.GameScene.setSpeed(true)

var endTimerStarted = false

func endLevel():
	_isLevelOver = true

func startEndTimer():

	if(endTimerStarted):
		return false

	endTimerStarted = true

	if(Global.GameScene.isMode(Global.GameMode.CAMPAIGN)):
		Global.GameScene.setSpeed(false)

	Global.setTimeout(self,1,self,"endLevel")

var destroyedCnt = 0

func debrisDestroyed(_object):

	destroyedCnt+=1

	if(destroyedCnt%5==0):
		# spawn a powerup ship for every 5 destroyed debris
		Global.GameScene.addScore(Config.DebrisPoints)
		Global.GameScene.spawnPowerupShip()

	if(destroyedCnt%3==0):
		# spawn a powerup ship for every 5 destroyed debris
		Global.GameScene.addScore(Config.DebrisPoints)
		Global.GameScene.spawnLargeCrystal(false)

var objectCnt = 0

func _addObject():

	if(duration>levelDuration):

		if(objectList.size()==0):
			startEndTimer()

		return false

	var o = Global.GameScene.spawnDebris()
	objectList.push_back(o)
	objectCnt+=1

func addDebris():
	if(Global.GameScene.isPlayerReady()):
		for _i in range(0,addAtOnce):
			_addObject()

	Global.setTimeout(self,getIntervalSec(),self,"addDebris")

func _process(delta):
	duration += delta

	if(!Global.doThrottle("check_debris",250)):

		var newList = []

		for _o in objectList:
			if(is_instance_valid(_o)):
				if(Global.isOffScreenBottom(_o.position,100)):
					_o.call_deferred("queue_free")
				else:
					newList.push_back(_o)

		objectList = newList
