extends "res://scripts/levels/LevelBase.gd"

func getLevelType():
	return Global.LevelType.BOSS

var bossInstance = null
var crystalInstance = null

var wasInit = false

func startWiggle(_a):
	$AnimationPlayerWiggleX.play("Wiggle");
	$AnimationPlayerWiggleY.play("Wiggle");
	Global.GameScene.isCurrentBossReady = true
	bossInstance.init()

func finishLevel():
	isOver = true

func spawnCrystal():
	crystalInstance = Global.GameScene.spawnLargeCrystal(true)
	crystalInstance .connect("CrystalOver",self,"finishLevel")

func initLevel():
	bossInstance = $SpiderBoss
	var _ca = $AnimationPlayer.connect("animation_finished",self,"startWiggle")
	wasInit = true

func getSpecialTargets():

	var _targets = []

	if(is_instance_valid(bossInstance)):
		for _e in bossInstance.getTargets():
			if is_instance_valid(_e) and _e.visible:
				_targets.push_back(_e);

	if(is_instance_valid(crystalInstance)):
		_targets.push_back(crystalInstance)

	return _targets;

var isOver = false 

func isLevelOver():
	if(!wasInit):
		return false

	return isOver

func customKillCleanup():
	# if(is_instance_valid(bossInstance)):
	# 	bossInstance.queue_free()
	pass

func canPlayerRespawn():
	# if(!is_instance_valid(bossInstance)):
	# 	return true
	
	if(is_instance_valid(bossInstance) && bossInstance.laserInProgress):
		return false

	return true

