extends "res://scripts/levels/LevelBase.gd"

var EmptyScene = preload("res://scenes/EmptyScene.tscn")

func getLevelType():
	return Global.LevelType.BOSS

var wasInit = false

func _process(delta):
	pass

func showTitle():
	Global.GameScene.displayMessage("DEMO COMPLETE!",5)

var _targets = []

func spawnPoints():
	var viewport_size = get_viewport().get_visible_rect().size
	for i in range(30):
		var new_scene = EmptyScene.instance()
		var random_x = randi()%int(viewport_size.x)
		var random_y = (randi()%int(viewport_size.y)) / 1.5
		var random_s = randf()/5+0.5
		var random_a = randf()/0.8
		new_scene.position = Vector2(random_x, random_y)
		new_scene.scale = Vector2(random_s, random_s)
		new_scene.modulate.a = random_a
		Global.GameScene.add_child(new_scene)
		_targets.push_back(new_scene)

func lbl(txt, aSpeed = 0.3, isOminuous = false, tScale = 2.0):
	# func spawnBonusLabel(position, text, animation_speed = 1.0, isTextOnly = false, isOminuous = false, textScale = 1.0, shake = 0, rotationSpeed = 0, doGrow = false):
	var _p = Global.getWindowSize()/Vector2(2.0,1.8)

	# show stats / end game

	Global.GameScene.finishGame();
	Global.GameScene.spawnBonusLabel(_p,txt,aSpeed, false, isOminuous, tScale, 0, 0, false);
	
func label1():
	lbl("You survived\nthe first boss!")
	Global.setTimeout(self,3,self,"label2")

func label2():
	lbl("Nicely done.")
	Global.setTimeout(self,3,self,"label3")

func label3():
	lbl("But that was just\nthe beginning.")
	Global.setTimeout(self,3,self,"label4")

func backToStats():
	Global.GameScene.openStats()

func label4():
	Global.GameScene.isCurrentBossReady = false
	lbl("Get the FULL GAME\nfor more fun!",0.1, false, 2.2)
	Global.setTimeout(self,10,self,"backToStats")

func haveFun():

	Global.GameScene.addTerminalLine("      *   *      ", 5.1)
	Global.GameScene.addTerminalLine("     * * * *     ", 5.2)
	Global.GameScene.addTerminalLine("     *  *  *     ", 5.3)
	Global.GameScene.addTerminalLine("      *   *      ", 5.4)
	Global.GameScene.addTerminalLine("       * *       ", 5.5)
	Global.GameScene.addTerminalLine("        *        ", 5.6)
	Global.GameScene.addTerminalLine("                 ", 5.7)
	Global.GameScene.addTerminalLine(" YOU'RE AWESOME! ", 10)

	if Global.GameScene.player:
		Global.GameScene.PlayerBulletType = Global.PlayerBulletTypes.HOMING_TRIPPLE_SUPER
		Global.GameScene.PlayerSpeed = 400
		Global.GameScene.PlayerMaxBullets = Config.MaxBulletsLimit
		Global.GameScene.player.setAutoFire(true);

		Global.GameScene.playerEffets[Global.PlayerEffect.SUPER_RAPIDFIRE] = 500

		for _i in range(Global.getMaxWingCount()):
			Global.GameScene.player.addWings(Global.PlayerBulletTypes.HOMING_SINGLE);

func initLevel():

	Global.setTimeout(self,3,self,"showTitle")
	Global.setTimeout(self,6,self,"haveFun")

	Global.setTimeout(self,9,self,"label1")

	Global.GameScene.isCurrentBossReady = true

	spawnPoints()

	Global.GameScene.addTerminalLine("      *   *      ", 5.1)
	Global.GameScene.addTerminalLine("     *** ***     ", 5.2)
	Global.GameScene.addTerminalLine("     *******     ", 5.3)
	Global.GameScene.addTerminalLine("      *****      ", 5.4)
	Global.GameScene.addTerminalLine("       ***       ", 5.5)
	Global.GameScene.addTerminalLine("        *        ", 5.6)
	Global.GameScene.addTerminalLine("                 ", 5.7)
	Global.GameScene.addTerminalLine(" -- THANK YOU -- ", 5.8)

	wasInit = true


func getSpecialTargets():
	return _targets;

func isLevelOver():
	# Demo over level is never over
	return false;

func customKillCleanup():
	pass

func canPlayerRespawn():
	return true
