extends "res://scripts/levels/LevelNormal.gd"

var wingedBoss = preload('res://scenes/Enemy_7.tscn')

func doForceEnemyCoordinator():
	return true

func getEnemyConfig():

	if(Global.GameScene.difficulty > Global.GameDifficulty.NORMAL):
		return [
			[7,0,0,10,0,0,7],
			[10,10,0,7,0,10,10],
		]

	return [
		[0,7,0,10,0,7,0],
		[10,10,10,10,10,10,10],
	]

func modifyEnemy(enemyObject):

	if enemyObject.has_method("changeWingHealth"):
		var h = enemyObject.health*4
		enemyObject.health = h+(15*Global.GameScene.difficulty)
		enemyObject.changeWingHealth(h)

func getLevelType():
	return Global.LevelType.BOSS

func getSpecialTargets():
	var _targets = []
	return _targets;

func customKillCleanup():
	pass

func _ready():
	Global.GameScene.isCurrentBossReady = true

func _physics_process(_delta):
	pass
