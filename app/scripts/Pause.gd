extends CanvasLayer

func _ready():
	StoreIntegration.connect("overlay_toggled",self,"_on_overlay_toggled")

	Input.connect("joy_connection_changed",self,"_on_joy_connection_changed")
	# _on_joy_connection_changed(Input.is_joy_known(0))

func _on_overlay_toggled(isOn):
	if isOn && !get_tree().paused:
		pauseOn()

# var _wasJoyConnected = false

func _process(_delta):
	if(Input.is_action_just_pressed("button_back")):
		togglePause()

func _on_joy_connection_changed(_num, isConnected):

	# print("X")
	# print(_num)
	# print(isConnected)

	# # only if the joy was connected and now is not
	# if isConnected:
	# 	_wasJoyConnected = true

	# if _wasJoyConnected && !isConnected && !get_tree().paused:
	if !isConnected && !get_tree().paused:
		pauseOn()

func pauseOff():
	SystemNotificationManager.wasPausedManually = false
	SystemNotificationManager.resume_game()
	self.visible = false
	Global.hideMouse(Global.isMouseControl())

func pauseOn():
	SystemNotificationManager.wasPausedManually = true
	SystemNotificationManager.pause_game()
	self.visible = true
	Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)

	$PauseButton.grab_focus()

func togglePause():
	if get_tree().paused:
		pauseOff()
	else:
		pauseOn()
