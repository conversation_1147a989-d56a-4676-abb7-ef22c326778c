extends Node2D

# var shipSelectorScene = preload("res://scenes/ShipSelector.tscn")

# var shipSelectorInstance = null

# func openShipSelector():
# 	shipSelectorInstance = shipSelectorScene.instance()
# 	self.get_parent().add_child(shipSelectorInstance)

# func _on_select_ship():
# 	openShipSelector()

var terminalContent = []

func addTerminalLine(line,duration = 5):

	terminalContent.push_back([line,duration,Tick.ms()])

	if(terminalContent.size()>10):
		terminalContent.pop_front()

func updateUI(_delta):

	if Global.doThrottle("updateUI",500):
		pass
	
	var _temp = []

	# remove expired
	for i in range(terminalContent.size()):
		var _l = terminalContent[i]
		if((_l[1]*1000+_l[2])>Tick.ms()):
			_temp.push_back(_l)
	
	terminalContent = _temp

	get_node("Overlay/TerminalLabel").text = ""

	var _lcnt = 0

	var _crsr = (int((titleDuration/0.5))%2 == 0)

	for _tl in terminalContent:
		var _tline = _tl[0]
		get_node("Overlay/TerminalLabel").text += _tline
		_lcnt+=1
		if(_lcnt==terminalContent.size()):
			get_node("Overlay/TerminalLabel").text += Global.ifelse(_crsr,".","_")
		else:
			get_node("Overlay/TerminalLabel").text +=  " \n"

func _ready():

	# addTerminalLine("Welcome to Tryü Sora!", 2)
	# addTerminalLine("test_mask", 5)

	# var _start = $TestButton.connect("pressed", self, "_test")

	get_node("Demo").visible = Config.Env.IsDemo

	Global.playMusic($AudioStreamPlayer,"menu")
	Global.dbg("Environment: ", Config.Env.EnvironmentName)

	Global.dbg("?")

	Global.hideMouse(false)

	$Version.text = "v"+Config.version

	if(Config.Env.Badge!=""):
		$Badge.text = "["+Config.Env.Badge+"]"
	else:
		$Badge.text = ""


	# play title mp3
	if(Global.OptionsData.isSoundOn):
		$AudioStreamPlayer_title.play()
	
	setData()

	# set menu size for non touch screens
	if !OS.has_touchscreen_ui_hint():
		$Menu.rect_scale.x = 0.6
		$Menu.rect_scale.y = 0.6
	else:
		$Menu.rect_scale.x = 0.8
		$Menu.rect_scale.y = 0.8
	
	$Menu.grab_focus()

	# connect button
	# var _ship_select = $SelectShipButton.connect("pressed", self, "_on_select_ship")

var titleDuration = 0.0

func _process(delta):

	titleDuration += delta
	updateUI(delta)

	if(!Global.doThrottle("title_screen_refresh", 500)):
		$PlayerDisplay.applySpecs(ShipSpecs.getSpecs())
		setData()

func setData():
	var _highScoreObject = Profile.getData("highScoreData", {})
	var _highScoreFlow = 0
	var _highScoreCampaign = 0

	if(_highScoreObject.has("flow")):
		_highScoreFlow = _highScoreObject["flow"]["score"]

	if(_highScoreObject.has("campaign")):
		_highScoreCampaign = _highScoreObject["campaign"]["score"]

	# set data
	$HighScoreFlow.text = "HiScore Flow: "+str(_highScoreFlow)
	$HighScoreCampaign.text = "HiScore Campaign: "+str(_highScoreCampaign)

	$Money.text = "$"+str(Profile.getData("crystals", 0))

