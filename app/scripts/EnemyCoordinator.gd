extends Node

# class to tell enemies when to attack, when to fire bullets, etc

var EnemyBulletCheckPeriod = Config.EnemyBulletCheckPeriodBase

var EnemyBulletChance_Random = Config.EnemyBulletChance_RandomBase		# x/1 chance to fire bullet
var EnemyBulletChance_OverPlayer = Config.EnemyBulletChance_OverPlayerBase		# x/1 chance to fire bullet
var EnemyBulletChance_Attacking = Config.EnemyBulletChance_AttackingBase		# x/1 chance to fire bullet

var EnemyAttackCheckPeriod = Config.EnemyAttackCheckPeriodBase
var EnemyAttackChance = Config.EnemyAttackChanceBase				# n/1 chance to attack
var EnemyesAttackingAtOnceMax = Config.EnemyesAttackingAtOnceMaxBase 

# monify values
func applyDifficulty():
	# calculate difficulty with a cap
	EnemyBulletCheckPeriod = max(100, Config.EnemyBulletCheckPeriodBase -  (Global.GameScene.getProperDifficulty() * 200))
	EnemyAttackCheckPeriod = max(100, Config.EnemyAttackCheckPeriodBase -  (Global.GameScene.getProperDifficulty() * 200))
	EnemyesAttackingAtOnceMax = min(10, Config.EnemyesAttackingAtOnceMaxBase + (Global.GameScene.getProperDifficulty() -1 ))

	# if mouse game, make enemies a bit more agressive
	if(Global.OptionsData.controlType==Global.GameControlMode.MOUSE):
		EnemyBulletCheckPeriod *= 1.1
		EnemyAttackCheckPeriod *= 1.1
		EnemyesAttackingAtOnceMax += 1
	
	# if easy mode, then make enemies less agressive
	if(Global.GameScene.difficulty == Global.GameDifficulty.EASY):
		EnemyBulletCheckPeriod *= 2
		EnemyAttackCheckPeriod *= 2

func coordinateAttacks():

	if(Global.doThrottle("EnemyCoordinator.Attack", EnemyAttackCheckPeriod)):
		return false

	# max attacking reached?
	if(Global.EnemiesAttacking >= EnemyesAttackingAtOnceMax):
		return false
	
	# is there luck?
	if(!Global.isChance(randi(), EnemyAttackChance)):
		return false
	
	# attack only if all enemies are entered stage ( no enty state enemies )
	if(Global.GameScene.levelConductor.levelInstance.getEnemiesWithMode(Global.EnemyMode.ENTRY).size() > 0):
		return false

	# attack only if all enemies are entered stage ( no enty state enemies )
	if(Global.GameScene.levelConductor.levelInstance.getEnemiesWithMode(Global.EnemyMode.INIT).size() > 0):
		return false

	var enemies = Global.GameScene.levelConductor.levelInstance.getEnemiesWithMode(Global.EnemyMode.IDLE)

	if(enemies.size()<=0):
		return false

	var enemy = enemies[randi() % enemies.size()]
	enemy.startAttack()

func isEnemyTooLow(enemy):
	var screenHeight = Global.getWindowSize().y
	var threshold = screenHeight * 0.70  # 70% of screen height

	Global.dbg("do fire?")
	Global.dbg(enemy.position.y)
	Global.dbg(threshold)

	return enemy.position.y > threshold

# decide who's going to fire bullet
func coordinateBullets():

	if(Global.doThrottle("EnemyCoordinator.Bullet", EnemyBulletCheckPeriod)):
		return false

	var rnd = randi()
	# check if anyone is going to fire bullet

	# select who's going to fire bullet ( if anyone )

	# random enemy
	if(Global.isChance(rnd, EnemyBulletChance_Random)):
		var enemies = Global.GameScene.levelConductor.levelInstance.getAllEnemiesOnLevel()
		if(enemies.size()<=0):
			return false
		var enemy = enemies[randi() % enemies.size()]
		fireBullet(enemy)
		return true

	# enemy over player
	if(Global.isChance(rnd, EnemyBulletChance_OverPlayer)):
		var enemies = Global.GameScene.levelConductor.levelInstance.getEnemiesOverPlayer()
		if(enemies.size()<=0):
			return false
		var enemy = enemies[randi() % enemies.size()]
		fireBullet(enemy)
		return true

	# attacking enemy
	if(Global.isChance(rnd, EnemyBulletChance_Attacking)):
		var enemies = Global.GameScene.levelConductor.levelInstance.getEnemiesWithMode(Global.EnemyMode.ATTACK)
		if(enemies.size()<=0):
			return false
		var enemy = enemies[randi() % enemies.size()]
		fireBullet(enemy)
		return true
	
	return false

func fireBullet(enemy):

	# if it's easy mode and enemy too low
	if(Global.GameScene.difficulty == Global.GameDifficulty.EASY && isEnemyTooLow(enemy)):
		Global.dbg("enemy too low - fire prevented")
		return false

	enemy.fireBullet()
