# Test script to validate BulletPool functionality
# This script can be used to test the pooling system independently

extends Node

# Test the BulletPool system
func test_bullet_pool():
	print("=== BulletPool Test Started ===")
	
	# Create a test pool
	var pool = BulletPool.new()
	var bullet_scene = preload("res://scenes/BulletHoming.tscn")
	
	# Initialize the pool
	pool.initialize(bullet_scene, self, 5, 20)
	
	print("Pool initialized with 5 initial bullets, max 20")
	print("Initial stats: ", pool.get_stats())
	
	# Test getting bullets from pool
	var bullets = []
	for i in range(10):
		var bullet = pool.get_bullet()
		if bullet:
			bullets.append(bullet)
			print("Got bullet ", i, " - Pool stats: ", pool.get_stats())
		else:
			print("Failed to get bullet ", i)
	
	print("Retrieved 10 bullets")
	print("Current stats: ", pool.get_stats())
	
	# Test returning bullets to pool
	for i in range(5):
		if i < bullets.size():
			pool.return_bullet(bullets[i])
			print("Returned bullet ", i, " - Pool stats: ", pool.get_stats())
	
	print("Returned 5 bullets")
	print("Final stats: ", pool.get_stats())
	
	# Test getting bullets again (should reuse returned ones)
	for i in range(3):
		var bullet = pool.get_bullet()
		if bullet:
			print("Reused bullet ", i, " - Pool stats: ", pool.get_stats())
	
	print("=== BulletPool Test Completed ===")

func _ready():
	# Run the test
	call_deferred("test_bullet_pool")
