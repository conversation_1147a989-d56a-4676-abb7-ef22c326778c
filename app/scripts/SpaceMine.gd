extends Node2D

var Explosion = preload("res://scenes/Explosion_1.tscn")
var AreaExplosion = preload("res://scenes/AreaExplosion.tscn")

var Speed = Config.MineSpeed

var velocity = Vector2(0,1)

var _speedMod = 1.0

var isInvincible = false;
var wasHit = false;

func hideAnim():
	var tween = Global.createTween(self)
	tween.interpolate_property(self,"scale",Vector2(1,1),Vector2(0,0),0.5, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.interpolate_property(self,"modulate:a",1,0,0.5, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.start()
	Global.setTimeout(self,0.5,self,"queue_free")


func isSpaceMine():
	return true;

var canKillPlayer = true

func isDeadly():
	return canKillPlayer

func getSpeedMod():
	return self._speedMod

func _ready():
	var _c = connect("area_entered",self,"_on_hit")
	self._speedMod = 1.5 - randf()
	rotation_degrees = (randf()*10)-5
	rotationVelocity = (randf()/2)-0.25
	self.hideParticles()


func showParticles():
	$Particles2D.visible = true;

func hideParticles():
	$Particles2D.visible = false;


func setVincible():
	self.showParticles()
	self.isInvincible = false
	$AnimatedSprite.playing = true
	if(Global.OptionsData.isSoundOn && !disableBeep):
		$AudioStreamPlayer2D.play()

func explosion():

	$Particles2D.visible = false;

	var mine = $AnimatedSprite

	var explosion = Explosion.instance()
	explosion.position = mine.global_position;
	explosion.z_index  = z_index+1;
	explosion.scale = Vector2(2,2)
	Global.GameScene.add_child_deferred(explosion);

	var area_explosion = AreaExplosion.instance()
	area_explosion.position = mine.global_position;
	area_explosion.z_index  = z_index+1;
	area_explosion.isMine = true
	Global.GameScene.add_child_deferred(area_explosion);

	call_deferred("queue_free")

var rotationVelocity = 0;

var disableBeep = false

func _on_hit(target):

	if Global.isOffScreen(self.global_position, -10):
		return 0

	# is player / kill
	if target.has_method("isPlayer"):
		explosion()
		Global.callIfExists(target,"die")
		Achievments.acquire("mined")
		return 0
	
	if target.has_method("isEnemyBullet"):
		explosion()
		Global.callIfExists(target,"destroy")
		return 0

	if isInvincible:
		return 0

	# player hit
	if(target.has_method("getDamagePoint")):

		# destroy bullet
		Global.callIfExists(target,"destroy")

		if not wasHit:
			activateMine()

			# stear mine
			var xdiff = -((target.position.x-self.position.x)/15)
			self.velocity.x = xdiff
			self.velocity.y -= (self.velocity.y*1.5)
			rotationVelocity = xdiff

			Global.playSound(SoundManager.MetalThud, global_position, 0)


		else:
			explosion()
	
		return 0

func activateMine():
	# reverse course, add a little side speed depending where it's hit

	wasHit = true
	isInvincible = true

	var tween = Global.createTween(self)
	tween.interpolate_property(self,"modulate:g",0,1,2, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.interpolate_property(self,"modulate:b",0,1,2, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.interpolate_property(self,"modulate:r",2,1,2, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.start()

	Global.setTimeout(self,1,self,"setVincible")

func destroy():
	explosion();

func _process(delta):

	var _velocity = velocity*Speed; #velocity.normalized() * Speed
	_velocity.x = velocity.x * Speed
	position += _velocity * delta * Vector2(1, getSpeedMod())

	rotation_degrees += rotationVelocity*delta*(Speed/1.5)

	# destroy bullet if it goes off screen
	if position.y > Global.getWindowSize().y+100:
		queue_free()

	if position.y < -100 && wasHit:
		queue_free()

	if position.x > (Global.getWindowSize().x+100):
		queue_free()

	if position.x < -100:
		queue_free()


