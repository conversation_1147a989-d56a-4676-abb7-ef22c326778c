extends Node2D


func _ready():
	# init("1000")
	pass

func clean_up(_void):
	call_deferred("queue_free")

var addShake = 0
var rotationSpeed = 0
var rotationPrefix = 0
var doGrow = false

func init(text, animation_speed = 1.0, isTextOnly = false, isOminuous = false, textScale=1.0):
	$Bonus/Label.text = text;
	var _t = $AnimationPlayer.connect("animation_finished", self, "clean_up");
	$AnimationPlayer.playback_speed = animation_speed;

	var textScaleVector = Vector2(textScale,textScale)

	if(isOminuous):
		$AnimationPlayer.play("LabelAnimOminous");
	else:
		$AnimationPlayer.play("LabelAnim");

	scale = textScaleVector;

	if(!isTextOnly):
		$Bonus/Particles2D.emitting = true;
		Global.playSound(SoundManager.BonusSound,position)
	else:
		$Bonus/Particles2D.emitting = false;
	
	rotationPrefix = -1 if randi()%2==1 else 1
	
var _deltaSum = 0

func _process(delta):

	if(addShake>0):
		_deltaSum+=delta
		if(_deltaSum>0.08):
			self.rotation_degrees = (randi()%(addShake*2)-addShake)
			_deltaSum = 0
	else:
		self.rotation_degrees+=rotationPrefix*rotationSpeed*delta

	if(doGrow):
		self.scale+=Vector2(0.3*delta,0.3*delta)
