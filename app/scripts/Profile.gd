extends Node

func getStatsDefault():
	return {
		"speed": Config.ShipSpeedBase,
		"bullets": Config.MaxBulletsBase,
		"weapon": Global.PlayerBulletTypes.SINGLE,
		"rapidfire": false,
		"lives": Config.ShipLifeBase,
		"wing1": 0,
		"wing2": 0,
		"wing3": 0
	}


var data= {
	"highScoreData": {
		"flow": {
			"score": 0,
			"difficulty": 0,
			"stats": {}
		},
		"campaign": {
			"score": 0,
			"difficulty": 0,
			"stats": {}
		},
		"gameVersion": ""
	},
	"crystals": 0,
	"initialStats": getStatsDefault(),
	"available_ships": 0,
	"selected_ship": 0
}

const KEY_PROFILE_STORAGE = "profile"

func resetShips():
	self.setData("available_ships",0)
	self.setData("selected_ship",0)
	ShipSpecs.selectedIndex = 0
	self.save()

func getNextShipIndex()->int:
	if(!ShipSpecs.wasAllUnlocaked()):
		var _tas = self.getData("available_ships",0)
		_tas+=1
		return _tas
	return -1

func unlockNextShip()->int:

	if(!ShipSpecs.wasAllUnlocaked()):
		var _tas = getNextShipIndex()

		self.setData("available_ships",_tas)
		self.save()

		return _tas

	return -1

func resetStats():
	self.data["initialStats"] = getStatsDefault()
	save()

func spendMoney(amount, validateOnly = false):
	var money = getData("crystals", 0)

	if(money<amount):
		return false

	if(validateOnly):
		return true

	money = money - amount
	setData("crystals", money).save()
	return true

func getStat(key, default):
	if(key in data["initialStats"]):
		return data["initialStats"][key]
	else:
		return default

func setStat(key, value):
	data["initialStats"][key] = value
	return self
	
func incSpeed():
	var speed = getStat("speed", Config.ShipSpeedBase)
	if(speed>=Config.ShipSpeedMax):
		return false
	speed += Config.ShipSpeedIncrement
	setStat("speed", speed).save()
	return true
	
func incBullet():
	var bullet = getStat("bullets", Config.MaxBulletsBase)
	if(bullet>=Config.ShipSpeedBase):
		return false
	bullet += Config.MaxBulletsIncrement
	setStat("bullets", bullet).save()
	return true

func incShield():
	var shields = getStat("shield", 0)
	if(shields>=Config.MaxShields):
		return false
	shields += 1
	setStat("shield", shields).save()
	return true

func incLives():
	var lives = getStat("lives", Config.ShipLifeBase)
	if(lives>=Config.ShipMaxLives):
		return false
	lives += 1
	setStat("lives", lives).save()
	return true

func incWeapon():
	var weapon = getStat("weapon", Global.PlayerBulletTypes.SINGLE)
	if(weapon>=(Global.PlayerBulletTypes.size()-1)):
		return false
	weapon += 1
	setStat("weapon", weapon).save()
	return true

func wingSum():
	var sum = int(getStat("wing1",0)) + int(getStat("wing2",0)) + int(getStat("wing3",0));
	return sum

func _addWing(wingName):

	if(wingSum()>=Global.getMaxWingCount()):
		return false
	var wingCnt = getStat(wingName,0)
	wingCnt+=1
	setStat(wingName, wingCnt).save()
	return true

func addWing1():
	_addWing("wing1")

func addWing2():
	_addWing("wing2")

func addWing3():
	_addWing("wing3")

func setRapidFire():
	var rapidfire = getStat("rapidfire", false)
	if(rapidfire):
		return false
	setStat("rapidfire", true).save()
	return true

func _ready():
	self.load()
	StoreIntegration.loadStatsData()

func setData(key, value):
	data[key] = value
	return self

func getData(key, default):
	if(data[key]):
		return data[key]
	else:
		return default
	
func wasHighScore(score, mode):
	return data.highScoreData[mode].score<score
	
func saveHighScore(score, mode, stats, difficulty, gameVersion):

	if(wasHighScore(score, mode)):

		var _temp = data.highScoreData
		var _mode = mode

		_temp.gameVersion = gameVersion

		_temp[_mode] = {
			"score": score,
			"difficulty": difficulty,
			"stats": stats
		}

		self.setData("highScore", score).save()

		StoreIntegration.saveHighScore(score, _mode)

		return true

	return false

func incSaveCrystal(amount):
	data.crystals = data.crystals+amount
	self.save()

func setSaveCrystal(amount):
	data.crystals = amount
	self.save()

func load():
	self.data = Storage.LoadData(KEY_PROFILE_STORAGE, self.data, true) 

func save():
	Storage.SaveData(KEY_PROFILE_STORAGE, self.data)
	StoreIntegration.saveStats(self.data)
