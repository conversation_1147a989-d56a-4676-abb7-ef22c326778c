extends Node2D

enum DragonDirection {
	LTR,
	RTL
}

var allParts = 0

var isDead = false

func getTargetArray():
	var _arr = [];

	for child in $"Body".get_children():
		if(is_instance_valid(child)):
			if(child.has_method("isDragonPart")):
				if(!child.isDead):
					if(!Global.isOffScreen(child.position,50)):
						_arr.push_back(child)
	return _arr;

func destory():

	Global.setTimeout(self, 2.2, self, "queue_free")

	for child in $"Body".get_children():
		if(child.has_method("isDragonPart")):
			child.destroy()

func _part_died(_object):
	allParts-=1
	if(allParts<=0):
		destory()

var direction = DragonDirection.LTR

var prevOffset = 0

func _process(delta):

	if(direction == DragonDirection.LTR) && get_parent().unit_offset > 0.5:
		setDirection(DragonDirection.RTL)
	elif(direction == DragonDirection.RTL) && get_parent().unit_offset < 0.5:
		direction = DragonDirection.LTR
		setDirection(DragonDirection.LTR)

	get_parent().offset += 100*delta

func setDirection(dir):
	direction = dir
	if(dir == DragonDirection.LTR):
		set_scale(Vector2(1,1))
	else:
		set_scale(Vector2(1,-1))

func _ready():

	#init

	direction = 1

	for child in $"Body".get_children():
		if(child.has_method("isDragonPart")):
			allParts+=1
			child.connect("part_died",self, "_part_died")
