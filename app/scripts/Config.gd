extends Node

# versioning
const version = "0.8.2"

# load environment file

const _env_res = preload("../env/Environment.dev.none.gd")
# const _env_res = preload("../env/Environment.prod.none.gd")

# Steam

# const _env_res = preload("../env/Environment.dev.steam.gd")

# const _env_res = preload("../env/Environment.prod.demo.steam.gd")
# const _env_res = preload("../env/Environment.prod.steam.gd")

# const StartScene = "res://scenes/NewTitleScreen.tscn"
const StartScene = "res://scenes/TitleScreen.tscn"

onready var Env = _env_res.new()

# shop prices

var ShopPrices = {
	"speed": 200,
	"bullet": 200,
	"weapon": 1000,
	"rapidfire": 1500,
	"lives": 5000,
	"wing1": 7000,
	"wing2": 9000,
	"wing3": 10000,
	"shield": 5500
}

# base speed of the ship
const ShipSpeedBase = 200
const ShipSpeedIncrement = 20
const ShipSpeedMax = 600
const ShipSpeedPointsAfterMax = 10000
const ShipLifeBase = 2

# extra live points
const ExtraLifeInterval = 2000000

# accuracy points
const HighAccuracyPoints = 50000
const MaxAccuracyPoints = 500000

# max lives
const ShipMaxLives = 10

# max bullet base
const MaxBulletsBase = 5
const MaxBulletsIncrement = 1
const MaxBulletsLimit = 33
const ShipBulletPointsAfterMax = 10000
const ShipBulletPointsAfterMaxWings = 10000
const ShipBulletPointsAutofire = 10000
const ShipBulletPointsMaxShields = 15000
const ShipFireRate = 125

# wing config
const MaxWingCount = 2
const MaxShields = 4
const EasyStartShields = 1

# timed effect base
const TimedEffectBaseShort = 5
const TimedEffectBase = 15
const TimedEffectBaseMiddle = 30
const TimedEffectBaseLong = 60
const TimedEffectCurseBase = 10
const TimedEffectShield = 2
const TimedEffectShieldRespawn = 3

# enemy aggressiveness base values
const EnemyesAttackingAtOnceMaxBase = 3
const EnemyBulletCheckPeriodBase = 1000     # frequency of bullet check
const EnemyAttackCheckPeriodBase = 1000     # frequency of attack check
const EnemyBulletChance_RandomBase = 15		# x/1 chance to fire bullet
const EnemyBulletChance_OverPlayerBase = 5		# x/1 chance to fire bullet
const EnemyBulletChance_AttackingBase = 3		# x/1 chance to fire bullet
const EnemyAttackChanceBase = 5				# n/1 chance to attack

# powerup chance
const PowerupChanceBase = 25 # 1 in N chance to spawn powerup
const PowerupChanceMin = 3 # 1 in N chance to spawn powerup
const PlayerMaxLuck = 10
const AccuracyBonusLimit = 90

# powerup speed
const PowerupMaxSpeed = 200
const PowerupMinSpeed = 100

# Debris speed
const DebrisMaxSpeed = 300
const DebrisMinSpeed = 100

# how often an enemy can fire bullets
const GlobalEnemyBulletThrottle = 500

# ship X min anc max position
const ShipBoundries = [50,980]

# close kill distance
const CloseKillDistance = 80

# ship starting position
const ShipStartPos = Vector2(512, 510)

# enemy entry animation speed modifier
# smaller is faster (1 normal, 0.1 is the fastest, 2 slower - you get the idea :) )
const EnemyEntrySpeed = 0.7

# delay when entering the screen
const EnemyColDelay = 0.3
const EnemyRowDelay = 3.0
const EnemyInitialDelay = 2
const EnemyInitialDelayBonus = 2
const EnemyRowDelayBonus = 3.5

# enemy attack speed
const EnemyAttackSpeed = 100

# wiggle scale
const WiggleMultiplier = 50.0

# wiggle max
# how much enemies should move left end right
const WiggleMax = 100

# wiggle individual shake
# how much individual enemies should mistake when they wiggle
# 0 nothing - 50 shaking a lot
const WiggleShake = 0

#bullet speed
const BulletSpeed = 400

#bullet speed
const EnemyBulletSpeed = 400

#mine speed
const MineSpeed = 75

#powerup ship setup
const PowerupShipHits = 50
const PowerupShipPoints = 50000
const PowerupShipSpeed = 40

#luck check frequency (ms)
const LuckCheckFrequency = 100

# start Luck Value
const StartLuckValue = 0.80

# chence of enemy bullet to jiggle
const EnemyBulletJiggleChance = 0.1

# top z index
const TopZIndex = 1000

# ui z index
const UIZIndex = 4000

# close kill points
const CloseKillPoints = 30000

# debris
const DebrisPoints = 5000

# that was close
const ThatWasClosePoints = 30000

# clean row points
const CleanRowPoints = 10000

# clean row points
const CleanAllRowPoints = 100000

# music max volume
const MusicMaxVolume = -15
const MusicFadeSeconds = 1

# debug
const CheatModeEnabled = true
const DoProtectStorageFiles = true

# start with level ( default 1 )
const StartFromLevel = 1

# random mine chance
const RandomMineChance = 100

# default wing bullet throttle ms
const DefaultWingBulletThrottle = 200

# laser wing bullet throttle ms
const LaserWingBulletThrottle = 300

# boss damage throttle ms
const BossDamageThrottle = 300
const BossDamageThrottleShort = 100
