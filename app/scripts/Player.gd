extends Area2D

var Explosion = preload("res://scenes/Explosion_1.tscn")
var Wing = preload("res://scenes/PlayerWing.tscn")

var isGod = false

export var mode = -1

signal player_death

func isPlayerInvincible():
	return isGod or Global.GameScene.hasPlayerEffect(Global.PlayerEffect.INVINCIBILITY);

func canGetPowerup():
	return true

func isPlayer():
	return true

func spawnEnd():
	var _c = connect("area_entered",self,"_on_hit")
	changeState(Global.PlayerMode.NORMAL)

	# debug add wings
	# addWings(Global.PlayerBulletTypes.SINGLE)
	# addWings(Global.PlayerBulletTypes.STRONG_SINGLE)
	# addWings(Global.PlayerBulletTypes.HOMING_SINGLE)

func destroyWings(level):

	if(wings.size()<level):
		return false

	var _tempDict = []

	for wing in wings:
		if(wing[Global.PlayerWingType.LEFT].wingPosition>=level):

			wing[Global.PlayerWingType.LEFT].destroy()
			wing[Global.PlayerWingType.RIGHT].destroy()
		else:
			_tempDict.push_back(wing)

	wings = _tempDict

func applySpecs(specIndex=-1):

	if(specIndex>0):
		ShipSpecs.selectedIndex = specIndex

	# set specs
	$parts.applySpecs(ShipSpecs.getSpecs())


func spawn():

	self.applySpecs()

	# restore defaults
	changeState(Global.PlayerMode.INIT)
	position = Config.ShipStartPos + Vector2(0, 200);
	modulate.a = 1
	scale = Vector2(1,1)

	# move to screen
	var tween = Global.createTween(self)
	tween.interpolate_property(self,"position",position,Config.ShipStartPos,1, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.connect("tween_all_completed",self,"spawnEnd")
	tween.start()

	# specs

	if(ShipSpecs.getSpecs().initial_super_rapidfire_duration>0):
		Global.GameScene.playerEffets[Global.PlayerEffect.SUPER_RAPIDFIRE] = ShipSpecs.getSpecs().initial_super_rapidfire_duration

	# add easy game perks

	if(Global.GameScene.difficulty == Global.GameDifficulty.EASY):
		Global.GameScene.shieldCount = max(Config.EasyStartShields, Global.GameScene.shieldCount)

var wings = []

func addWings(bulletType):

	if(wings.size()>=Global.getMaxWingCount()):
		Global.GameScene.addScore(Config.ShipBulletPointsAfterMaxWings)
		Global.GameScene.spawnBonusLabel(global_position-Vector2(0,64) ,"Maxed",1.5,true,false,0.8);
		return false

	var wing_left = Wing.instance()
	var wing_right = Wing.instance()

	wing_left.setType(Global.PlayerWingType.LEFT)
	wing_right.setType(Global.PlayerWingType.RIGHT)

	wing_left.setWingPosition(wings.size())
	wing_right.setWingPosition(wings.size())

	wing_left.setBulletType(bulletType)
	wing_right.setBulletType(bulletType)

	wing_left.z_index = Global.getPlayer().z_index
	wing_right.z_index = Global.getPlayer().z_index

	wings.push_back({
		Global.PlayerWingType.LEFT: wing_left,
		Global.PlayerWingType.RIGHT: wing_right,
	})

	Global.GameScene.add_child(wing_left);
	Global.GameScene.add_child(wing_right);

	wing_left.activate()
	wing_right.activate()


# Called when the node enters the scene tree for the first time.
func _ready():
	self.spawn()
	pass

func changeState(state):
	self.mode = state

func removeFromScene():
	emit_signal("player_death")
	modulate.a = 0
	position = Config.ShipStartPos + Vector2(0, 200);

func displayInvincibility():
	if isPlayerInvincible():
		#$CollisionShape2D.disabled = true
		if(modulate.a!=0.5) && !Config.Env.IsDevelopment:
			modulate.a = 0.5
	else:
		#$CollisionShape2D.disabled = false
		if(modulate.a!=1):
			modulate.a = 1

func isShieldActive():
	return $ShieldEffect.visible

func showShield():
	$ShieldEffect.visible = true

func hideShield():
	$ShieldEffect.visible = false

func toggleGodMode():
	isGod = !isGod

func giveTemporalInvincibility(time):
	Global.GameScene.addPlayerEffect(Global.PlayerEffect.INVINCIBILITY, time)
	showShield()
	Global.setTimeout(self,time,self,"hideShield")

func die():
	if(isPlayerInvincible()):
		return false

	if(self.mode != Global.PlayerMode.NORMAL):
		return false

	# shield?

	if(Global.GameScene.shieldCount>0):
		Global.GameScene.shieldCount-=1

		# activate invincibility
		# @todo play shield animation
		Global.playSound(SoundManager.Shield,global_position, 0);
		giveTemporalInvincibility(Config.TimedEffectShield)

		return false

	if(Global.GameScene.hasPlayerEffect(Global.PlayerEffect.LUCK)):
		Achievments.acquire("unlucky_bastard")

	changeState(Global.PlayerMode.DEAD)

	# only decrease powers if not easy mode
	if(Global.GameScene.difficulty != Global.GameDifficulty.EASY):
		decreasePowers()

	Global.stopMusic(Global.GameScene.get_node("BackgroundMusicPlayer"))

	destroyWings(0)

	var explosion = Explosion.instance()
	explosion.position = position;
	explosion.z_index  = z_index+1;
	explosion.scale = Vector2(2,2)
	Global.GameScene.add_child(explosion);
	Global.GameScene.shakeCamera(0.5)

	var tween = Global.createTween(self)
	tween.interpolate_property(self,"scale",Vector2(1,1),Vector2(0,0),0.8, Tween.TRANS_EXPO, Tween.EASE_OUT)
	tween.interpolate_property(self,"modulate:a",1,0,0.2, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.connect("tween_all_completed",self,"removeFromScene")
	tween.start()

	Global.GameScene.levelConductor.levelInstance.hideAllStuffOnLevel();

func _on_hit(target):

	if(target.has_method("isDeadly")):

		if(!target.isDeadly()):
			return false

		# kill bullet
		if(target.has_method("destroy")):
			target.destroy()

		self.die()

func inputActionPressed(inputName):

	if mode != Global.PlayerMode.NORMAL:
		return false

	return Input.is_action_pressed(inputName)

func inputActionJustPressed(inputName):

	if mode != Global.PlayerMode.NORMAL:
		return false

	return Input.is_action_just_pressed(inputName)

func incSpeed():
	if Global.GameScene.PlayerSpeed < Config.ShipSpeedMax:
		Global.GameScene.PlayerSpeed += Config.ShipSpeedIncrement
	else:
		Global.GameScene.addScore(Config.ShipSpeedPointsAfterMax)
		Global.GameScene.spawnBonusLabel(global_position-Vector2(0,64) ,"Maxed",1.5,true,false,0.8);

func decSpeed():
	if Global.GameScene.PlayerSpeed > Config.ShipSpeedBase:
		Global.GameScene.PlayerSpeed -= Config.ShipSpeedIncrement

func incBullets():
	if Global.GameScene.PlayerMaxBullets < Config.MaxBulletsLimit:
		Global.GameScene.PlayerMaxBullets += Config.MaxBulletsIncrement
	else:
		Global.GameScene.spawnBonusLabel(global_position-Vector2(0,64) ,"Maxed",1.5,true,false,0.8);
		Global.GameScene.addScore(Config.ShipBulletPointsAfterMax)

func decBullets():
	if Global.GameScene.PlayerMaxBullets > Config.MaxBulletsBase:
		Global.GameScene.PlayerMaxBullets -= Config.MaxBulletsIncrement

func setAutoFire(isActive = true):

	if(Global.GameScene.PlayerAutoFire && isActive):
		Global.GameScene.addScore(Config.ShipBulletPointsAutofire)
		return false

	Global.GameScene.PlayerAutoFire = isActive

# function to decrease powerups when player dies
func decreasePowers(doRemoveEffects = true):

	# don't downgrade on easy mode

	if(ShipSpecs.getSpecs().downgrade_after_death):
		decSpeed()
		decBullets()
		setAutoFire(false)
		downgradeWeapon()

	if(doRemoveEffects):
		removeAllEffects()

func removeAllEffects():
	Global.GameScene.removeAllEffects()


func downgradeWeapon():
	if Global.GameScene.PlayerBulletType > 0:
		Global.GameScene.PlayerBulletType -= 1

func _process(delta):

	var wasMovement = false
	var scalex = 1
	var shaderDirection = 0;
	var velocity = Vector2()

	if(Global.GameScene.levelConductor.getAccuracy()>Config.AccuracyBonusLimit):
		$AccuracyIndicator.visible = true
	else:
		$AccuracyIndicator.visible = false

	if(Global.OptionsData.controlType!=Global.GameControlMode.MOUSE):

		if(Global.OptionsData.controlType==Global.GameControlMode.KEYS):

			if inputActionPressed("button_right"):
				wasMovement = true
				velocity.x += 1

			if inputActionPressed("button_left"):
				wasMovement = true
				velocity.x -= 1
		else:

			if(abs(get_global_mouse_position().x-global_position.x)>3):

				if(get_global_mouse_position().x>global_position.x):
					wasMovement = true
					velocity.x += 1
				elif (get_global_mouse_position().x<global_position.x):
					wasMovement = true
					velocity.x -= 1

		if wasMovement:
			Global.GameScene.didPlayerMoveAtAll = true
			shaderDirection = -velocity.x
			scalex = 0.9

		if(Global.GameScene.hasPlayerEffect(Global.PlayerEffect.SLOTH_MODE)):
			velocity = velocity.normalized() * (Global.GameScene.PlayerSpeed / 2)
		else:
			velocity = velocity.normalized() * Global.GameScene.PlayerSpeed

		velocity *= ShipSpecs.getSpecs().speed_multiplier

		position += velocity * delta

		if !wasMovement:
			shaderDirection = 0
			scalex = 1

		scale.x = scalex

		# @todo add shader
		# var _spr = self.get_node("AnimatedSprite")
		# if(_spr):
		# 	_spr.material.set_shader_param("direction", shaderDirection)

		var _spr = self.get_node("parts")
		if(_spr):
			_spr.setShader(shaderDirection)

	else:
		if mode == Global.PlayerMode.NORMAL:
			# global_position.x = get_global_mouse_position().x

			var pdiff = abs(global_position.x - get_global_mouse_position().x)
			var nudge = pdiff/2 #*delta*10

			if(global_position.x<get_global_mouse_position().x):
				global_position.x+=min(nudge,pdiff)

			if(global_position.x>get_global_mouse_position().x):
				global_position.x-=min(nudge,pdiff)


			# this does not work if we have low framerate
			# var _xdiff = get_global_mouse_position().x-global_position.x
			# global_position.x += (_xdiff/2)*max(delta*50,0.5)

	if(position.x < Config.ShipBoundries[0]):
		wasMovement = false
		global_position.x = Config.ShipBoundries[0]

	if(position.x > Config.ShipBoundries[1]):
		wasMovement = false
		global_position.x = Config.ShipBoundries[1]

	if isFireButtonActive():
		Global.GameScene.didPlayerMoveAtAll = true
		Global.GameScene.spawnBullets(Global.GameScene.PlayerBulletType, position)

	# set indicator
	var mod = float(Global.GameScene.bulletsOnScreenPercentage())/100.0;
	$BulletIndicator.scale.y = mod
	$Overheat.modulate.a = mod

func hasAutofire():
	return true
	# return Global.GameScene.PlayerAutoFire || Global.GameScene.isMode(Global.GameMode.FLOW)

func isFireButtonActive():

	if Global.GameScene.hasPlayerEffect(Global.PlayerEffect.WEAPON_DISABLED):
		return false

	if !Global.GameScene.levelConductor.didLevelStart() && Global.OptionsData["gameMode"]!=Global.GameMode.FLOW:
		return false

	if Global.GameScene.levelConductor.getCurrent().getLevelType() == Global.LevelType.BOSS && !Global.GameScene.isCurrentBossReady:
		return false

	var _doFire = false

	var fireButtonJustPressed = false
	var fireButtonPressed = false

	if(Global.OptionsData.controlType == Global.GameControlMode.KEYS):
		fireButtonJustPressed = inputActionJustPressed("button_fire")
		fireButtonPressed = inputActionPressed("button_fire")
	else:
		fireButtonJustPressed = inputActionJustPressed("button_fire") || inputActionJustPressed("mouse_fire_button")
		fireButtonPressed = inputActionPressed("button_fire") || inputActionPressed("mouse_fire_button")

	if fireButtonJustPressed:
		_doFire = true

	var rapidInterval = 50 if Global.GameScene.hasPlayerEffect(Global.PlayerEffect.SUPER_RAPIDFIRE) else getFireRate()

	if fireButtonJustPressed:
		# reset interval if we just pressed the button
		Global.doThrottle("Player.FireTimer",rapidInterval, false, true)

	if (hasAutofire() || Global.GameScene.hasPlayerEffect(Global.PlayerEffect.SUPER_RAPIDFIRE)) && fireButtonPressed:
		if !Global.doThrottle("Player.FireTimer",rapidInterval):
			_doFire = true

	return _doFire

func getFireRate():
	var _res = Global.getBulletAutofireConf(Global.GameScene.PlayerBulletType) / Global.GameScene.getPlayerMaxBullets()
	return max(_res, Config.ShipFireRate)
