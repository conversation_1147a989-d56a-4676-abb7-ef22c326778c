# Simple validation script to test if our pool classes work
extends Node

func _ready():
	print("=== Pool Validation Test ===")
	
	# Test if we can create the classes
	var object_pool = ObjectPool.new()
	print("ObjectPool created successfully: ", object_pool != null)
	
	var bullet_pool = BulletPool.new()
	print("BulletPool created successfully: ", bullet_pool != null)
	
	# Test basic functionality
	if bullet_pool:
		print("BulletPool methods available:")
		print("- has get_bullet: ", bullet_pool.has_method("get_bullet"))
		print("- has return_bullet: ", bullet_pool.has_method("return_bullet"))
		print("- has initialize: ", bullet_pool.has_method("initialize"))
	
	print("=== Pool Validation Complete ===")
