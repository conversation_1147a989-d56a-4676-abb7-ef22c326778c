extends Control

var ach = preload("res://scenes/AchievmentScreen.tscn")
var shop = preload("res://scenes/Shop.tscn")
var info = preload("res://scenes/Info.tscn")
var shipSelectorScene = preload("res://scenes/ShipSelector.tscn")

var shipSelectorInstance = null

func openStore():
	StoreIntegration.openStore()

func openLeaderboard():
	OS.shell_open(Config.Env.LeaderboardUrl)

func _ready():

	get_node("MainCont/Buy").visible = Config.Env.IsDemo

	# start menu

	var _start = $MainCont/Start.connect("pressed", self, "_showDifficulty")
	var _selectShip = $MainCont/SelectShip.connect("pressed", self, "openShipSelector")
	var _mode = $MainCont/GameMode.connect("pressed", self, "toggleMode")
	var _buy = $MainCont/Buy.connect("pressed", self, "openStore")
	var _leaderBoard = $MainCont/Leaderboard.connect("pressed", self, "openLeaderboard")

	var _d0 = $MainCont/Start/StartCont/Easy.connect("pressed", self, "startGame", [Global.GameDifficulty.EASY])
	var _d1 = $MainCont/Start/StartCont/Normal.connect("pressed", self, "startGame", [Global.GameDifficulty.NORMAL])
	var _d2 = $MainCont/Start/StartCont/Hard.connect("pressed", self, "startGame", [Global.GameDifficulty.HARD])
	var _d3 = $MainCont/Start/StartCont/Extreme.connect("pressed", self, "startGame", [Global.GameDifficulty.EXTREME])

	# options menu

	var _opt = $MainCont/Options.connect("pressed", self, "_showOptions")

	var _o0 = $MainCont/Options/OptionsCont/Music.connect("pressed", self, "toggleMusic")
	var _o1 = $MainCont/Options/OptionsCont/Sound.connect("pressed", self, "toggleSound")
	var _o2 = $MainCont/Options/OptionsCont/Fullscreen.connect("pressed", self, "toggleFullscreen")
	var _o3 = $MainCont/Options/OptionsCont/ControlType.connect("pressed", self, "toggleContrtol")
	
	# exit menu

	var _ext = $MainCont/Exit.connect("pressed", self, "_showExit")

	var _e0 = $MainCont/Exit/ExitCont/Ok.connect("pressed", self, "exit", [true])
	var _e1 = $MainCont/Exit/ExitCont/Cancel.connect("pressed", self, "exit", [false])

	# if web, don't show exit button

	if(OS.get_name() == "HTML5"):
		$MainCont/Exit.visible = false
	
	# no mouse for mobile for now

	if(OS.has_touchscreen_ui_hint()):
		# no mouse control
		$MainCont/Options/OptionsCont/ControlType.visible = false
		Global.OptionsData.controlType = Global.GameControlMode.KEYS
		Global.saveOptions()

		# hide full screen option
		$MainCont/Options/OptionsCont/Fullscreen.visible = false

	#misc

	var _ac = $MainCont/Achievments.connect("pressed", self, "openAchievments")
	var _sh = $MainCont/Shop.connect("pressed", self, "openShop")
	var _in = $MainCont/Info.connect("pressed", self, "openInfo")

	# refresh
	setMenuTexts()
	setFocus()

func _focusButton():
	if(is_instance_valid(lastShown)):
		lastShown.grab_focus()
	else:
		$MainCont/Start.grab_focus()

func setFocus():
	# grab focus
	if(!was_focused):
		was_focused = true
		_focusButton()


var _openedInstance = null;

var was_focused = false

func _process(_delta):

	if(is_instance_valid(_openedInstance)):
		was_focused = false
		(self as Control).visible = false
	else:
		setFocus()
		(self as Control).visible = true

func _input(event):
	if event.is_action_pressed("button_back"):
		closeAll()

func openShipSelector():
	_openedInstance = shipSelectorScene.instance()
	lastShown = $MainCont/SelectShip
	self.get_parent().add_child(_openedInstance)

func openAchievments():
	_openedInstance = ach.instance()
	lastShown = $MainCont/Achievments
	self.get_parent().add_child(_openedInstance)

func openInfo():
	_openedInstance = info.instance()
	lastShown = $MainCont/Info
	self.get_parent().add_child(_openedInstance)

func openShop():
	_openedInstance = shop.instance()
	lastShown = $MainCont/Shop
	self.get_parent().add_child(_openedInstance)

func exit(_ok):
	if _ok:
		get_tree().quit()
	else:
		$MainCont/Exit/ExitCont.visible = false
		$MainCont/Exit.grab_focus()

func setMenuTexts():
	$MainCont/Options/OptionsCont/Music.text = "Music " + Global.ifelse(Global.OptionsData.isMusicOn, "ON", "OFF")
	$MainCont/Options/OptionsCont/Sound.text = "Sound " + Global.ifelse(Global.OptionsData.isSoundOn, "ON", "OFF")
	$MainCont/Options/OptionsCont/Fullscreen.text = "Fullscreen " + Global.ifelse(Global.OptionsData.isFullscreenOn, "ON", "OFF")

	var controlStringArray = [
		"KEYS",
		"MOUSE",
		"MOUSE FOLLOW"
	]

	var gameModeArray = [
		"CAMPAIGN",
		"FLOW"
	]

	var _txt = controlStringArray[Global.OptionsData.controlType]

	if _txt == "KEYS":
		_txt = "KEYS / JOY"


	$MainCont/Options/OptionsCont/ControlType.text = _txt
	$MainCont/GameMode.text = gameModeArray[Global.OptionsData.gameMode]

func toggleMusic():
	Global.OptionsData.isMusicOn = !Global.OptionsData.isMusicOn
	Global.saveOptions(true)
	setMenuTexts()

func toggleSound():
	Global.OptionsData.isSoundOn = !Global.OptionsData.isSoundOn
	Global.saveOptions()
	if(Global.OptionsData.isSoundOn):
		Global.playSound(SoundManager.Clunk,Global.getWindowSize()/Vector2(2,2),-5)
	setMenuTexts()

func toggleMode():
	var current = Global.OptionsData.gameMode

	current+=1

	if(current>=Global.GameMode.size()):
		current=0
	
	Global.OptionsData.gameMode = current

	Global.saveOptions()
	setMenuTexts()

func toggleContrtol():

	var current = Global.OptionsData.controlType
	current+=1
	if(current>=Global.GameControlMode.size()):
		current=0
	
	Global.OptionsData.controlType = current

	Global.saveOptions()
	setMenuTexts()

func toggleFullscreen():
	Global.OptionsData.isFullscreenOn = !Global.OptionsData.isFullscreenOn
	Global.saveOptions()
	setMenuTexts()

func closeAll():
	$MainCont/Start/StartCont.visible = false
	$MainCont/Options/OptionsCont.visible = false
	$MainCont/Exit/ExitCont.visible = false
	_focusButton()

var lastShown = null

func _showExit():
	lastShown = $MainCont/Exit

	$MainCont/Start/StartCont.visible = false
	$MainCont/Options/OptionsCont.visible = false
	$MainCont/Exit/ExitCont.visible = !$MainCont/Exit/ExitCont.visible
	if $MainCont/Exit/ExitCont.visible:
		$MainCont/Exit/ExitCont/Cancel.grab_focus()
	else:
		$MainCont/Exit.grab_focus()

func _showDifficulty():
	lastShown = $MainCont/Start

	$MainCont/Options/OptionsCont.visible = false
	$MainCont/Exit/ExitCont.visible = false
	$MainCont/Start/StartCont.visible = !$MainCont/Start/StartCont.visible
	if $MainCont/Start/StartCont.visible:
		$MainCont/Start/StartCont/Normal.grab_focus()
	else:
		$MainCont/Start.grab_focus()

func _showOptions():
	lastShown = $MainCont/Options

	$MainCont/Start/StartCont.visible = false
	$MainCont/Exit/ExitCont.visible = false
	$MainCont/Options/OptionsCont.visible = !$MainCont/Options/OptionsCont.visible
	if $MainCont/Options/OptionsCont.visible:
		$MainCont/Options/OptionsCont/Music.grab_focus()
	else:
		$MainCont/Options.grab_focus()

func startGame(diff):
	var _start = get_tree().change_scene("res://scenes/Main.tscn")
	Global.SelectedDifficulty = diff
