extends "res://scripts/EnemyBase.gd"

func getCenterPosition():
	return global_position+Vector2(0,25)

func _ready():
	Bullet = preload("res://scenes/EnemyBullet_5.tscn")
	health = 100
	ExplosionColor = Color(1,1,0.2)
	pointValue = 4000
	species = "Ghost"

func fireBullet(doForce = false):

	if(!doForce):
		if (Global.doThrottle(getId("",".Bullet") ,Config.GlobalEnemyBulletThrottle) || !Global.isEnemyActive(mode)):
			return false

	# don't shoot if too close to bottom
	if Global.isOffScreenBottom(getCenterPosition(), -200):
		return false

	var bullet = Bullet.instance()
	bullet.position = getCenterPosition();
	bullet.z_index  = z_index-1;
	Global.GameScene.add_child(bullet);

	var bullet2 = Bullet.instance()
	bullet2.trigType = "cos"
	bullet2.position = getCenterPosition();
	bullet2.z_index  = z_index-1;
	Global.GameScene.add_child(bullet2);

	Global.EnemyBulletsOnScreen += 1
