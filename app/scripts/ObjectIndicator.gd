extends Node2D

func _ready():
	pass

func setType(type):

	$AnimatedSprite.frame = type

	if(type == 3):
		$AnimatedSprite.playing = true
	else:
		$AnimatedSprite.playing = false

func setSize(size):
	$AnimatedSprite.scale.y = 1.0
	$AnimatedSprite.scale.x = (size*0.5)+1.0
	z_index = Config.TopZIndex+10

func removeMe():
	call_deferred("queue_free")

func init(size, type, text, lifeTime):
	setSize(size)
	setType(type)
	$Label.text = text
	Global.setTimeout(self,lifeTime, self, "removeMe")

