extends Node

# object to store and pass data globally temporally

# key list

const SPIDER_LEG_COUNT = "spider_leg_count"
const SPIDER_BOSS_INSTANCE = "spider_boss_instance"

var data = {}

func setValue(key, value):
    data[key] = value

func getValue(key, defaultValue = null):
    if(data.has(key)):
        return data[key]
    else:
        return defaultValue

func clearAll():
    data = {}

func clearKey(key):
    if(data.has(key)):
        data.erase(key)