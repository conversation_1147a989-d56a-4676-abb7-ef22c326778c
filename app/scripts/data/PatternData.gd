extends Node

func getData():
    return {
    "normal": [
      {"note":"# from mid-left to mid-right back to center","duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,3],[0,3],[1,3],[2,3],[3,3],[4,3],[5,3],[6,3],[7,3],[8,3],[9,3],[9,2],[8,2],[7,2],[6,2],[5,2],[4,2]]},
      {"note":"# from top-left in a spiral across the scrren ends in center","duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,-1],[0,0],[1,1],[1,2],[2,3],[2,4],[3,4],[4,4],[5,4],[6,4],[7,4],[8,4],[8,3],[9,2],[8,1],[7,1],[6,1],[5,2]]},
      {"note":"# sideways 6 shape from rop left ending in center","duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,1],[0,1],[1,1],[2,1],[2,2],[3,2],[3,3],[4,3],[4,4],[5,4],[5,5],[6,5],[7,5],[8,5],[9,5],[9,4],[9,3],[9,2],[9,1],[8,1],[7,1],[6,1],[6,2],[5,2],[5,3],[4,3],[4,4]]},
      {"note":"# entry from bottom left ends in top right curved line","duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,5],[0,5],[0,4],[1,4],[1,3],[2,3],[3,3],[3,2],[4,2],[5,2],[6,2],[6,1],[7,1],[8,1],[9,1]]},
      {"note":"# entry from bottom left ends in top right straight line","duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,5],[0,5],[1,4],[2,4],[3,3],[4,3],[5,2],[6,2],[7,1],[8,1],[9,1]]},
      {"note":"# entry from top right, spiral to center","duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,1],[0,1],[1,1],[2,2],[3,2],[3,3],[3,4],[4,5],[5,5],[6,4],[6,3],[5,2],[4,3],[5,3]]},
      {"note":"# entry from left, straight to cetner","duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,3],[0,3],[1,3],[2,3],[3,3],[3,3],[4,3],[5,3]]},
      {"note":"# entry top left, to bottom right curved","duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,1],[0,1],[1,1],[1,2],[2,2],[2,3],[3,3],[4,3],[4,4],[5,4],[6,4],[7,4],[7,5],[8,5],[9,5],[9,4]]},
      {"note":"# entry from top left to bottom right straight","duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,1],[0,1],[1,2],[2,2],[3,3],[4,3],[5,4],[6,4],[7,5],[8,5],[9,5]]},
      {"note":"# entry from left center make a spiral to top, then right then bottom , ends in center","duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,3],[0,3],[1,3],[1,2],[2,2],[2,1],[3,1],[3,0],[4,0],[5,0],[6,0],[6,1],[7,1],[7,2],[8,2],[8,3],[8,4],[7,4],[7,5],[6,5],[5,5],[4,5],[3,5],[3,4],[3,3],[4,3],[5,3]]},

      {"duration":3,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[5,-1],[5,1]]},
      {"duration":3,"sharpness":0,"offsetx":0,"offsety":1,"mirrorx":0,"mirrory":0,"points":[[-1,1],[1,1]]},
      {"duration":3,"sharpness":0,"offsetx":0,"offsety":2,"mirrorx":0,"mirrory":0,"points":[[-1,1],[1,1]]},
      {"duration":3,"sharpness":0,"offsetx":0,"offsety":3,"mirrorx":0,"mirrory":0,"points":[[-1,1],[1,1]]},
      
      {"duration":3,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[5,-1],[5,0]]},
      {"duration":3,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[6,-1],[6,1]]},
      {"duration":3,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[7,-1],[7,2]]},
      {"duration":3,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[8,-1],[8,3]]},
      {"duration":3,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[9,-1],[9,4]]}
    ],
      "bonus": [
      {"note":"# U shape on left side of screen ( top entry top exit)","duration":4,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[1,-1],[1,0],[1,1],[1,2],[2,3],[2,4],[3,5],[4,4],[4,3],[5,2],[5,1],[5,0],[5,-1]]},
      {"note":"# U shape on the whole screen symmetric ( top entry top exit ","duration":4,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[1,-1],[1,0],[1,1],[1,2],[2,3],[2,4],[3,5],[4,4],[5,5],[6,4],[7,5],[8,4],[8,3],[9,2],[9,1],[9,0],[9,-1]]},
      {"note":"# bottom left entry top right exit","duration":4,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,5],[0,5],[1,5],[1,4],[2,4],[3,4],[3,3],[4,3],[5,3],[6,3],[6,2],[7,2],[7,1],[8,1],[9,1],[9,0],[10,0],[10,-1]]},
      {"note":"# another U shape whole screen","duration":4,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,-1],[0,0],[0,1],[1,1],[1,2],[2,2],[2,3],[3,3],[3,4],[4,4],[4,5],[5,5],[6,5],[6,4],[7,4],[7,3],[8,3],[8,2],[9,2],[9,1],[10,1],[10,0],[11,-1]]},
      {"note":"# top left entry , bottom right exit","duration":4,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,-1],[0,0],[0,1],[1,1],[1,2],[2,2],[3,2],[3,3],[4,3],[5,3],[6,3],[6,4],[7,4],[8,4],[8,5],[9,5],[10,5],[11,6]]},
      {"note":"# A shape entry on left exit or gith","duration":4,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,6],[0,5],[1,4],[2,3],[3,2],[4,1],[5,1],[6,1],[7,2],[8,3],[9,4],[10,5],[11,6]]},
      {"note":"# N shape entry on left right, exit to right","duration":4,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,6],[0,5],[1,4],[1,3],[2,3],[2,2],[2,1],[2,0],[3,1],[4,1],[4,2],[4,3],[5,3],[5,4],[6,5],[7,5],[7,4],[8,4],[8,3],[8,2],[9,2],[9,1],[9,0],[10,-1]]},
      {"note":"# W shape, entry on left top, exit on right top","duration":4,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[1,-1],[1,0],[1,1],[1,2],[1,3],[1,4],[2,5],[3,5],[4,4],[4,3],[4,2],[4,1],[5,0],[6,1],[6,2],[6,3],[6,4],[7,5],[8,5],[9,4],[9,3],[9,2],[9,1],[9,0],[9,-1]]},
      {"note":"# Z shape entry on left top, exit on bottom right","duration":4,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,1],[0,1],[1,1],[2,1],[3,1],[4,1],[5,1],[6,1],[7,1],[8,1],[9,1],[9,2],[8,3],[7,3],[6,3],[5,3],[4,3],[3,3],[2,3],[1,4],[2,4],[3,5],[4,4],[5,5],[6,4],[7,5],[8,4],[9,5],[10,5],[11,5]]},
      {"note":"# left entry , comes back, exists on right","duration":4,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,3],[0,3],[1,3],[2,3],[3,3],[4,3],[5,3],[6,3],[7,3],[8,3],[9,3],[9,2],[8,2],[7,2],[6,2],[5,2],[4,2],[3,2],[2,2],[1,2],[1,3],[2,3],[3,3],[4,3],[5,3],[6,3],[7,3],[8,3],[9,3],[10,3],[11,3]]},
      {"duration":7,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,-1],[0,-1],[1,0],[2,1],[2,2],[2,3],[2,3],[2,4],[2,4],[2,4],[1,4],[1,4],[1,4],[0,4],[0,4],[0,4],[0,3],[0,3],[0,2],[1,1],[2,0],[3,1],[4,2],[4,3],[4,3],[4,4],[4,4],[4,4],[3,4],[3,4],[3,4],[2,4],[2,4],[2,4],[2,3],[2,3],[2,2],[3,1],[4,0],[5,-1],[6,0],[7,1],[8,2],[9,3],[9,3],[10,4],[10,4],[10,4],[10,5],[10,5],[10,5],[9,5],[9,5],[9,5],[8,5],[8,5],[8,5],[8,4],[8,4],[8,3],[8,2],[9,1],[9,1],[9,1],[10,0],[10,0],[11,-1]]},
      {"duration":7,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,-1],[0,0],[1,1],[2,2],[3,3],[3,4],[3,5],[2,5],[1,5],[0,5],[-1,4],[-1,3],[0,2],[1,1],[2,0],[3,-1],[4,0],[5,1],[6,2],[7,3],[7,4],[7,5],[6,5],[5,5],[4,5],[3,4],[3,3],[4,2],[5,1],[6,0],[7,-1],[8,0],[9,1],[10,2],[10,3],[10,4],[9,5],[8,5],[7,4],[6,3],[6,2],[7,1],[8,1],[9,1],[9,2],[8,3],[8,3],[8,3],[8,3],[8,3],[7,3],[7,3],[7,3],[7,3],[7,2],[7,2],[7,2],[8,1],[8,1],[9,1],[10,0],[11,-1]]},
      {"duration":7,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,-1],[0,0],[1,1],[2,2],[2,3],[2,4],[2,5],[1,5],[0,5],[0,4],[0,3],[0,2],[0,1],[1,0],[2,0],[3,1],[3,2],[3,3],[3,4],[3,5],[2,5],[1,5],[1,4],[1,3],[1,2],[1,1],[2,0],[3,0],[4,1],[4,2],[4,3],[4,4],[4,5],[3,5],[2,5],[2,4],[2,3],[2,2],[3,1],[4,0],[5,0],[6,1],[7,2],[7,3],[7,4],[7,5],[6,5],[5,5],[5,4],[5,3],[6,2],[7,1],[8,0],[9,0],[9,1],[9,2],[8,3],[7,4],[6,4],[6,3],[7,2],[8,2],[9,1],[10,0],[11,-1]]},
      {"note":"# left to right, vertical spiral","duration":7,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,-1],[0,0],[1,1],[2,2],[2,3],[2,4],[1,5],[1,5],[0,4],[0,3],[1,2],[2,1],[3,2],[3,3],[3,4],[2,5],[2,5],[1,4],[1,3],[2,2],[3,1],[4,2],[4,3],[4,4],[3,5],[3,5],[2,4],[2,3],[3,2],[4,1],[5,2],[5,3],[5,4],[4,5],[4,5],[3,4],[3,3],[4,2],[5,1],[6,2],[6,3],[6,4],[5,5],[5,5],[4,4],[4,3],[5,2],[6,1],[7,2],[7,3],[7,4],[6,5],[6,5],[5,4],[5,3],[6,2],[7,1],[8,2],[8,3],[8,4],[7,5],[7,5],[6,4],[6,3],[7,2],[8,1],[9,2],[9,3],[9,4],[8,5],[8,5],[7,4],[7,3],[8,2],[9,1],[10,0],[11,-1]]},
      {"duration":7,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,1],[0,1],[1,1],[2,1],[3,1],[3,1],[4,1],[4,1],[4,1],[5,1],[5,1],[5,1],[6,1],[6,1],[6,1],[7,1],[7,1],[8,1],[9,1],[10,1],[11,2],[10,3],[9,3],[8,3],[7,3],[7,3],[6,3],[6,3],[6,3],[5,3],[5,3],[5,3],[4,3],[4,3],[4,3],[3,3],[3,3],[2,3],[1,3],[1,4],[2,4],[3,4],[4,4],[5,4],[6,4],[7,4],[8,4],[9,4],[9,3],[9,2],[9,1],[11,-1]]},
      {"duration":7,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,2],[0,2],[1,2],[2,2],[2,2],[3,2],[3,2],[4,2],[4,2],[4,2],[5,2],[5,2],[5,2],[6,2],[6,2],[7,2],[7,2],[8,2],[9,1],[10,0],[11,-1],[11,-1],[11,-1],[11,-1],[11,-1],[11,0],[11,1],[10,2],[9,3],[8,4],[7,4],[7,4],[6,4],[6,4],[5,4],[5,4],[5,4],[4,4],[4,4],[4,4],[3,4],[3,4],[2,4],[2,4],[2,3],[2,2],[1,1],[-1,-1]]},

      {"duration":7,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,1],[0,1],[1,1],[2,1],[3,1],[4,1],[5,1],[6,1],[7,1],[8,1],[9,1],[10,1],[11,1],[11,2],[11,3],[10,3],[9,3],[8,3],[7,3],[6,3],[5,3],[4,3],[3,3],[2,3],[1,3],[0,3],[-1,3]]},
      {"duration":7,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[11,4],[10,4],[9,4],[8,4],[7,4],[6,4],[5,4],[4,4],[3,4],[2,4],[1,4],[0,4],[-1,4],[-1,3],[-1,2],[0,2],[1,2],[2,2],[3,2],[4,2],[5,2],[6,2],[7,2],[8,2],[9,2],[10,2],[11,2]]},

      {"duration":7,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,1],[0,1],[1,1],[1,2],[2,2],[3,2],[3,3],[4,3],[5,3],[6,3],[6,4],[7,4],[8,4],[8,5],[9,5],[10,5],[11,5]]},

      {"duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,0],[11,2]]},
      {"duration":5,"sharpness":0,"offsetx":0,"offsety":1,"mirrorx":0,"mirrory":0,"points":[[-1,0],[11,2]]},
      {"duration":5,"sharpness":0,"offsetx":0,"offsety":2,"mirrorx":0,"mirrory":0,"points":[[-1,0],[11,2]]},
      {"duration":5,"sharpness":0,"offsetx":0,"offsety":3,"mirrorx":0,"mirrory":0,"points":[[-1,0],[11,2]]}
    ],
      "_shmup": [
    ],
      "shmup": [
        {"duration":8,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,0],[0,0],[1,1],[1,2],[1,3],[1,3],[1,4],[1,4],[1,4],[2,5],[3,4],[3,3],[3,3],[3,3],[3,2],[4,1],[4,1],[5,2],[5,3],[5,4],[5,4],[6,5],[7,4],[7,4],[7,3],[7,2],[8,1],[8,1],[8,1],[9,2],[9,3],[9,3],[9,4],[10,5],[11,5],[11,5]]},
        {"duration":8,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":1,"mirrory":0,"points":[[-1,0],[0,0],[1,1],[1,2],[1,3],[1,3],[1,4],[1,4],[1,4],[2,5],[3,4],[3,3],[3,3],[3,3],[3,2],[4,1],[4,1],[5,2],[5,3],[5,4],[5,4],[6,5],[7,4],[7,4],[7,3],[7,2],[8,1],[8,1],[8,1],[9,2],[9,3],[9,3],[9,4],[10,5],[11,5],[11,5]]},
        {"duration":8,"sharpness":0,"offsetx":0,"offsety":1,"mirrorx":0,"mirrory":1,"points":[[-1,0],[0,0],[1,1],[1,2],[1,3],[1,3],[1,4],[1,4],[1,4],[2,5],[3,4],[3,3],[3,3],[3,3],[3,2],[4,1],[4,1],[5,2],[5,3],[5,4],[5,4],[6,5],[7,4],[7,4],[7,3],[7,2],[8,1],[8,1],[8,1],[9,2],[9,3],[9,3],[9,4],[10,5],[11,5],[11,5]]},
        {"duration":8,"sharpness":0,"offsetx":0,"offsety":1,"mirrorx":1,"mirrory":1,"points":[[-1,0],[0,0],[1,1],[1,2],[1,3],[1,3],[1,4],[1,4],[1,4],[2,5],[3,4],[3,3],[3,3],[3,3],[3,2],[4,1],[4,1],[5,2],[5,3],[5,4],[5,4],[6,5],[7,4],[7,4],[7,3],[7,2],[8,1],[8,1],[8,1],[9,2],[9,3],[9,3],[9,4],[10,5],[11,5],[11,5]]},

        {"duration":8,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,-1],[0,0],[1,1],[1,1],[2,1],[3,1],[3,1],[3,1],[2,2],[2,2],[3,2],[4,2],[4,2],[4,2],[3,3],[3,3],[4,3],[5,3],[6,3],[7,3],[7,3],[6,4],[6,4],[7,4],[8,4],[9,3],[9,3],[8,2],[7,2],[7,2],[6,1],[5,1],[5,1],[5,0],[5,0],[5,-1],[5,-1]]},
        {"duration":8,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":1,"mirrory":0,"points":[[-1,-1],[0,0],[1,1],[1,1],[2,1],[3,1],[3,1],[3,1],[2,2],[2,2],[3,2],[4,2],[4,2],[4,2],[3,3],[3,3],[4,3],[5,3],[6,3],[7,3],[7,3],[6,4],[6,4],[7,4],[8,4],[9,3],[9,3],[8,2],[7,2],[7,2],[6,1],[5,1],[5,1],[5,0],[5,0],[5,-1],[5,-1]]},
        {"duration":10,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[5,-1],[5,0],[5,1],[5,1],[5,1],[4,1],[3,1],[2,1],[1,1],[0,1],[-1,1],[-1,2],[0,2],[1,2],[2,2],[3,2],[4,2],[5,2],[6,2],[7,2],[8,2],[9,2],[10,2],[11,2],[11,1],[10,1],[9,1],[8,1],[7,1],[6,1],[5,1],[5,2],[5,2],[5,3],[4,3],[3,3],[2,3],[2,4],[2,4],[3,4],[4,4],[5,4],[6,4],[7,4],[8,4],[8,4],[8,3],[7,3],[6,3],[5,3],[2,4],[1,5],[-1,7]]},
        {"duration":10,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":1,"mirrory":0,"points":[[5,-1],[5,0],[5,1],[5,1],[5,1],[4,1],[3,1],[2,1],[1,1],[0,1],[-1,1],[-1,2],[0,2],[1,2],[2,2],[3,2],[4,2],[5,2],[6,2],[7,2],[8,2],[9,2],[10,2],[11,2],[11,1],[10,1],[9,1],[8,1],[7,1],[6,1],[5,1],[5,2],[5,2],[5,3],[4,3],[3,3],[2,3],[2,4],[2,4],[3,4],[4,4],[5,4],[6,4],[7,4],[8,4],[8,4],[8,3],[7,3],[6,3],[5,3],[2,4],[1,5],[-1,7]]},

        {"duration":7,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,1],[0,1],[1,1],[2,1],[2,1],[3,1],[3,1],[3,1],[4,1],[4,1],[4,1],[4,1],[5,1],[5,1],[5,1],[5,1],[5,1],[6,1],[6,1],[6,1],[6,1],[7,1],[7,1],[7,1],[8,1],[8,1],[9,1],[10,1],[11,1],[10,2],[9,3],[8,4],[7,4],[6,4],[5,3],[5,3],[5,3],[4,4],[3,4],[2,4],[1,3],[0,2],[-1,1]]},
        {"duration":7,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":1,"mirrory":0,"points":[[-1,1],[0,1],[1,1],[2,1],[2,1],[3,1],[3,1],[3,1],[4,1],[4,1],[4,1],[4,1],[5,1],[5,1],[5,1],[5,1],[5,1],[6,1],[6,1],[6,1],[6,1],[7,1],[7,1],[7,1],[8,1],[8,1],[9,1],[10,1],[11,1],[10,2],[9,3],[8,4],[7,4],[6,4],[5,3],[5,3],[5,3],[4,4],[3,4],[2,4],[1,3],[0,2],[-1,1]]},
        {"duration":7,"sharpness":0,"offsetx":0,"offsety":1,"mirrorx":0,"mirrory":1,"points":[[-1,1],[0,1],[1,1],[2,1],[2,1],[3,1],[3,1],[3,1],[4,1],[4,1],[4,1],[4,1],[5,1],[5,1],[5,1],[5,1],[5,1],[6,1],[6,1],[6,1],[6,1],[7,1],[7,1],[7,1],[8,1],[8,1],[9,1],[10,1],[11,1],[10,2],[9,3],[8,4],[7,4],[6,4],[5,3],[5,3],[5,3],[4,4],[3,4],[2,4],[1,3],[0,2],[-1,1]]},
        {"duration":7,"sharpness":0,"offsetx":0,"offsety":1,"mirrorx":1,"mirrory":1,"points":[[-1,1],[0,1],[1,1],[2,1],[2,1],[3,1],[3,1],[3,1],[4,1],[4,1],[4,1],[4,1],[5,1],[5,1],[5,1],[5,1],[5,1],[6,1],[6,1],[6,1],[6,1],[7,1],[7,1],[7,1],[8,1],[8,1],[9,1],[10,1],[11,1],[10,2],[9,3],[8,4],[7,4],[6,4],[5,3],[5,3],[5,3],[4,4],[3,4],[2,4],[1,3],[0,2],[-1,1]]},

        {"duration":8,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[1,-1],[1,0],[1,1],[2,1],[3,1],[4,1],[5,1],[6,1],[7,1],[8,1],[9,0],[10,1],[9,2],[8,2],[7,2],[6,2],[5,2],[4,2],[3,2],[2,2],[1,1],[0,2],[1,3],[2,3],[3,3],[4,3],[5,3],[5,3],[5,3],[6,3],[7,3],[8,3],[9,3],[9,4],[8,4],[7,4],[6,4],[5,4],[4,4],[3,4],[2,4],[1,4],[1,5],[1,6],[1,7]]},
        {"duration":8,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":1,"mirrory":0,"points":[[1,-1],[1,0],[1,1],[2,1],[3,1],[4,1],[5,1],[6,1],[7,1],[8,1],[9,0],[10,1],[9,2],[8,2],[7,2],[6,2],[5,2],[4,2],[3,2],[2,2],[1,1],[0,2],[1,3],[2,3],[3,3],[4,3],[5,3],[5,3],[5,3],[6,3],[7,3],[8,3],[9,2],[10,3],[9,4],[9,5],[9,6],[9,7]]},

        {"duration":8,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[1,-1],[1,0],[1,1],[2,1],[3,1],[4,1],[5,1],[6,1],[7,1],[8,1],[9,0],[10,1],[9,2],[8,2],[7,2],[6,2],[5,2],[4,2],[3,2],[2,2],[1,1],[0,2],[1,3],[2,3],[3,3],[4,3],[5,3],[5,3],[5,3],[6,3],[7,3],[8,3],[9,3],[9,4],[8,4],[7,4],[6,4],[5,4],[4,4],[3,4],[2,4],[1,4],[1,5],[1,6],[1,7]]},
        {"duration":8,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":1,"mirrory":0,"points":[[1,-1],[1,0],[1,1],[2,1],[3,1],[4,1],[5,1],[6,1],[7,1],[8,1],[9,0],[10,1],[9,2],[8,2],[7,2],[6,2],[5,2],[4,2],[3,2],[2,2],[1,1],[0,2],[1,3],[2,3],[3,3],[4,3],[5,3],[5,3],[5,3],[6,3],[7,3],[8,3],[9,2],[10,3],[9,4],[9,5],[9,6],[9,7]]},

        {"duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[1,-1],[1,0],[1,1],[1,2],[2,3],[2,4],[3,5],[4,4],[4,3],[5,2],[5,1],[5,0],[5,-1]]},
        {"duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[1,-1],[1,0],[1,1],[1,2],[2,3],[2,4],[3,5],[4,4],[5,5],[6,4],[7,5],[8,4],[8,3],[9,2],[9,1],[9,0],[9,-1]]},
        {"duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,5],[0,5],[1,5],[1,4],[2,4],[3,4],[3,3],[4,3],[5,3],[6,3],[6,2],[7,2],[7,1],[8,1],[9,1],[9,0],[10,0],[10,-1]]},
        {"duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,-1],[0,0],[0,1],[1,1],[1,2],[2,2],[2,3],[3,3],[3,4],[4,4],[4,5],[5,5],[6,5],[6,4],[7,4],[7,3],[8,3],[8,2],[9,2],[9,1],[10,1],[10,0],[11,-1]]},
        {"duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,-1],[0,0],[0,1],[1,1],[1,2],[2,2],[3,2],[3,3],[4,3],[5,3],[6,3],[6,4],[7,4],[8,4],[8,5],[9,5],[10,5],[11,6]]},
        {"duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,6],[0,5],[1,4],[2,3],[3,2],[4,1],[5,1],[6,1],[7,2],[8,3],[9,4],[10,5],[11,6]]},
        {"duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,6],[0,5],[1,4],[1,3],[2,3],[2,2],[2,1],[2,0],[3,1],[4,1],[4,2],[4,3],[5,3],[5,4],[6,5],[7,5],[7,4],[8,4],[8,3],[8,2],[9,2],[9,1],[9,0],[10,-1]]},
        {"duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[1,-1],[1,0],[1,1],[1,2],[1,3],[1,4],[2,5],[3,5],[4,4],[4,3],[4,2],[4,1],[5,0],[6,1],[6,2],[6,3],[6,4],[7,5],[8,5],[9,4],[9,3],[9,2],[9,1],[9,0],[9,-1]]},
        {"duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,1],[0,1],[1,1],[2,1],[3,1],[4,1],[5,1],[6,1],[7,1],[8,1],[9,1],[9,2],[8,3],[7,3],[6,3],[5,3],[4,3],[3,3],[2,3],[1,4],[2,4],[3,5],[4,4],[5,5],[6,4],[7,5],[8,4],[9,5],[10,5],[11,5]]},
        {"duration":5,"sharpness":0,"offsetx":0,"offsety":0,"mirrorx":0,"mirrory":0,"points":[[-1,3],[0,3],[1,3],[2,3],[3,3],[4,3],[5,3],[6,3],[7,3],[8,3],[9,3],[9,2],[8,2],[7,2],[6,2],[5,2],[4,2],[3,2],[2,2],[1,2],[1,3],[2,3],[3,3],[4,3],[5,3],[6,3],[7,3],[8,3],[9,3],[10,3],[11,3]]}
    ]
}