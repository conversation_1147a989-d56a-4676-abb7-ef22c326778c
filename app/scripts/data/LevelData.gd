extends Node

# procedural level generation

func generateEnemyRow(length: int, enemyTypes: Array = [0, 1, 2], fullRowChance: int = -1, isSymmetric: bool = true, largeEnemyList: Array = [7]) -> Array:
	var enemyRow = []
	var hasNonZero = false

	while not hasNonZero:
		enemyRow.clear()
		hasNonZero = false

		if fullRowChance == 0 or (fullRowChance > 0 and randi() % fullRowChance == 0):
			# Generate a full row of the same enemy type.
			var enemy = enemyTypes[randi() % enemyTypes.size()]
			for _i in range(length):
				enemyRow.append(enemy)
				if enemy != 0:
					hasNonZero = true
		else:
			# Generate a row with random enemy types.
			for _i in range(length):
				var enemy = enemyTypes[randi() % enemyTypes.size()]
				enemyRow.append(enemy)
				if enemy != 0:
					hasNonZero = true

			if isSymmetric:
				# Make the row symmetric.
				for i in range(length / 2):
					enemyRow[length - i - 1] = enemyRow[i]

		# Handle large enemies.
		for i in range(length):
			if enemyRow[i] in largeEnemyList:
				if i == 0 or i == length - 1:
					enemyRow[i] = 0  # Remove large enemy from the ends.
				else:
					enemyRow[i - 1] = 0  # Ensure zeros on both sides.
					enemyRow[i + 1] = 0
					hasNonZero = true

	return enemyRow

func getRandomPatternIndex(key):
	var v = PatternData.getData()[key]
	return randi()%v.size()

func getPatternData(key,index):
	var v = PatternData.getData()[key]
	return v[index]

func _getPreset(typeStr,presetStr, levelType, col, row, initialEntryDelay = 2, music = "shmup1", enemyList = [0,1,2], isSymmetric = true, wiggleType = "RANDOM_GLOBAL", forceDual = false, preventDual = false, bg=1):

	# var enemySizes = Global.GameScene.enemySize

	var enemyConf = []
	var wiggleConf = []
	var delayConfig = []
	var patternConfig = []

	var entryDelay = 0

	var delay = initialEntryDelay

	var isDouble = false 
	var wiggleStr = ""

	if(wiggleType=="RANDOM_GLOBAL"):
		wiggleStr = ["NORMAL","COL_ROW","COL","COUNT"][randi()%4]

	for _row in range(row):

		if(isDouble):
			isDouble = false
		else:
			isDouble = (randi()%5==0)
		
		if(forceDual):
			isDouble = true

		if(preventDual):
			isDouble = false
		
		if(wiggleType=="RANDOM_ROW"):
			wiggleStr = ["NORMAL","COL_ROW","COL","COUNT"][randi()%4]

		var enemyRow = generateEnemyRow(col,enemyList,5,isSymmetric,[7])
		for _c in range(1 if !isDouble else 2):
			enemyConf.push_back(enemyRow)
			wiggleConf.push_back(wiggleStr)

		for _c in range(1 if !isDouble else 2):
			delayConfig.push_back(delay)

		# var doMirrorY = randi()%5==0
		# @todo we cannot mirror by Y becuase enemies will fly too low
		var isSharp = randi()%5==0

		var pindex = getRandomPatternIndex(typeStr)
		var pdata = getPatternData(typeStr, pindex)

		var sharpness = 0.0 if !isSharp else randf()/2.0

		for _c in range(1 if !isDouble else 2):

			entryDelay = 1.0 if randi()%5==0 else 2.0

			# 1 -azonnal bejon a kovetkezo
			# 2 -van egy normalis szunet
			# 0.5 - nem varja meg hogy kimenjen

			# entryDelay = 1 if randi()%5==0 else 2
			# if(randi()%10==0):
			# 	entryDelay = 0.5

			# @todo create wave combos where they come in one after another
			#       in a sortof "dance"
			
			var mirrorx = pdata['mirrorx']
			if(_c==1):
				# flip
				mirrorx = 1 if mirrorx==0 else 0

			# index, mirrorx = 0, mirrory = 0, sharpness = 0, duration = 0
			patternConfig.push_back({"type":presetStr,"value":[pindex,mirrorx, pdata['mirrory'],sharpness,0]})

		delay+=entryDelay

	var baseObject = {
	"Title": typeStr.to_lower(),
	"Bg": bg,
	"Type": levelType,
	"WiggleType": wiggleConf,
	"Music": music,
	"Config": enemyConf,
	"EntryDelay": delayConfig,
	"EntryPattern": patternConfig
	}

	return baseObject

func getBonusPreset(col, row, initialEntryDelay , music = "shmup1", enemyList = [0,1,2], isSymmetric = true, forceDual = false, preventDual = false):
	return _getPreset("bonus","BonusPreset","Bonus", col, row, initialEntryDelay , music , enemyList, isSymmetric, "RANDOM_GLOBAL", forceDual, preventDual, self.currentBg)

func getShmupPreset(col, row, initialEntryDelay , music = "shmup1", enemyList = [0,1,2], isSymmetric = true, forceDual = false, preventDual = false):
	return _getPreset("shmup","ShmupPreset","Bonus", col, row, initialEntryDelay , music , enemyList, isSymmetric, "RANDOM_GLOBAL", forceDual, preventDual, self.currentBg)

func getNormalPreset(col, row, initialEntryDelay , music = "shmup1", enemyList = [0,1,2], isSymmetric = true, forceDual = false, preventDual = true):
	return _getPreset("normal","BasicPreset","Normal", col, row, initialEntryDelay , music , enemyList, isSymmetric, "RANDOM_GLOBAL", forceDual, preventDual, self.currentBg)

var currentBg = 1

func generateShmupLevelData():
	var _res = { "data": []}

	# seq 1

	currentBg = 1

	for _i in range(3):
		_res["data"].push_back(getShmupPreset(8,5,0,"shmup1",[1]))

	for _i in range(3):
		_res["data"].push_back(getNormalPreset(8,4,0,"shmup1",[0,1,1,1]))

	_res["data"].push_back(getNormalPreset(8,4,0,"shmup1",[1]))

	_res["data"].push_back( {
		"Title": "rush",
		"Bg": 1,
		"Type": "Rush",
		"Music": "shmup1",
		"enemyCountMod": 30,
		"Repo": [1,2]
	})

	_res["data"].push_back({
		"Title": "debris",
		"Bg": 1,
		"Type": "Debris",
		"Music": "shmup1",
		"DurationBase": 30
	})

	for _i in range(3):
		_res["data"].push_back(getShmupPreset(10,5,0,"shmup1",[1,2,2]))

	for _i in range(3):
		_res["data"].push_back(getNormalPreset(8,4,0,"shmup1",[0,1,2,2]))

	_res["data"].push_back(getNormalPreset(8,4,0,"shmup1",[1,2,2]))

	_res["data"].push_back( {
		"Title": "rush",
		"Bg": 1,
		"Type": "Rush",
		"Music": "shmup1",
		"enemyCountMod": 30,
		"Repo": [1,2]
	})

	_res["data"].push_back({
		"Title": "debris",
		"Bg": 1,
		"Type": "Debris",
		"Music": "shmup1",
		"DurationBase": 45
	})

	_res["data"].push_back(getShmupPreset(20,4,0,"shmup1",[1],true,true))

	_res["data"].push_back(
	{
	"Title": "Dance With Me!",
	"Bg": 1,
	"Type": "Boss",
	"Music": "shmup1",
	"Scene": "Boss1"
	})

	# seq 2
	currentBg = 2

	for _i in range(3):
		_res["data"].push_back(getShmupPreset(8,5,0,"shmup2",[2,4]))

	for _i in range(3):
		_res["data"].push_back(getNormalPreset(8,4,0,"shmup2",[0,4,2]))

	_res["data"].push_back(getNormalPreset(8,4,0,"shmup2",[4,2]))

	_res["data"].push_back( {
		"Title": "rush",
		"Bg": 2,
		"Type": "Rush",
		"Music": "shmup2",
		"enemyCountMod": 40,
		"Repo": [1,2,4]
	})

	_res["data"].push_back({
		"Title": "debris",
		"Bg": 2,
		"Type": "Debris",
		"Music": "shmup2",
		"DurationBase": 50
	})

	for _i in range(3):
		_res["data"].push_back(getShmupPreset(10,5,0,"shmup2",[4,4,3]))

	for _i in range(3):
		_res["data"].push_back(getNormalPreset(8,4,0,"shmup2",[0,4,4,3]))

	_res["data"].push_back(getNormalPreset(8,4,0,"shmup2",[4,4,3]))

	_res["data"].push_back( {
		"Title": "rush",
		"Bg": 2,
		"Type": "Rush",
		"Music": "shmup2",
		"enemyCountMod": 45,
		"Repo": [1,2,3,4]
	})

	_res["data"].push_back({
		"Title": "debris",
		"Bg": 2,
		"Type": "Debris",
		"Music": "shmup2",
		"DurationBase": 55
	})

	_res["data"].push_back(getShmupPreset(20,4,0,"shmup2",[4],true,true))

	_res["data"].push_back(
	{
	"Title": "Monster!",
	"Bg": 2,
	"Type": "Boss",
	"Music": "shmup2",
	"Scene": "Boss2"
	})

	#seq 3
	currentBg = 3

	for _i in range(3):
		_res["data"].push_back(getShmupPreset(8,5,0,"shmup3",[5,4]))

	for _i in range(3):
		_res["data"].push_back(getNormalPreset(8,4,0,"shmup3",[0,5,4]))

	_res["data"].push_back(getNormalPreset(8,4,0,"shmup3",[5,4]))

	_res["data"].push_back( {
		"Title": "rush",
		"Bg": 3,
		"Type": "Rush",
		"Music": "shmup3",
		"enemyCountMod": 40,
		"Repo": [1,2,4,5]
	})

	_res["data"].push_back({
		"Title": "debris",
		"Bg": 3,
		"Type": "Debris",
		"Music": "shmup3",
		"DurationBase": 50
	})

	for _i in range(3):
		_res["data"].push_back(getShmupPreset(10,5,0,"shmup3",[5,5,6]))

	for _i in range(3):
		_res["data"].push_back(getNormalPreset(8,4,0,"shmup3",[0,5,5,6]))

	_res["data"].push_back(getNormalPreset(8,4,0,"shmup3",[5,5,6]))

	_res["data"].push_back( {
		"Title": "rush",
		"Bg": 3,
		"Type": "Rush",
		"Music": "shmup3",
		"enemyCountMod": 50,
		"Repo": [1,2,3,4,5,6]
	})

	_res["data"].push_back({
		"Title": "debris",
		"Bg": 3,
		"Type": "Debris",
		"Music": "shmup3",
		"DurationBase": 60
	})

	_res["data"].push_back(getShmupPreset(20,4,0,"shmup3",[5],true,true))

	_res["data"].push_back(
	{
	"Title": "WingSnap!",
	"Bg": 3,
	"Type": "Boss",
	"Music": "shmup3",
	"Scene": "Boss3",
	"WiggleType": ["COUNT", "COUNT"],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [7, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [7, 1, 0, 0, 0]
	}
	]
	})

	#seq 4
	currentBg = 4

	for _i in range(3):
		_res["data"].push_back(getShmupPreset(8,5,0,"shmup4",[8,5,6]))

	for _i in range(3):
		_res["data"].push_back(getNormalPreset(8,4,0,"shmup4",[0,8,5,6]))

	_res["data"].push_back(getNormalPreset(8,4,0,"shmup4",[6,8]))

	_res["data"].push_back( {
		"Title": "rush",
		"Bg": 4,
		"Type": "Rush",
		"Music": "shmup4",
		"enemyCountMod": 50,
		"Repo": [1,2,4,5,6,8]
	})

	_res["data"].push_back({
		"Title": "debris",
		"Bg": 4,
		"Type": "Debris",
		"Music": "shmup4",
		"DurationBase": 60
	})

	for _i in range(3):
		_res["data"].push_back(getShmupPreset(10,5,0,"shmup4",[8,8,9]))

	for _i in range(3):
		_res["data"].push_back(getNormalPreset(8,4,0,"shmup4",[0,8,8,9]))

	_res["data"].push_back(getNormalPreset(8,4,0,"shmup4",[8,8,9]))

	_res["data"].push_back( {
		"Title": "rush",
		"Bg": 4,
		"Type": "Rush",
		"Music": "shmup4",
		"enemyCountMod": 55,
		"Repo": [1,2,3,4,5,6,8,9]
	})

	_res["data"].push_back({
		"Title": "debris",
		"Bg": 4,
		"Type": "Debris",
		"Music": "shmup4",
		"DurationBase": 65
	})

	_res["data"].push_back(getShmupPreset(20,4,0,"shmup4",[8],true,true))

	_res["data"].push_back(
	{
	"Title": "Itsy Bitsy...",
	"Bg": 4,
	"Type": "Boss",
	"Music": "final_boss",
	"Scene": "Boss4"
	})

	if(Config.Env.IsDemo):
		_res.data[20] = {
			"Title": "YOU'RE AWESOME!",
			"LabelOverride": "Wow!",
			"Bg": 4,
			"Type": "Boss",
			"Music": "menu",
			"Scene": "DemoComplete"
		}

	return _res


# campaign

func getCampaignData():
	var _cData = {
	"data": [
	{
	"Title": "Don't hurt us!",
	"Bg": 1,
	"Type": "Normal",
	"Music": "bg1",
	"Config": [
	[0, 0, 1, 1, 0, 1, 1, 0, 0],
	[0, 1, 1, 1, 1, 1, 1, 1, 0],
	[0, 0, 1, 1, 1, 1, 1, 0, 0],
	[0, 0, 0, 1, 1, 1, 0, 0, 0],
	[1, 0, 0, 0, 1, 0, 0, 0, 1]
	],
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"EntryDelay": [],
	"EntryPattern": [
	{
		# index, type = "normal", mirrorx = 0, mirrory = 0, sharpness = 0, duration = 0):
		"type": "BasicPreset",
		"value": [0, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [0, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [1, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [1, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [2, 1, 0, 0, 0]
	}
	]
	},
	{
	"Title": "Go!",
	"Bg": 1,
	"Type": "Normal",
	"Music": "bg1",
	"Config": [
	[0, 1, 1, 1, 1, 1, 1, 1, 0],
	[1, 1, 1, 1, 1, 1, 1, 1, 1],
	[1, 1, 1, 1, 1, 1, 1, 1, 1],
	[0, 1, 0, 1, 1, 1, 0, 1, 0],
	[1, 0, 1, 0, 1, 0, 1, 0, 1]
	],
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [0, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [0, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [1, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [1, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [2, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "With all you got!",
	"Bg": 1,
	"Type": "Rush",
	"Music": "bg1",
	"Repo": [1, 2]
	},

	{
	"Title": "We got help!",
	"Bg": 1,
	"Type": "Normal",
	"Music": "bg1",
	"Config": [
	[0,2,1,1,2,1,1,2,0],
	[0,1,1,1,0,1,1,1,0],
	[1,1,1,1,2,1,1,1,1],
	[0,1,1,1,0,1,1,1,0],
	[0,0,2,1,2,1,2,0,0]
	],
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [2, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [2, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [3, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [3, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [4, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Sheesh!",
	"Bg": 1,
	"Type": "Normal",
	"Music": "bg1",
	"Config": [
	[2,2,1,1,2,1,1,2,2],
	[0,1,1,2,0,2,1,1,0],
	[2,1,1,1,2,1,1,1,2],
	[0,1,1,1,0,1,1,1,0],
	[0,1,2,1,2,1,2,1,0]
	],
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [2, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [2, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [3, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [3, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [4, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Kill 'em all!",
	"Bg": 1,
	"Type": "Bonus",
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"Music": "bg1",
	"Config": [
	[1, 1, 1, 1, 1, 1, 1, 1, 1],
	[1, 1, 1, 1, 1, 1, 1, 1, 1],
	[1, 1, 1, 1, 1, 1, 1, 1, 1],
	[1, 1, 1, 1, 1, 1, 1, 1, 1],
	[1, 1, 1, 1, 1, 1, 1, 1, 1]
	],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BonusPreset",
		"value": [0, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [0, 1, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [1, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [1, 1, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [2, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Debris Dash!",
	"Bg": 1,
	"Type": "Debris",
	"Music": "chill",
	"DurationBase": 45,
	},

	{
	"Title": "We are Grid!",
	"Bg": 1,
	"Type": "Normal",
	"Music": "bg1",
	"Config": [
	[1,1,1,1,1,1,1,1,1,1],
	[1,2,1,2,1,2,1,2,1,2],
	[2,1,2,1,2,1,2,1,2,1],
	[1,2,1,2,1,2,1,2,1,2],
	[1,1,1,1,1,1,1,1,1,1]
	],
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"EntryDelay": [0,0.2,0.4,0.6,0.8],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [10, 0, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [11, 1, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [11, 1, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [12, 1, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [12, 1, 0, 0, 1]
	}
	]
	},


	{
	"Title": "Dizzy ...",
	"Bg": 1,
	"Type": "Bonus",
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"Music": "bg1",
	"Config": [
	[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
	[1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1],
	[1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1],
	[2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 1],
	[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2],
	[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2],
	],
	"EntryDelay": [0,0,2,2,4,4],
	"EntryPattern": [
	{
		"type": "BonusPreset",
		"value": [10, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [10, 1, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [11, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [11, 1, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [12, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [12, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Dance With Me!",
	"Bg": 1,
	"Type": "Boss",
	"Music": "boss",
	"Scene": "Boss1"
	},
	
	{
	"Title": "Uncharted territory",
	"Bg": 2,
	"Type": "Normal",
	"Music": "bg2",
	"Config": [
	[0,4,4,4,0,4,4,4,0],
	[4,4,4,4,4,4,4,4,4],
	[0,0,4,4,0,4,4,0,0],
	[4,4,0,0,4,0,0,4,4],
	[0,0,4,4,0,4,4,0,0]
	],
	"WiggleType": ["COL", "COL", "COL", "COL", "COL"],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [6, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [6, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [7, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [7, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [8, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Avanos!",
	"Bg": 2,
	"Type": "Normal",
	"Music": "bg2",
	"Config": [
	[0,4,4,4,4,4,4,4,0],
	[4,4,4,4,4,4,4,4,4],
	[0,0,4,4,4,4,4,0,0],
	[4,4,4,4,4,4,4,4,4],
	[0,0,4,4,4,4,4,0,0]
	],
	"WiggleType": ["COL", "COL", "COL", "COL", "COL"],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [6, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [6, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [7, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [7, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [8, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Chaaaarge!",
	"Bg": 2,
	"Type": "Rush",
	"Music": "bg2",
	"Repo": [1, 2, 3 , 4]
	},

	{
	"Title": "Protect the Monster",
	"Bg": 2,
	"Type": "Normal",
	"Music": "bg2",
	"Config": [
	[0,3,4,3,0,3,4,3,0],
	[4,4,4,4,0,4,4,4,4],
	[0,0,4,4,0,4,4,0,0],
	[4,4,4,0,0,0,4,4,4],
	[0,0,4,4,4,4,4,0,0]
	],
	"WiggleType": ["COL_ROW", "COL_ROW", "COL_ROW", "COL_ROW", "COL_ROW"],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [1, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [9, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [0, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [4, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [5, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Amazing!",
	"Bg": 2,
	"Type": "Normal",
	"Music": "bg2",
	"Config": [
	[0,3,4,4,4,4,4,3,0],
	[4,4,4,3,3,3,4,4,4],
	[0,3,4,4,3,4,4,3,0],
	[4,4,4,3,3,3,4,4,4],
	[0,3,4,4,3,4,4,3,0]
	],
	"WiggleType": ["COL_ROW", "COL_ROW", "COL_ROW", "COL_ROW", "COL_ROW"],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [1, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [9, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [0, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [4, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [5, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Retreat!",
	"Bg": 2,
	"Type": "Bonus",
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"Music": "bg2",
	"Config": [
	[4,4,4,4,4,4,4,4,4],
	[0,4,4,4,0,4,4,4,0],
	[4,0,0,4,4,4,0,0,4],
	[4,4,0,4,0,4,0,4,4],
	[4,4,4,4,4,4,4,4,4]
	],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BonusPreset",
		"value": [4, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [7, 1, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [2, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [3, 1, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [9, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Clean your room!",
	"Bg": 2,
	"Type": "Debris",
	"Music": "chill",
	"DurationBase": 55,
	},

	{
	"Title": "Pile up!",
	"Bg": 2,
	"Type": "Normal",
	"Music": "bg2",
	"Config": [
	[3,4,4,4,4,3,3,4,4,4,4,3],
	[4,4,4,4,4,4,4,4,4,4,4,4],
	[4,4,4,3,4,4,4,4,3,4,4,4],
	[4,4,4,4,4,4,4,4,4,4,4,4],
	[4,4,4,4,4,3,3,4,4,4,4,4],
	],
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"EntryDelay": [0,0.2,0.4,0.6,0.8],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [14, 0, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [15, 0, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [16, 0, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [17, 0, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [18, 0, 0, 0, 1]
	}
	]
	},

	{
	"Title": "Living Wall",
	"Bg": 2,
	"Type": "Bonus",
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"Music": "bg2",
	"Config": [
	[4, 4, 4, 4, 4, 4, 4, 4, 4, 4],
	[4, 4, 4, 4, 4, 4, 4, 4, 4, 4],
	[4, 4, 4, 4, 4, 4, 4, 4, 4, 3],
	[4, 4, 4, 4, 4, 4, 4, 4, 4, 3],
	[4, 4, 4, 4, 4, 4, 4, 4, 3, 3],
	[4, 4, 4, 4, 4, 4, 4, 4, 3, 3],
	],
	"EntryDelay": [0,0,2,2,4,4],
	"EntryPattern": [
	{
		"type": "BonusPreset",
		"value": [14, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [14, 1, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [15, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [15, 1, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [13, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [13, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Monsteeeer!",
	"Bg": 2,
	"Type": "Boss",
	"Music": "boss",
	"Scene": "Boss2"
	},

	{
	"Title": "I'm blue",
	"Bg": 3,
	"Type": "Normal",
	"Music": "bg3",
	"Config": [
	[0,1,5,5,5,5,5,1,0],
	[0,5,1,5,5,5,1,5,0],
	[0,0,5,0,5,0,5,0,0],
	[0,0,0,5,0,5,0,0,0],
	[0,0,0,0,5,0,0,0,0]
	],
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [1, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [2, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [2, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [1, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [9, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "And now ...",
	"Bg": 3,
	"Type": "Normal",
	"Music": "bg3",
	"Config": [
	[1,1,5,5,5,5,5,1,1],
	[0,5,1,5,5,5,1,5,0],
	[0,1,5,1,5,1,5,1,0],
	[0,1,1,5,1,5,1,0,0],
	[0,0,5,1,5,1,5,0,0]
	],
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [1, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [2, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [2, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [1, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [9, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Jeeeeez!",
	"Bg": 3,
	"Type": "Rush",
	"Music": "bg3",
	"Repo": [1, 2, 3, 4, 5, 6]
	},

	{
	"Title": "Carpit Bombin'",
	"Bg": 3,
	"Type": "Normal",
	"Music": "bg3",
	"Config": [
	[6,6,6,5,5,5,6,6,6],
	[1,5,6,5,6,5,6,5,1],
	[0,1,5,5,5,5,5,1,0],
	[0,0,1,5,5,5,1,0,0],
	[0,0,0,1,5,1,0,0,0]
	],
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [7, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [7, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [1, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [2, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [3, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Let's rock!",
	"Bg": 3,
	"Type": "Normal",
	"Music": "bg3",
	"Config": [
	[6,6,6,5,5,5,6,6,6],
	[1,5,6,5,6,5,6,5,1],
	[6,1,5,5,5,5,5,1,6],
	[0,5,1,5,5,5,1,5,0],
	[0,0,5,1,5,1,5,0,0]
	],
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [7, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [7, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [1, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [2, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [3, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Shufflin' away",
	"Bg": 3,
	"Type": "Bonus",
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"Music": "bg3",
	"Config": [
	[5,5,5,5,6,5,5,5,5],
	[5,5,5,6,5,6,5,5,5],
	[5,5,6,5,5,5,6,5,5],
	[5,6,5,5,5,5,5,6,5],
	[6,5,5,5,6,5,5,5,6]
	],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BonusPreset",
		"value": [1, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [1, 1, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [1, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [1, 1, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [9, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Space-Junkyard",
	"Bg": 3,
	"Type": "Debris",
	"Music": "chill",
	"DurationBase": 60,
	},

	{
	"Title": "All at once ...",
	"Bg": 3,
	"Type": "Normal",
	"Music": "bg3",
	"Config": [
	[6,6,6,6,6,6,6,6,6,6,6],
	[5,5,5,5,5,5,5,5,5,5,5],
	[5,5,5,5,5,5,5,5,5,5,5],
	[5,5,5,5,5,5,5,5,5,5,5],
	[5,5,5,5,5,5,5,5,5,5,5]
	],
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"EntryDelay": [0,0.2,0.4,0.6,0.8],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [10, 0, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [11, 1, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [11, 1, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [12, 1, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [12, 1, 0, 0, 1]
	}
	]
	},

	{
	"Title": "Exercise",
	"Bg": 3,
	"Type": "Bonus",
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"Music": "bg3",
	"Config": [
	[5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5],
	[5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5],
	[6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 6],
	[6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 6],
	[6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 6, 6],
	[6, 6, 6, 5, 5, 5, 5, 5, 5, 5, 5, 5, 6, 6, 6],
	],
	"EntryDelay": [0,0,2,2,4,4],
	"EntryPattern": [
	{
		"type": "BonusPreset",
		"value": [16, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [17, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [16, 1, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [17, 1, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [18, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [18, 1, 0, 0, 0]
	}
	]
	},


	{
	"Title": "WingSnap!",
	"Bg": 3,
	"Type": "Boss",
	"Music": "boss",
	"Scene": "Boss3",
	"WiggleType": ["COUNT", "COUNT"],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [7, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [7, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Only when I move!",
	"Bg": 4,
	"Type": "Normal",
	"Music": "bg4",
	"Config": [
	[10,0,10,0,10,0,10],
	[0,10,0,10,0,10,0],
	[8,0,8,0,8,0,8],
	[0,8,0,8,0,8,0]
	],
	"WiggleType": ["NORMAL", "NORMAL", "COUNT", "COUNT" ],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [3, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [3, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [5, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [5, 1, 0, 0, 0]
	}
	]
	},
	{
	"Title": "Heyaaaa!",
	"Bg": 4,
	"Type": "Normal",
	"Music": "bg4",
	"Config": [
	[10,0,10,0,10,0,10],
	[0,10,0,10,0,10,0],
	[3,8,8,1,8,8,3],
	[1,8,1,8,1,8,1],
	[0,2,8,8,8,2,0]
	],
	"WiggleType": ["COUNT", "NORMAL", "COUNT", "NORMAL", "COUNT" ],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [3, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [3, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [5, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [5, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [3, 0, 0, 0, 0]
	},
	]
	},

	{
	"Title": "Dissssappointed!!!",
	"Bg": 4,
	"Type": "Rush",
	"Music": "bg4",
	"Repo": [1, 2, 3, 4, 5, 6, 8, 9, 10]
	},

	{
	"Title": "Cos's Sins",
	"Bg": 4,
	"Type": "Normal",
	"Music": "bg4",
	"Config": [
	[9,9,10,9,10,9,9],
	[10,10,0,10,0,10,10],
	[8,0,8,0,8,0,8],
	[0,8,0,8,0,8,0]
	],
	"WiggleType": ["NORMAL", "NORMAL", "COUNT", "COUNT" ],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [7, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [7, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [1, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [1, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "3,2,1!",
	"Bg": 4,
	"Type": "Normal",
	"Music": "bg4",
	"Config": [
	[9,9,10,9,10,9,9],
	[10,10,2,10,2,10,10],
	[8,2,8,1,8,2,8],
	[1,8,1,8,1,8,1],
	[1,0,8,8,8,0,1]
	],
	"WiggleType": ["COUNT", "NORMAL", "COUNT", "NORMAL", "COUNT" ],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [7, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [7, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [1, 0, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [1, 1, 0, 0, 0]
	},
	{
		"type": "BasicPreset",
		"value": [1, 1, 0, 0, 0]
	}
	]
	},

	{
	"Title": "Flying in shame",
	"Bg": 4,
	"Type": "Bonus",
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"Music": "bg4",
	"Config": [
	[9, 8, 9, 8, 9, 8, 9, 8, 9],
	[10, 10, 10, 10, 10, 10, 10, 10, 10],
	[9, 8, 9, 8, 9, 8, 9, 8, 9],
	[10, 10, 10, 10, 10, 10, 10, 10, 10],
	[9, 8, 9, 8, 9, 8, 9, 8, 9]
	],
	"EntryDelay": [],
	"EntryPattern": [
	{
		"type": "BonusPreset",
		"value": [0, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [0, 1, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [1, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [1, 1, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [2, 1, 0, 0, 0]
	}
	]
	},
	{
	"Title": "Recycle this!",
	"Bg": 4,
	"Type": "Debris",
	"Music": "chill",
	"DurationBase": 70,
	},

	{
	"Title": "Now or never!",
	"Bg": 4,
	"Type": "Normal",
	"Music": "bg4",
	"Config": [
	[9,9,9,9,9,9,9,9,9,9,9,9],
	[5,5,5,5,5,5,5,5,5,5,5,5],
	[10,10,10,10,10,10,10,10,10,10,10,10],
	[5,5,5,5,5,5,5,5,5,5,5,5],
	[8,8,8,8,8,8,8,8,8,8,8,8]
	],
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL", "COUNT"],
	"EntryDelay": [0,0.2,0.4,0.6,0.8],
	"EntryPattern": [
	{
		"type": "BasicPreset",
		"value": [14, 0, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [15, 0, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [16, 0, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [17, 0, 0, 0, 1]
	},
	{
		"type": "BasicPreset",
		"value": [18, 0, 0, 0, 1]
	}
	]
	},

	{
	"Title": "Moire",
	"Bg": 4,
	"Type": "Bonus",
	"WiggleType": ["NORMAL", "NORMAL", "NORMAL", "NORMAL"],
	"Music": "bg4",
	"Config": [
	[10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10],
	[9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9, 9],
	[8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8],
	[5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5],
	],
	"EntryDelay": [0,0.5,1,1.5],
	"EntryPattern": [
	{
		"type": "BonusPreset",
		"value": [19, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [20, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [21, 0, 0, 0, 0]
	},
	{
		"type": "BonusPreset",
		"value": [22, 0, 0, 0, 0]
	}
	]
	},


	{
	"Title": "Itsy Bitsy...",
	"Bg": 4,
	"Type": "Boss",
	"Music": "final_boss",
	"Scene": "Boss4"
	}
	]}

	if(Config.Env.IsDemo):
		_cData.data[10] = {
			"Title": "YOU'RE AWESOME!",
			"LabelOverride": "Wow!",
			"Bg": 4,
			"Type": "Boss",
			"Music": "menu",
			"Scene": "DemoComplete"
		}

	return _cData;
