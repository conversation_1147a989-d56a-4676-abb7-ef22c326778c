extends "res://scripts/EnemyBase.gd"

var attackTolerance = 15

func _ready():
	Bullet = preload("res://scenes/EnemyBullet_4.tscn")
	health = 60
	ExplosionColor = Color(0.2,0.2,1.1)
	pointValue = 5000
	species = "EyeGuy"
	attackTolerance += randi()%10

func preDamage():
	shieldActiveEff()
	Global.setTimeout(self,0.1,self,"shieldOffEff")

var shieldHitCount = 0

func canBeDamaged():
	shieldHitCount += 1
	# if you hit shield many times, it will damage it

	if(shieldHitCount>attackTolerance):
		# you annoy me, let's attack
		shieldHitCount = attackTolerance
		startAttack()


	return (self.mode == Global.EnemyMode.ATTACK) || (self.mode == Global.EnemyMode.ENTRY)

var rotationTargetDeg = 0

func rotationHandler(delta):

	if self.mode != Global.EnemyMode.ATTACK || Global.isOffScreenBottom(self.global_position, 0):
		rotationTargetDeg = calcRotationDegrees(delta)
	else:
		var dir = Global.getPlayer().position - self.global_position
		var radAngle = atan2(dir.y, dir.x)
		var degAngle = rad2deg(radAngle)
		rotationTargetDeg = degAngle+90
	
	var rotDiff = rotationTargetDeg-rotation_degrees
	
	rotation_degrees+=(rotDiff/2)*delta*10

func attackSpeedMultiplier():
	return 2.0

var shieldActive = false

func shieldActiveEff():
	shieldActive = true
	$ShieldEffect.modulate.a = 1
	$ShieldEffect.modulate.b = 0

func shieldOnEff():
	$ShieldEffect.modulate.a = 0.5
	$ShieldEffect.modulate.b = 1

func shieldOffEff():
	shieldActive = false
	$ShieldEffect.modulate.a = 0.1
	$ShieldEffect.modulate.b = 1


func preProcess(_delta):

	if self.mode != Global.EnemyMode.ATTACK && self.mode != Global.EnemyMode.ENTRY:
		if(!shieldActive):
			shieldOnEff()
	else:
		shieldOffEff()

	return true
