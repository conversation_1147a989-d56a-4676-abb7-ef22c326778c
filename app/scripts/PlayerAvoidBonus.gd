extends Area2D

func _ready():
	var _c = connect("area_entered",self,"_on_hit")

var eventCount = 0

func addPoints():

	# no bonus if boss level
	# too distracting
	if(Global.GameScene.isBossLevel()):
		return false

	if(Global.getPlayer().isPlayerInvincible()):
		# no bonus if god
		return false

	if(Global.doThrottle("that_was_close", 2500)):
		return false
	
	if(Global.GameScene.isPlayerReady()):

		if(Global.getPlayer().isShieldActive()):
			return false

		Global.GameScene.shakeCamera(0.1)

		var _pos =Global.getPlayer().position-Vector2(0,50) 
		Global.GameScene.spawnBonusLabel(_pos , "Close encounter!\n+"+str(Config.ThatWasClosePoints), 1.0, false, false, 0.7);
		Global.GameScene.spawnBonusCrystals(5)
		Global.playTts(SoundManager.tts_that_was_close)

		eventCount+=1

		Global.GameScene.addScore(Config.ThatWasClosePoints);

		if(eventCount == 10):
			Achievments.acquire("yolo")

func _on_hit(target):

	if(target.has_method("isDeadly")):

		if(!target.isDeadly()):
			return false

		Global.setTimeout(self, 0.5, self, "addPoints")
