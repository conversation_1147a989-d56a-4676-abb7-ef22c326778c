class_name PathAnimator
extends Node

var pathData = {}

var currentTime = 0.0
var pathDuration = 3.0
var pathSharpness = 0.0

const X_DIVIDE = 10
const Y_DIVIDE = 6

# Function to calculate a point on a Bezier curve
func bezier_curve(points: PoolVector2Array, t: float, sharpness: float) -> Vector2:
	assert(t >= 0.0 and t <= 1.0, "t must be between 0 and 1")
	assert(sharpness >= 0.0 and sharpness <= 1.0, "sharpness must be between 0 and 1")
	assert(points.size() >= 2, "There must be at least two points")

	var bezier_point = get_bezier_point(points, t)
	var linear_point = get_linear_point(points, t)
	
	# Interpolate between the Bezier point and the linear segment point
	return bezier_point.linear_interpolate(linear_point, sharpness)

# Recursive helper function for Bezier curve calculation
func get_bezier_point(points: PoolVector2Array, t: float) -> Vector2:
	if points.size() == 2:
		return points[0].linear_interpolate(points[1], t)
	else:
		var new_points = PoolVector2Array()
		for i in range(points.size() - 1):
			new_points.append(points[i].linear_interpolate(points[i + 1], t))
		return get_bezier_point(new_points, t)

# Function to calculate the corresponding linear point
func get_linear_point(points: PoolVector2Array, t: float) -> Vector2:
	var n = points.size() - 1
	var segment = int(t * n)
	var local_t = (t * n) - segment
	return points[segment].linear_interpolate(points[min(segment + 1, n)], local_t)

func getCoordsBasedOnPathPoints(pathPoint:Vector2):

	var xUnit = Global.getWindowSize().x / X_DIVIDE;
	var yUnit = Global.getWindowSize().y / Y_DIVIDE;

	var pathMultiplier = Vector2(xUnit, yUnit)

	return Vector2(pathPoint*pathMultiplier)

func getPointVectorArray(pData):

	var pointVectors = []

	for dot in pData["points"]:

		var x = dot[0]+pData["offsetx"]
		var y = dot[1]+pData["offsety"]


		if(pathData["mirrorx"]==1):
			x = X_DIVIDE-x

		if(pathData["mirrory"]==1):
			y = Y_DIVIDE-y

		pointVectors.push_back(getCoordsBasedOnPathPoints(Vector2(x,y)))
	
	return pointVectors

func setPathData(pData, finalCoord = null, interpolateCounter = 5, maxJitter = Vector2(0,0)):
	pathData = pData
	var _arr = getPointVectorArray(pathData);

	# calculate interpolation coords

	var _lastCoord = _arr[_arr.size()-1]

	if(finalCoord):
		for i in range(interpolateCounter):
			var _divider = float(1/float(interpolateCounter));
			var _progress = i*_divider
			var _point = _lastCoord.linear_interpolate(finalCoord,_progress)


			if(maxJitter!=Vector2(0,0)):
				_point.x = (_point.x-maxJitter.x/2)+(randf()*maxJitter.x)
				_point.y = (_point.y-maxJitter.y/2)+(randf()*maxJitter.y)

			_arr.push_back(_point)

		# add final coord
		_arr.push_back(finalCoord)

	_points = PoolVector2Array(_arr)

func stepPosition(delta):
	currentTime = currentTime+(delta/(pathDuration*Global.GameScene.getEnemyEntrySpeed()))

	var res = false

	if(currentTime>1.0):
		currentTime=1.0
		res = true

	return res

var _coords = null
var _didEnd = false

func getPosition():
	return _coords

func didEnd():
	return _didEnd

var _points = null

func calcPosition(cTime):

	pathDuration = pathData["duration"]
	pathSharpness = pathData["sharpness"]

	return bezier_curve(_points,cTime, pathSharpness);

func move(delta):

	_didEnd = stepPosition(delta)
	_coords  = calcPosition(currentTime)
