extends Area2D

func _ready():
	var _c = connect("area_entered",self,"_on_hit")

func _process(delta):
	get_parent().offset += 200*delta

func _on_hit(target):

	if(target.has_method("getDamagePoint")):

		if not Global.doThrottle("TestSprite_900",100):
			Global.GameScene.spawnBonusLabel(target.position,["XD",":)",";)",":o"][randi()%4],randf()+0.5);
			Global.GameScene.spawnCrystal(target.position,Global.CrystalType.c5);

		# destroy bullet
		Global.callIfExists(target,"destroy")
