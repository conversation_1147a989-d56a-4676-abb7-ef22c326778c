extends Node2D

export var explosionColorMod = Color(1,1,1,1)

func removeFromScene():
	self.queue_free()

func _ready():

	if $AudioStreamPlayer2D != null:
		$AudioStreamPlayer2D.autoplay = false
		if(Global.OptionsData.isSoundOn):
			$AudioStreamPlayer2D.play()

	modulate = explosionColorMod

	# add small camera shake
	Global.GameScene.shakeCamera(0.1)

	var tween = Global.createTween(self)
	tween.interpolate_property(self,"modulate:a",1,0,1, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.interpolate_property(self,"scale",Vector2(0.2,0.2),Vector2(1.2,1.2), 0.8, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.connect("tween_all_completed",self,"removeFromScene")
	tween.start()