extends Node2D

var Sparkle = preload("res://scenes/Sparkle.tscn")
var BonusMultiplierLabel = preload("res://scenes/BonusMultiplierLabel.tscn")

var statsData = {}
var DisplayData = {}
var isHighScore = false
var displayScore = 0
var baseScore = 0
var duration = 0
var actualVisibleScore = 0

func updateDispalyScore(val, doShow):
	displayScore = val
	if(doShow):
		$FinalScore.text = str(val)

func addSparkle(pos):
	var sparkle = Sparkle.instance()
	sparkle.position = pos
	sparkle.scale = Vector2(3,3)
	self.add_child(sparkle)


func sparkleLabel(pos):
	for _i in range(0,20):
		Global.setTimeout(self,0.05*_i,self,"addSparkle",[pos + Vector2(_i*(10+randi()%10),randi()%20)])

var SuperbCounter = 0
var AllCounter = 0

func animateLabel(obj, index, score):

	AllCounter+=1

	var summ = obj["multiplier"]*obj["value"]
	var txt = str(obj["multiplier"])+"x"+obj["prefix"]+str(obj["value"])+obj["suffix"]+" = "+str(summ)

	var _node = get_node("Details/Values/"+obj["label"])

	if(obj["minimal_time"]>duration):
		_node.text = "0 (min "+str(obj["minimal_time"])+"s)"
		_node.modulate.a = 0.5
		return false

	# @todo if obj["value"]>obj["limit"] animate, add exploding stars, fireworks, sound effect
	# @todo if everythong is > limit, then add a text PERFECT or something

	_node.text = txt

	updateDispalyScore(score, false)

	var _isSuperb = obj["value"]>obj["limit"]

	if(obj["limit"]<=0):
		_isSuperb = false
	
	if(obj["value"]==0):
		_node.modulate.a = 0.5

	if(_isSuperb):
		Global.playSound(SoundManager.BonusSound, Global.getWindowSize()/Vector2(2,2))
		sparkleLabel(_node.get_global_transform().origin)
		_node.modulate.g = 0.2
		_node.modulate.b = 0.2
		_node.modulate.r = 1.0
		SuperbCounter+=1
	elif(obj["value"]>0):
		Global.playSound(SoundManager.Clunk, Global.getWindowSize()/Vector2(2,2))
	


func highScoreDisplay():
	# display high score if there's any
	$HighScore.visible = isHighScore
	# @todo add sound effect and fireworks, some visual effect etc
	if(isHighScore):
		Global.playSound(SoundManager.ExtraLife, Global.getWindowSize()/Vector2(2,1.5))

func wowDisplay():
	if(AllCounter==SuperbCounter):
		var bonusLabel = BonusMultiplierLabel.instance()
		bonusLabel.z_index = Config.TopZIndex+10
		bonusLabel.position = Global.getWindowSize()/Vector2(2,2)
		add_child(bonusLabel)
		bonusLabel.call_deferred("init","PERFECTION :) !", 0.5, false, true, 3.0)
		Global.playTts(SoundManager.tts_bonus)


func calculateRealScore():
	var _i = 0
	var _sum = 0
	for _ok in DisplayData:
		var _o = DisplayData[_ok]

		var summ = _o["multiplier"]*_o["value"]

		if(_o["minimal_time"]<duration):
			Global.dbg("+")
			_sum+=summ

		Global.dbg("SUM")
		Global.dbg(duration)
		Global.dbg(_o)
		Global.dbg(_o["label"])
		Global.dbg(summ)
		Global.dbg(_sum)

		Global.setTimeout(self,0.2*_o["order"],self,"animateLabel",[_o,_i,baseScore+_sum])

		_i+=1

	# delayed high score display
	Global.setTimeout(self,0.2*(_i+1),self,"highScoreDisplay")
	Global.setTimeout(self,0.2*(_i+2),self,"wowDisplay")

	# final score, is highscore?

	var finalScore = Global.lastScoreObject["score"]+_sum

	if(!Global.getCheatState()):
		isHighScore = Profile.saveHighScore(finalScore ,Global.lastScoreObject["mode"],Global.lastScoreObject["summaryStatsObject"],Global.lastScoreObject["difficulty"],Global.lastScoreObject["version"])

	# updateDispalyScore(finalScore)

func scoreIncAnimation():

	if(!Global.doThrottle("stats_score_animation",50)):

		if(actualVisibleScore<displayScore):
			actualVisibleScore += int((displayScore-actualVisibleScore)/2)+10
			Global.playSound(SoundManager.ShortBeep, Global.getWindowSize()/Vector2(2,2))
		else:
			actualVisibleScore = displayScore

	$FinalScore.text = str(actualVisibleScore)

func _process(_delta):

	get_node("Void").visible = Global.getCheatState()
	scoreIncAnimation()


func setDisplayObject():

	duration = statsData["key_"+str(Global.StatsKeys.DURATION)]

	DisplayData = {

		"key_"+str(Global.StatsKeys.LEVEL): {
			"order": 1,
			"multiplier": 1000,
			"suffix": "",
			"prefix": "",
			"limit": 20,
			"value": statsData["key_"+str(Global.StatsKeys.LEVEL)],
			"label": "Level",
			"minimal_time": 0
		},

		"key_"+str(Global.StatsKeys.DURATION): {
			"order": 2,
			"multiplier": 10,
			"suffix": "s",
			"prefix": "",
			"limit": 300,
			"value": duration,
			"label": "Duration",
			"minimal_time": 0
		},

		"key_"+str(Global.StatsKeys.GEMS): {
			"order": 3,
			"multiplier": 10,
			"suffix": "",
			"prefix": "$",
			"limit": 10000,
			"value": statsData["key_"+str(Global.StatsKeys.GEMS)],
			"label": "Crystals",
			"minimal_time": 0
		},

		"key_"+str(Global.StatsKeys.ACCURACY): {
			"order": 4,
			"multiplier": 1000,
			"suffix": "%",
			"prefix": "",
			"limit": 75,
			"value": statsData["key_"+str(Global.StatsKeys.ACCURACY)],
			"label": "Accuracy",
			"minimal_time": 120
		},

		"key_"+str(Global.StatsKeys.ENTRY_KILLS): {
			"order": 5,
			"multiplier": 1000,
			"suffix": "%",
			"prefix": "",
			"limit": 75,
			"value": statsData["key_"+str(Global.StatsKeys.ENTRY_KILLS)],
			"label": "EntryKills",
			"minimal_time": 300
		},

		"key_"+str(Global.StatsKeys.ROWS_CLEARED): {
			"order": 6,
			"multiplier": 1000,
			"suffix": "%",
			"prefix": "",
			"limit": 75,
			"value": statsData["key_"+str(Global.StatsKeys.ROWS_CLEARED)],
			"label": "RowsCleared",
			"minimal_time": 300
		},

		"key_"+str(Global.StatsKeys.CLOSE_KILLS): {
			"order": 7,
			"multiplier": 1000,
			"suffix": "%",
			"prefix": "",
			"limit": 10,
			"value": statsData["key_"+str(Global.StatsKeys.CLOSE_KILLS)],
			"label": "CloseKills",
			"minimal_time": 60
		},

		"key_"+str(Global.StatsKeys.REAL_DIFFICULTY): {
			"order": 8,
			"multiplier": 50000,
			"suffix": "",
			"prefix": "",
			"limit": 2,
			"value": statsData["key_"+str(Global.StatsKeys.REAL_DIFFICULTY)],
			"label": "Difficulty",
			"minimal_time": 300
		}
	}

func enableOkButton():
	$Ok.disabled = false
	$Ok.grab_focus()
	var _ok = $Ok.connect("pressed", self, "finishStats")

func _ready():
	self.statsData = Global.lastStatsObject

	Global.playMusic($BackgroundMusicPlayer, "gameover")

	# disable ok button
	$Ok.disabled = true

	# enable ok button after 1 seconds
	Global.setTimeout(self,1,self,"enableOkButton")

	# restore mouse state
	Global.hideMouse(false)

	setDisplayObject()

	baseScore = statsData["key_"+str(Global.StatsKeys.SCORE)]
	$HighScore.visible = false
	updateDispalyScore(baseScore, true)
	actualVisibleScore = baseScore
	calculateRealScore()

func finishStats():
	Global.backToTitle()
