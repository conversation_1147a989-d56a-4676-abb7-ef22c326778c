extends Area2D

var Explosion = preload("res://scenes/Explosion_1.tscn")

func setCanKilled():
	canItBeDestroyed = true

func _ready():
	var _c = connect("area_entered",self,"_on_hit")
	Global.setTimeout(self,2,self,"setCanKilled")

var isDestroyed = false
var isBossAlive = true
var canItBeDestroyed = false

func finalDestroy():
	disconnect("area_entered",self,"_on_hit")
	# if isBossAlive:
	# 	Global.GameScene.displayMessage("Argh!! My Disco Ball!!", 3, 0.3)
	queue_free()

func destroy():

	if isDestroyed:
		return false
	
	isDestroyed = true

	Achievments.acquire("party_pooper")

	Global.GameScene.wasDiscoBallDestoryed = Global.DiscoBallState.DESTROYED

	var explosion = Explosion.instance()
	explosion.position = global_position;
	explosion.z_index  = z_index+1;
	explosion.scale = Vector2(2,2)
	Global.GameScene.add_child(explosion);

	for _i in range(15):
		var pos = global_position
		pos.x = pos.x+ (-20 + randi()%40)
		pos.y = pos.y+ (-20 + randi()%40)
		var ctype = Global.CrystalType.c5

		if randi()%4 == 3:
			ctype = Global.CrystalType.c10

		Global.GameScene.call_deferred("spawnCrystal",pos, ctype)


	var tween = Global.createTween(self)
	tween.interpolate_property(self,"scale",Vector2(1,1),Vector2(0,0),0.8, Tween.TRANS_EXPO, Tween.EASE_OUT)
	tween.interpolate_property(self,"modulate:a",1,0,0.2, Tween.TRANS_QUAD, Tween.EASE_OUT)
	tween.connect("tween_all_completed",self,"finalDestroy")
	tween.start()

func _on_hit(area):
    if area.has_method("getDamagePoint") && Global.GameScene.isCurrentBossReady && canItBeDestroyed:
        destroy()
