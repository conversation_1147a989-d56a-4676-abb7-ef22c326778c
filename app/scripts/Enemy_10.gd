extends "res://scripts/EnemyBase.gd"

func _ready():
	Bullet = preload("res://scenes/EnemyBullet_6.tscn")
	health = 120
	ExplosionColor = Color(1.2,1.2,1.1)
	pointValue = 5000
	species = "Minion"

var rotationTargetDeg = 0

func fireBullet(doForce = false):

	if(!doForce):
		if (Global.doThrottle(getId("",".Bullet") ,Config.GlobalEnemyBulletThrottle) || !Global.isEnemyActive(mode)):
			return false

	# don't shoot if too close to bottom
	if Global.isOffScreenBottom(getCenterPosition(), -200):
		return false

	var bullet = Bullet.instance()

	bullet.start(rotation_degrees)

	bullet.position = getCenterPosition();
	bullet.z_index  = z_index-1;
	Global.GameScene.add_child(bullet);

	Global.EnemyBulletsOnScreen += 1

func rotationHandler(delta):

	if self.mode != Global.EnemyMode.IDLE:
		rotationTargetDeg = calcRotationDegrees(delta)
	else:
		rotationTargetDeg = 0

	var rotDiff = rotationTargetDeg-rotation_degrees
	
	rotation_degrees+=(rotDiff/3)*delta*20

var moveDirection = Vector2(0,0)

func calcRotationDegrees(_delta):

	var coords = Vector2(0,0)
	var prevPos = Vector2(0,0)

	if(global_position):
		coords = global_position

	if(previousPosition):
		prevPos = previousPosition

	var dir = prevPos - coords
	moveDirection = dir

	var radAngle = atan2(dir.y, dir.x)
	var degAngle = rad2deg(radAngle)

	return degAngle+90
