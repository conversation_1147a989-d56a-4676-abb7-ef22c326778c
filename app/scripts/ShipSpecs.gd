extends Node

class ShipSpecsBase:

	var name = "Ship Mk I"
	var description = "Standard, reliable ship for beginners."

	# player speed is multiplied with this value
	var speed_multiplier = 1.0
	var speed_multiplier_limits = Vector2(0.5, 2.0)

	# bullet frequency is multiplied with this number
	var bullet_multiplier = 1.0
	var bullet_multiplier_limits = Vector2(0.5, 2.0)

	# luck is multiplied with this number
	# (0.5 - 2.5 ) let's say
	var luck_multiplier = 1.0
	var luck_multiplier_limits = Vector2(0.5, 2.5)

	# how many shields will user get when starting the game
	# ( cannot exceed max shields )
	var initial_shield_count = 0
	var initial_shield_count_limits = Vector2(0, Config.MaxShields)

	# how many lives will the user start with
	var initial_live_count = 2
	var initial_live_count_limits = Vector2(1, 5)

	# if > 0 user will get that many seconds of rapidfire after death
	var initial_super_rapidfire_duration = 0
	var initial_super_rapidfire_duration_limits = Vector2(0, 30)

	# will the user get crystal magnet permanently or not
	var permanent_crystal_magnet = false
	var permanent_crystal_magnet_limits = false

	# 1.0 - 2.0 normal to more agile homing missles
	var homing_steer_force_multiplier = 1.0
	var homing_steer_force_multiplier_limits = Vector2(0.5, 2.0)

	# max how many wings can the user have
	var max_wing_count = Config.MaxWingCount
	var max_wing_count_limits = Vector2(1, Config.MaxWingCount)

	# how many wings will the user start the game with
	# cannot be bigger than max
	var initial_wing_count = 0
	var initial_wing_count_limits = Vector2(0, Config.MaxWingCount)

	# wing type the user starts with
	# all the wings will have this type
	var initial_wing_bullet_type = Global.PlayerBulletTypes.SINGLE
	var initial_wing_bullet_type_limit = false

	# every bullet power will be multiplied with this value
	var weapon_strength_multiplier = 1.0
	var weapon_strength_multiplier_limits = Vector2(1.0, 5.0)

	# every timed powerup ( including curse ) will last *times* this value
	# it will count down slower 1000ms*multiplier_value interval
	var timed_powerup_duration_multiplier = 1.0
	var timed_powerup_duration_multiplier_limits = Vector2(0.5, 2.0)

	# bullet the user will start the game with
	var initial_weapon_type = Global.PlayerBulletTypes.SINGLE
	var initial_weapon_type_limit = false 

	# how strong the junks will be (1.0 - 10.0)
	var junk_strength_divider = 1.0
	var junk_strength_divider_limits = Vector2(1.0, 10.0)

	# each crystal value will be multiplied ( and rounded up )
	var crystal_value_multiplier = 1.0
	var crystal_value_multiplier_limits = Vector2(0.5, 2.0)

	# no downgrade after death
	var downgrade_after_death = true
	var downgrade_after_death_limit = false

	# skin
	var body_index = 0
	var wing_index = 0
	var flame_index = 0

	var color_modulation = Color(1.0,1.0,1.0,1.0)

	# min difficulty to acquire
	var min_difficulty = Global.GameDifficulty.EASY

var selectedIndex = 0

func _ready():
	selectedIndex = Profile.getData("selected_ship",0)

func wasAllUnlocaked():
	return Profile.getData("available_ships",0)>=getPresets().size()-1

func getPresets():
	# 0 - default ship

	var _default = ShipSpecsBase.new()

	_default.name = "Ship Mk I"
	_default.description = "Standard, reliable ship for beginners."

	_default.speed_multiplier = 1.0
	_default.bullet_multiplier = 1.0
	_default.luck_multiplier = 1.0
	_default.initial_shield_count = 0
	_default.initial_live_count = 2
	_default.initial_super_rapidfire_duration = 0
	_default.permanent_crystal_magnet = false
	_default.max_wing_count = Config.MaxWingCount
	_default.homing_steer_force_multiplier = 1.0
	_default.initial_wing_bullet_type = Global.PlayerBulletTypes.SINGLE
	_default.initial_wing_count = 0
	_default.weapon_strength_multiplier = 1.0
	_default.initial_weapon_type = Global.PlayerBulletTypes.SINGLE
	_default.junk_strength_divider = 1.0
	_default.crystal_value_multiplier = 1.0
	_default.downgrade_after_death = true

	_default.body_index = 0
	_default.wing_index = 0
	_default.flame_index = 0

	_default.min_difficulty = Global.GameDifficulty.EASY

	_default.color_modulation = Color(1.0,1.0,1.0,1.0)

	# 1 - fire-rate

	var _spec1 = ShipSpecsBase.new()

	_spec1.name = "Rapid 20"
	_spec1.description = "Fire-rate optimized ship with 20% faster shooting and stronger weapons."
	_spec1.speed_multiplier = 1.0
	_spec1.bullet_multiplier = 1.2
	_spec1.luck_multiplier = 1.0
	_spec1.initial_shield_count = 0
	_spec1.initial_live_count = 2
	_spec1.initial_super_rapidfire_duration = 10
	_spec1.permanent_crystal_magnet = false
	_spec1.max_wing_count = Config.MaxWingCount
	_spec1.homing_steer_force_multiplier = 1.0
	_spec1.initial_wing_bullet_type = Global.PlayerBulletTypes.SINGLE
	_spec1.initial_wing_count = 0
	_spec1.weapon_strength_multiplier = 1.2
	_spec1.initial_weapon_type = Global.PlayerBulletTypes.SINGLE
	_spec1.junk_strength_divider = 1.0
	_spec1.crystal_value_multiplier = 1.0
	_spec1.downgrade_after_death = true

	_spec1.body_index = 2
	_spec1.wing_index = 2
	_spec1.flame_index = 2

	_spec1.min_difficulty = Global.GameDifficulty.EASY

	_spec1.color_modulation = Color(0.8,0.8,1.0,1.0)

	# 2 - increased-luck

	var _spec2 = ShipSpecsBase.new()

	_spec2.name = "Lucky Silver"
	_spec2.description = "A ship with enhanced luck, increasing the chance of rare drops and power-ups."
	_spec2.speed_multiplier = 1.0
	_spec2.bullet_multiplier = 0.8
	_spec2.luck_multiplier = 2.0
	_spec2.initial_shield_count = 1
	_spec2.initial_live_count = 2
	_spec2.initial_super_rapidfire_duration = 5
	_spec2.permanent_crystal_magnet = false
	_spec2.max_wing_count = Config.MaxWingCount
	_spec2.homing_steer_force_multiplier = 1.0
	_spec2.initial_wing_bullet_type = Global.PlayerBulletTypes.SINGLE
	_spec2.initial_wing_count = 0
	_spec2.weapon_strength_multiplier = 1.0
	_spec2.initial_weapon_type = Global.PlayerBulletTypes.SINGLE
	_spec2.junk_strength_divider = 1.0
	_spec2.crystal_value_multiplier = 1.2
	_spec2.downgrade_after_death = true

	_spec2.body_index = 2
	_spec2.wing_index = 0
	_spec2.flame_index = 3

	_spec2.min_difficulty = Global.GameDifficulty.NORMAL

	_spec2.color_modulation = Color(1.0,0.8,1.0,1.0)

	# 3 - Fortress

	var _spec3 = ShipSpecsBase.new()

	_spec3.name = "Fortress"
	_spec3.description = "A tanky ship with extra shield and durability, perfect for soaking up damage. Keeps upgrades after hit."
	_spec3.speed_multiplier = 1.0
	_spec3.bullet_multiplier = 1.0
	_spec3.luck_multiplier = 1.0
	_spec3.initial_shield_count = 1
	_spec3.initial_live_count = 2
	_spec3.initial_super_rapidfire_duration = 10
	_spec3.permanent_crystal_magnet = false
	_spec3.max_wing_count = Config.MaxWingCount
	_spec3.homing_steer_force_multiplier = 1.0
	_spec3.initial_wing_bullet_type = Global.PlayerBulletTypes.SINGLE
	_spec3.initial_wing_count = 0
	_spec3.weapon_strength_multiplier = 1.2
	_spec3.initial_weapon_type = Global.PlayerBulletTypes.SINGLE
	_spec3.junk_strength_divider = 2.0
	_spec3.crystal_value_multiplier = 1.0
	_spec3.downgrade_after_death = false

	_spec3.body_index = 1
	_spec3.wing_index = 1
	_spec3.flame_index = 1

	_spec3.min_difficulty = Global.GameDifficulty.NORMAL

	_spec3.color_modulation = Color(1.0,1.1,1.0,1.0)

	# 4 - G.O.D.

	var _spec4 = ShipSpecsBase.new()

	_spec4.name = "G.O.D."
	_spec4.description = "Galactic Overdrive Destroyer! Unparalleled power, a pinnacle of engineering. Unmatched firepower ,near-invincible defenses, this almighty vessel dominates the battlefield!"
	_spec4.speed_multiplier = 1.5
	_spec4.bullet_multiplier = 2.0
	_spec4.luck_multiplier = 0.5
	_spec4.initial_shield_count = 0
	_spec4.initial_live_count = 2
	_spec4.initial_super_rapidfire_duration = 20
	_spec4.permanent_crystal_magnet = true
	_spec4.max_wing_count = Config.MaxWingCount+2
	_spec4.homing_steer_force_multiplier = 2.0
	_spec4.initial_wing_bullet_type = Global.PlayerBulletTypes.SINGLE
	_spec4.initial_wing_count = 1
	_spec4.weapon_strength_multiplier = 1.2
	_spec4.initial_weapon_type = Global.PlayerBulletTypes.SINGLE
	_spec4.junk_strength_divider = 1.5
	_spec4.crystal_value_multiplier = 1.0
	_spec4.downgrade_after_death = false

	_spec4.body_index = 0
	_spec4.wing_index = 1
	_spec4.flame_index = 3

	_spec4.min_difficulty = Global.GameDifficulty.HARD

	_spec4.color_modulation = Color(1.3,0.7,0.7,1.2)

	# 5 - Chuck Norris

	var _spec5 = ShipSpecsBase.new()

	_spec5.name = "C-N0rr15"
	_spec5.description = "Unstoppable force of pure destruction. This ship doesn’t dodge bullets, bullets dodge IT."
	_spec5.speed_multiplier = 1.5
	_spec5.bullet_multiplier = 2.0
	_spec5.luck_multiplier = 1.2
	_spec5.initial_shield_count = 2
	_spec5.initial_live_count = 2
	_spec5.initial_super_rapidfire_duration = 30
	_spec5.permanent_crystal_magnet = true
	_spec5.max_wing_count = Config.MaxWingCount+3
	_spec5.homing_steer_force_multiplier = 2.0
	_spec5.initial_wing_bullet_type = Global.PlayerBulletTypes.SINGLE
	_spec5.initial_wing_count = 1
	_spec5.weapon_strength_multiplier = 1.2
	_spec5.initial_weapon_type = Global.PlayerBulletTypes.STRONG_SINGLE
	_spec5.junk_strength_divider = 2.0
	_spec5.crystal_value_multiplier = 1.0
	_spec5.downgrade_after_death = false

	_spec5.body_index = 0
	_spec5.wing_index = 0
	_spec5.flame_index = 2

	_spec5.min_difficulty = Global.GameDifficulty.EXTREME

	_spec5.color_modulation = Color(0.5,0.5,1.1,1.3)


	# @todo: add more

	var presets = [
		_default,
		_spec1,
		_spec2,
		_spec3,
		_spec4,
		_spec5
	]

	return presets

func presetCount():
	return getPresets().size()


func preset(index)->ShipSpecsBase:

	var presets = getPresets()
	
	if(index<0):
		index = 0

	if(index>=presets.size()):
		index = presets.size()-1

	return presets[index]

func getSpecs() -> ShipSpecsBase:

	return self.preset(self.selectedIndex)
