extends Node

var wasPausedManually = false
var is_paused = false;

func _notification(what):

    match(what):
        MainLoop.NOTIFICATION_WM_FOCUS_IN:
            if not wasPausedManually:
                wasPausedManually = false
                resume_game()
        MainLoop.NOTIFICATION_WM_FOCUS_OUT:
            if not is_paused:
                wasPausedManually = false
            pause_game()
        MainLoop.NOTIFICATION_WM_GO_BACK_REQUEST:
            # Android back button pressed
            pass
        MainLoop.NOTIFICATION_WM_QUIT_REQUEST:
            get_tree().quit()

func pause_game():
    if is_paused:
        return
    is_paused = true
    get_tree().paused = true

func resume_game():
    if !is_paused:
        return
    is_paused = false
    get_tree().paused = false
