extends Node2D

func applySpecs(specIndex=-1):

	specIndex = int(specIndex)

	if(specIndex<=0):
		specIndex = 0
	
	var spc = ShipSpecs.getPresets()[specIndex]

	# text
	$Name.text = spc.name
	$Description.text = spc.description

	# set specs
	$Ship.applySpecs(spc)

	# unavailable?

	var isAvailable = !(Profile.getData("available_ships",0)<currentIndex)

	$Unavailable.visible = !isAvailable
	$Note.visible = !isAvailable
	$Note.text = "Beat [ " + Global.getDifficultyString(spc.min_difficulty) + " ] mode and have previous ships to unlock!"
	$Select.disabled = !isAvailable

	if(isAvailable):
		$Ship.modulate.a = 1.0
	else:
		$Ship.modulate.a = 0.3


var currentIndex = 0

func _ready():
	# currentIndex = Profile.getData("selected_ship",0)
	currentIndex = 0
	applySpecs(currentIndex)

	var _pb = $Previous.connect("pressed", self, "_on_previous_pressed")
	var _nb = $Next.connect("pressed", self, "_on_next_pressed")
	var _sb = $Select.connect("pressed", self, "_on_select_pressed")
	var _cb = $Close.connect("pressed", self, "_on_close_pressed")

	$Next.grab_focus()


func _on_previous_pressed():
	currentIndex=currentIndex-1

	if(currentIndex<0):
		currentIndex = ShipSpecs.presetCount()-1
	
	applySpecs(currentIndex)

func _on_next_pressed():
	currentIndex=currentIndex+1

	if(currentIndex>ShipSpecs.presetCount()-1):
		currentIndex = 0
	
	applySpecs(currentIndex)

func _on_select_pressed():
	ShipSpecs.selectedIndex = currentIndex
	Profile.setData("selected_ship", currentIndex)
	Profile.save()
	queue_free()

func _input(event):
	if event.is_action_pressed("button_back"):
		_on_close_pressed()

func _on_close_pressed():
	get_parent().get_node("Menu").grab_focus()
	queue_free()
