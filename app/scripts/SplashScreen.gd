extends Node2D

func _ready():
	pass

func _input(ev):
	if ev is InputEventKey and ev.scancode == KEY_SPACE:
		closeMe()

func modWorld():

	if(out_started):
		return

	var env = $WorldEnvironment.environment
	var randval = 0.5+(randf()*1)
	var randval2 = 0.5+(randf()*1)
	var randdelay = randf()*0.2

	var tween = Global.createTween(self)
	tween.interpolate_property(env, "adjustment_brightness", 1 , randval, randdelay,Tween.TRANS_LINEAR, Tween.EASE_OUT);
	tween.start()

	var tween2 = Global.createTween(self)
	tween2.interpolate_property(env, "adjustment_brightness",randval2,1, randdelay,Tween.TRANS_LINEAR, Tween.EASE_OUT, 0.2);
	tween2.start()

var blink = false

func blinkf():
	blink = not blink
	if(blink):
		$Title.text = 'DeadFly.Games'
	else:
		$Title.text = 'DeadFly.Games_'

var time = 0
var out_started = false

func closeMe():
	var _title_screen = get_tree().change_scene(Config.StartScene)

func _process(delta):

	if(Input.is_action_just_pressed("button_back") || Input.is_action_just_pressed("button_fire")):
		closeMe()

	time = time + delta

	if (time>4 && !out_started):
		out_started = true
		var env = $WorldEnvironment.environment
		var tween = Global.createTween(self)
		tween.interpolate_property(env, "adjustment_brightness", 1 , 0, 0.4,Tween.TRANS_LINEAR, Tween.EASE_OUT);
		tween.start()

	if (time>5):
		self.closeMe()

	if !Global.doThrottle("splash_screen_animation", 500):
		modWorld()

	if !Global.doThrottle("splash_screen_animation_blink", 200):
		blinkf()
