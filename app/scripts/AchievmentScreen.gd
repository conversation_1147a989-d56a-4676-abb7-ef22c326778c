extends Control

func _ready():

	get_node("Demo").visible = Config.Env.IsDemo

	var id = 0
	$ItemList.clear()
	for item in Achievments.getItems():
		var prefix = "( ) " if !item.acquired else "(X) "

		$ItemList.add_item( prefix+item.name+" :  ", null, false)
		$ItemList.set_item_disabled(id, !item.acquired)

		id+=1

		if (item.private && !item.acquired):
			$ItemList.add_item( "???", null, false)
		else:
			$ItemList.add_item( item.description, null, false)

		$ItemList.set_item_disabled(id, !item.acquired)

		id+=1
		$CloseButton.grab_focus()
	
	var _ch = $CloseButton.connect("pressed", self, "_on_CloseButton_pressed")

func _input(event):
	if event.is_action_pressed("button_back"):
		_on_CloseButton_pressed()

	if event.is_action_pressed("button_down"):
		var scrollBar = $ItemList.get_v_scroll()  # Ensure correct path
		scrollBar.value += 100

	if event.is_action_pressed("button_up"):
		var scrollBar = $ItemList.get_v_scroll()  # Ensure correct path
		scrollBar.value -= 100


func _on_CloseButton_pressed():
	queue_free()
