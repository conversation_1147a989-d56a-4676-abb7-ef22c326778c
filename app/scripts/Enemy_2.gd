extends "res://scripts/EnemyBase.gd"

# enemy health
func _ready():
	health = 50
	ExplosionColor = Color(1,0.2,0.2)
	pointValue = 2000
	species = "RedDancer"

func selfCoordinatedBullet():

	if Global.doThrottle(getId("",".BulletSelfFire"), Config.GlobalEnemyBulletThrottle):
		return false

	if(abs(Global.getPlayerPosition().x - self.position.x)<50):
		if Global.isChance(randi(), 50):
			fireBullet()


func _process(_delta):
	if Global.GameScene.isPlayerReady():
		selfCoordinatedBullet()
